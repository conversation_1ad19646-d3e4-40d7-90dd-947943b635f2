.idea
.vscode
.cache
.clang-format

*.pyc
*.tar.gz
CMakeCache.txt
CMakeFiles
cmake_install.cmake
Makefile
build
.local
.ycm_extra_conf.py
.clangd
compile_commands.json
install_manifest.txt
cmake-build-debug
**/build
**/CMakeCache.txt
**/CMakeFiles
**/cmake_install.cmake
**/Makefile
**/CMakeCache.txt
**/CMakeFiles
**/cmake_install.cmake
**/Makefile
**/install_manifest.txt
**/venv
**/CPPLINT.cfg
doxygen_output

debian/*
!debian/rules
!debian/control
!debian/compat
!debian/changelog

obj-x86_64-linux-gnu

TAGS
**/TAGS

*bak
**/*bak
