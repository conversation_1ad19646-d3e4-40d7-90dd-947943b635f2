cmake_minimum_required(VERSION 3.15.1)

set(PACKAGE_VERSION 3.1.40)
set(CMAKE_CXX_COMPILER "/usr/bin/g++")
set(CMAKE_CXX_FLAGS "-g -std=c++14")
set(RELEASE_VERSION "3.0")
set(LOG_TAG "glog")
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
project(trunk_pnd VERSION ${PACKAGE_VERSION} LANGUAGES CXX)

option(BUILD_SHARED_LIBS "Build shared libraries" ON)
option(INSTALL_OPT_TRUNK "Install on trunk path" ON)
option(INSTALL_HEADERS "Install lib headers" ON)
option(JIAN "use trunk jian log" ON)
option(PLOT "use matplotlibcpp plot " OFF)

if(INSTALL_OPT_TRUNK)
  set(CMAKE_INSTALL_PREFIX "/opt/trunk_master")
endif()

include(cmake/trunk_utils.cmake)
include(CMakePackageConfigHelpers)
include(CMakeDependentOption)
include(CheckCXXCompilerFlag)
include(GNUInstallDirs)
include(CTest)

set(TARGET_NAME ${PROJECT_NAME})
set(PROJECT_DESCRIPTION "trunk pnd")

if(NOT PACKAGE_HUB)
    trunk_make_package(${TARGET_NAME} ${PROJECT_VERSION} ${PROJECT_DESCRIPTION})
endif()

file(GLOB_RECURSE sources ${CMAKE_CURRENT_SOURCE_DIR}/src/*.cc)
file(GLOB_RECURSE headers ${CMAKE_CURRENT_SOURCE_DIR}/src/*.h)

find_package(Eigen3 REQUIRED)
find_package(OsqpEigen REQUIRED)
find_package(Boost REQUIRED COMPONENTS system thread)
find_package(PkgConfig REQUIRED)
find_package(absl REQUIRED)
pkg_check_modules(TRUNK_COMMON REQUIRED trunk_common>=3.6.6)
pkg_check_modules(OMPL REQUIRED ompl>=1.3.3)
# check jian
if(JIAN)
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -s")
  set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -s")
  pkg_check_modules(JIAN REQUIRED jian>=1.0.16)
  add_definitions(-DENABLE_JIAN)
  set(LOG_TAG "jian")
endif()

if (NOT PLOT)
  file(GLOB_RECURSE PLOT_HEAD src/plot/*.h)
  file(GLOB_RECURSE PLOT_SOURCE src/plot/*.cc)
  list(REMOVE_ITEM sources ${PLOT_SOURCE})
  list(REMOVE_ITEM headers ${PLOT_HEAD})
endif()

include_directories(${CMAKE_CURRENT_SOURCE_DIR}/src)

add_library(${TARGET_NAME} ${headers} ${sources})

if(PLOT)
   find_package(Python3 COMPONENTS Interpreter Development NumPy REQUIRED)
   target_include_directories(${TARGET_NAME} PUBLIC
      ${Python3_INCLUDE_DIRS}
   )
   target_link_directories(${TARGET_NAME} PUBLIC
      ${Python3_LIBRARY_DIRS}
   )
   target_link_libraries(${TARGET_NAME}
      ${Python3_LIBRARIES}
   )
   message(STATUS "Plot feature is enabled, adding plot sources.")
endif()

target_include_directories(${TARGET_NAME} PUBLIC
  ${TRUNK_COMMON_INCLUDE_DIRS}
  ${Boost_INCLUDE_DIRS}
  ${IPOPT_INCLUDE_DIRS}
  ${EIGEN3_INCLUDE_DIRS}
  ${OMPL_INCLUDE_DIRS}
  )

target_link_directories(${TARGET_NAME} PUBLIC
  ${TRUNK_COMMON_LIBRARY_DIRS}
  ${OMPL_LIBRARY_DIRS}
  ${IPOPT_LIBRARY_DIRS}
  ${Python3_LIBRARY_DIRS}
  )

target_link_libraries(${TARGET_NAME}
  ${TRUNK_COMMON_LIBRARIES}
  ${OMPL_LIBRARIES}
  ${IPOPT_LIBRARIES}
  ${Python3_LIBRARIES}
  OsqpEigen::OsqpEigen
  qpOASES
  absl::str_format
  absl::strings
  absl::flat_hash_map
  absl::time
  )

install(TARGETS ${TARGET_NAME}
  EXPORT ${TARGET_NAME}
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
  )

install(DIRECTORY src/
  DESTINATION include/trunk/pnd
  FILES_MATCHING
  PATTERN "*.hpp"
  PATTERN "*.h"
  )

set_target_properties(${TARGET_NAME} PROPERTIES
  VERSION "${PROJECT_VERSION}"
  SOVERSION "${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}"
  PROJECT_LABEL ${TARGET_NAME}
  DEBUG_POSTFIX "${CMAKE_DEBUG_POSTFIX}"
  POSITION_INDEPENDENT_CODE 1
  )

configure_file(cmake/config.cmake.in  "${PROJECT_BINARY_DIR}/${TARGET_NAME}-config-install.cmake" @ONLY)
configure_file(cmake/version.cmake.in "${PROJECT_BINARY_DIR}/${TARGET_NAME}-config-version.cmake" @ONLY)

install (
  FILES "${PROJECT_BINARY_DIR}/${TARGET_NAME}-config-install.cmake"
  RENAME ${TARGET_NAME}-config.cmake
  DESTINATION ${CMAKE_INSTALL_DATADIR}/cmake/${TARGET_NAME}
  )
install (
  FILES "${PROJECT_BINARY_DIR}/${TARGET_NAME}-config-version.cmake"
  DESTINATION ${CMAKE_INSTALL_DATADIR}/cmake/${TARGET_NAME}
  )
install (
  EXPORT ${TARGET_NAME}
  NAMESPACE ${TARGET_NAME}::
  DESTINATION ${CMAKE_INSTALL_DATADIR}/cmake/${TARGET_NAME}
  )
install (
  EXPORT ${TARGET_NAME}
  FILE ${TARGET_NAME}-nonamespace-targets.cmake
  DESTINATION ${CMAKE_INSTALL_DATADIR}/cmake/${TARGET_NAME}
  )

configure_file(cmake/package.pc.in ${TARGET_NAME}.pc @ONLY)

install(
  FILES "${PROJECT_BINARY_DIR}/${TARGET_NAME}.pc"
  DESTINATION "${CMAKE_INSTALL_DATADIR}/pkgconfig"
  )
