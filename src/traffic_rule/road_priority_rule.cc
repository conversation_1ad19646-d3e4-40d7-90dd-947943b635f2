// Copyright 2024, trunk Inc. All rights reserved

#include "road_priority_rule.h"

#include "util/util.h"

namespace trunk {
namespace pnd {
namespace rule {

REGISTER(trunk::pnd::rule, Rule, RoadPriority);

tpnc::Status RoadPriorityRule::ApplyRule(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  if (reference_line_info == nullptr || frame == nullptr) {
    return tpnc::Status::ERROR("reference_line_info is nullptr");
  }
  if (!reference_line_info->is_drivable().first) {
    return tpnc::Status::OK();
  }
  if (reference_line_info->target_info()) {
    const auto& lane_id = reference_line_info->target_info()->lane_id;
    if (frame->target_range() &&
        frame->target_range()->lane_id2line_idx().count(lane_id)) {
      int idx = frame->target_range()->lane_id2line_idx().at(lane_id);
      // TRUNK_LOG_INFO << "lane_id: " << lane_id << " idx: " << idx;
      reference_line_info->set_road_priority(idx);
    }
    if (reference_line_info->lane_id2info_idx().count(lane_id)) {
    }
  }
  if (frame->current_reference_line_info() != nullptr) {
    frame->current_reference_line_info()->set_road_priority(0);
  }
  return tpnc::Status::OK();
}

}  // namespace rule
}  // namespace pnd
}  // namespace trunk
