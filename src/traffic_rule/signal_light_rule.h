// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include "rule.h"

namespace trunk {
namespace pnd {
namespace rule {

class SignalLightRule : public Rule {
  enum class TrafficLightColor {
    UNKNOWN = 0,
    RED = 1,
    YELLOW = 2,
    GREEN = 3,
    BLACK = 4
  };

  enum class TrafficLightTurnType {
    UNKNOWN = 0,
    LEFT_TURN = 1,
    RIGHT_TURN = 2,
    STRAIGHT = 3,
    UTURN = 4,
    LEFT_TURN_AND_STRAIGHT = 5,
    RIGHT_TURN_AND_STRAIGHT = 6,
    LEFT_TURN_AND_UTURN = 7,
    LEFT_TURN_RIGHT_TURN_AND_STRAIGHT = 8
  };

 public:
  SignalLightRule() = default;

  virtual ~SignalLightRule() = default;

  tpnc::Status ApplyRule(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info) override;
 
  private:
   void GetJunctionData(const std::shared_ptr<port::Frame>& frame,
                        int& turn_type, double& distance_to_junction);

   bool FindTrafficLight(
       const std::vector<std::pair<int, int>>& traffic_light_status,
       int junction_turn_type, int& light_status);
};

}  // namespace rule
}  // namespace pnd
}  // namespace trunk
