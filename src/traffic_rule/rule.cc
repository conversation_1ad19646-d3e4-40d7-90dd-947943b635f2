// Copyright 2023, trunk Inc. All rights reserved

#include "rule.h"

#include <absl/time/clock.h>
#include <absl/time/time.h>
namespace trunk {
namespace pnd {
namespace rule {

tpnc::Status Rule::ApplyRuleAndCalRunTime(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  const auto start_run_time = absl::Now();
  tpnc::Status ret = ApplyRule(frame, reference_line_info);
  run_time_ = absl::ToDoubleMilliseconds(absl::Now() - start_run_time);
  reference_line_info->AddRunTime(run_time_);
  return ret;
}

}  // namespace rule
}  // namespace pnd
}  // namespace trunk
