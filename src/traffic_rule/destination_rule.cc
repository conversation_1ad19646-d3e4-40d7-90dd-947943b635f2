// Copyright 2024, trunk Inc. All rights reserved

#include "destination_rule.h"

#include "util/util.h"

namespace trunk {
namespace pnd {
namespace rule {

REGISTER(trunk::pnd::rule, Rule, Destination);

tpnc::Status DestinationRule::ApplyRule(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  if (reference_line_info == nullptr) {
    return tpnc::Status::ERROR("reference_line_info is nullptr");
  }
  const auto* model = tpnc::Singleton<tmodel::Truck>::GetInstance();
  if (model == nullptr) {
    return tpnc::Status::ERROR("model is nullptr");
  }
  const auto sl = tutil::TransformFrenet::TransformToFrenet(
      reference_line_info->reference_line(), frame->destination_point());
  constexpr double kDistanceThres = 180.0;  // m
  TRUNK_LOG_DEBUG << "destination : " << frame->destination_point()
                  << " s: " << sl.s() << " l: " << sl.l();
  // sl 会和车体坐标系下的终点有一定的偏差
  if (sl.s() > -1.0 && sl.s() < kDistanceThres &&
      std::fabs(sl.l()) < util::GetRoadWidth(reference_line_info, sl.s())) {
    const auto& qp_spline_config =
        tpnc::Singleton<tpnc::Config>::GetInstance()
            ->GetConfig<QpSpeedOptimizerParam>("qp_speed_optimizer")
            .qp_spline_config();
    auto* m_obs = reference_line_info->AddVirtualStaticObstacle(
        util::VirtualObstacleIdGenerator(),
        sl.s() + qp_spline_config.follow_drag_distance() + 1,
        sl.s() + model->geometry().length() +
            qp_spline_config.follow_drag_distance() + 1);
    if (m_obs == nullptr) {
      return tpnc::Status::ERROR("fail to add destination virtual obstacle");
    }
    m_obs->set_stop_reason(port::StopReason::kDestination);
  }
  return tpnc::Status::OK();
}

}  // namespace rule
}  // namespace pnd
}  // namespace trunk
