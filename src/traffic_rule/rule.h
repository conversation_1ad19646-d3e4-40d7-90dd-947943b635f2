// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include "port/frame.h"

namespace trunk {
namespace pnd {
namespace rule {

class Rule {
 public:
  Rule() = default;

  virtual ~Rule() = default;

  virtual tpnc::Status ApplyRule(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info) = 0;

  const std::string& GetName() const { return name_; }

  tpnc::Status ApplyRuleAndCalRunTime(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info);

 protected:
  std::string name_;
  MEMBER_BASIC_TYPE(double, run_time, 0.0);
};

}  // namespace rule
}  // namespace pnd
}  // namespace trunk
