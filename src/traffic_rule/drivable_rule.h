// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include "rule.h"
#include "util/util.h"

namespace trunk {
namespace pnd {
namespace rule {

class DrivableRule : public Rule {
 public:
  DrivableRule() = default;

  virtual ~DrivableRule() = default;

  tpnc::Status ApplyRule(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info) override;
};

}  // namespace rule
}  // namespace pnd
}  // namespace trunk
