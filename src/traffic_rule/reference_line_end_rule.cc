// Copyright 2024, trunk Inc. All rights reserved

#include "reference_line_end_rule.h"

#include "util/util.h"

namespace trunk {
namespace pnd {
namespace rule {

REGISTER(trunk::pnd::rule, Rule, ReferenceLineEnd);

tpnc::Status ReferenceLineEndRule::ApplyRule(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  if (reference_line_info == nullptr) {
    return tpnc::Status::ERROR("reference_line_info is nullptr");
  }
  if (reference_line_info->origin_reference_line().empty()) {
    return tpnc::Status::ERROR("reference line empty");
  }
  const auto* model = tpnc::Singleton<tmodel::Truck>::GetInstance();
  if (model == nullptr) {
    return tpnc::Status::ERROR("model is nullptr");
  }
  const double back_s = reference_line_info->origin_reference_line().back().s();
  const auto& config =
      tpnc::Singleton<tpnc::Config>::GetInstance()->GetConfig<SysParam>("sys");

  bool is_tip = tutil::TwoPointsDistance(
                    reference_line_info->lane_boundaries().first.back(),
                    reference_line_info->lane_boundaries().second.back()) < 0.1;
  // 地图一次数据长度，前向300m
  if (back_s < 300.0 - 15.0) {
    reference_line_info->AddVirtualStaticObstacle(
        util::VirtualObstacleIdGenerator(),
        back_s - model->geometry().length() - (is_tip ? 50.0 : 0.0), back_s);
  }

  return tpnc::Status::OK();
}

}  // namespace rule
}  // namespace pnd
}  // namespace trunk
