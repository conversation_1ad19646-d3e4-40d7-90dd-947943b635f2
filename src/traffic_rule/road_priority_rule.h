// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include "param/sys_param.h"
#include "rule.h"

namespace trunk {
namespace pnd {
namespace rule {

class RoadPriorityRule : public Rule {
 public:
  RoadPriorityRule() = default;

  virtual ~RoadPriorityRule() = default;

  tpnc::Status ApplyRule(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info) override;
};

}  // namespace rule
}  // namespace pnd
}  // namespace trunk
