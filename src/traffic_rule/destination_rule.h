// Copyright 2024, trunk Inc. All rights reserved

#pragma once

#include "param/qp_st_speed_param.h"
#include "rule.h"

namespace trunk {
namespace pnd {
namespace rule {

class DestinationRule : public Rule {
 public:
  DestinationRule() = default;

  virtual ~DestinationRule() = default;

  tpnc::Status ApplyRule(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info) override;
};

}  // namespace rule
}  // namespace pnd
}  // namespace trunk
