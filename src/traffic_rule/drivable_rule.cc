// Copyright 2023, trunk Inc. All rights reserved

#include "drivable_rule.h"

#include "param/sys_param.h"
#include "port/lane_type.h"
#include "util/key.h"

namespace trunk {
namespace pnd {
namespace rule {

REGISTER(trunk::pnd::rule, Rule, Drivable);

tpnc::Status DrivableRule::ApplyRule(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  if (reference_line_info == nullptr) {
    return tpnc::Status::ERROR("reference_line_info is nullptr");
  }

  // 接管时，非当前车道禁用
  if (frame->env().vehicle().driver_status() ||
      frame->last_drived_reference_line_info() == nullptr) {
    if (reference_line_info == frame->current_reference_line_info()) {
      reference_line_info->set_is_drivable({true, "manual driving"});
    } else {
      reference_line_info->set_is_drivable({false, "manual driving"});
    }
    return tpnc::Status::OK();
  }
  // TODO: 进task前设置不能走的车道, 如压实线, 最左车道等

  // 高速场景中车道大于2时，不能行驶最左侧的车道

  if (!reference_line_info->target_info()) {
    reference_line_info->set_is_drivable({false, "have not target_info"});
    return tpnc::Status::OK();
  }
  bool passive = false;
  std::string reason =
      "The current position does not support overtaking and lane changing";
  // 车道为以下场景时，不主动变道
  const auto& target_info = *reference_line_info->target_info();
  passive = target_info.type == port::HighwayRegionType::EMERGENCY ||
            target_info.type == port::HighwayRegionType::BRANCH ||
            target_info.IsAuxiliaryLane();

  if (frame->current_reference_line_info() &&
      frame->current_reference_line_info()->target_info() &&
      (target_info.l_lane_id !=
           frame->current_reference_line_info()->target_info()->lane_id &&
       target_info.r_lane_id !=
           frame->current_reference_line_info()->target_info()->lane_id &&
       frame->current_reference_line_info() != reference_line_info)) {
    // 非当前车道的左右车道都禁掉
    passive = true;
  }

  // 对第一条车道进行特殊处理
  if (frame->target_range()->lane_id2line_idx().size() > 3 &&
      target_info.l_lane_id == "") {
    passive = true;
    reason = "Cannot enter the first lane on the left";
  }
  if (frame->target_range()->lane_id2line_idx().size() == 3) {
    if (reference_line_info->target_info() != nullptr &&
        (reference_line_info->target_info()->type ==
             port::HighwayRegionType::EMERGENCY ||
         reference_line_info->target_info()->type ==
             port::HighwayRegionType::BRANCH ||
         reference_line_info->target_info()->IsAuxiliaryLane())) {
      // 有应急车道
      passive = true;
      reason = "Cannot enter the emergency lane";
    }
  }
  // 当前所处位置车道边界的类型
  std::string cur_target_id = "";
  for (const auto& range : frame->ranges()) {
    if (range.InRange(frame->init_point())) {
      cur_target_id = range.target_lane_id();
      break;
    }
  }
  if (cur_target_id == "") {
    cur_target_id = frame->target_lane_id();
  }
  bool solid = false;
  auto cur_drived_line = frame->last_drived_reference_line_info_mapping();
  if (cur_drived_line && cur_drived_line != reference_line_info) {
    auto cur_tar_info = cur_drived_line->target_info();
    if (cur_tar_info) {
      if (cur_tar_info->l_lane_id == target_info.lane_id) {
        if (cur_tar_info->l_boundary_type == port::BoundaryType::SOLID) {
          passive = true;
          solid = true;
          reason = "solid boundary";
        }
      } else if (cur_tar_info->r_lane_id == target_info.lane_id) {
        if (cur_tar_info->r_boundary_type == port::BoundaryType::SOLID) {
          passive = true;
          solid = true;
          reason = "solid boundary";
        }
      }
    }
    if (cur_target_id != "") {
      const auto& cur_range = frame->ranges()[frame->lane_id2range_idx().at(
          cur_target_id)];  // 当前车辆所在的range
      auto cur_info = reference_line_info->target_info();
      for (auto& ii : cur_range.lane_id2line_idx()) {
        if (&frame->reference_lines_info()[ii.second] == reference_line_info) {
          cur_info = frame->GetInfoById(ii.first);
          break;
        }
      }
      if (cur_drived_line) {
        auto cur_target_info = cur_drived_line->target_info();
        for (auto& ii : cur_range.lane_id2line_idx()) {
          if (&frame->reference_lines_info()[ii.second] == cur_drived_line) {
            cur_target_info = frame->GetInfoById(ii.first);
            break;
          }
        }
        if (cur_target_info && cur_info) {
          if (cur_target_info->l_lane_id == cur_info->lane_id) {
            if (cur_target_info->l_boundary_type == port::BoundaryType::SOLID) {
              passive = true;
              solid = true;
              reason = "solid boundary";
            }
          } else if (cur_target_info->r_lane_id == cur_info->lane_id) {
            if (cur_target_info->r_boundary_type == port::BoundaryType::SOLID) {
              passive = true;
              solid = true;
              reason = "solid boundary";
            }
          }
        }
      }
    }
  }
  // 路口前不允许超车变道
  for (int cur_idx = frame->lane_id2range_idx().at(cur_target_id);
       cur_idx < frame->ranges().size(); ++cur_idx) {
    for (const auto& ii : frame->ranges().at(cur_idx).lane_id2line_idx()) {
      if (frame->GetInfoById(ii.first)->type ==
              port::HighwayRegionType::JUNCTION &&
          frame->GetReferenceLineById(ii.first)->reference_line().size() >
              frame->GetInfoById(ii.first)->begin_idx) {
        double distance_to_junction = tutil::TwoPointsDistance(
            frame->GetReferenceLineById(ii.first)->reference_line().at(
                frame->GetInfoById(ii.first)->begin_idx),
            tport::Point2D(0, 0));
        if (distance_to_junction < 200.0) {
          passive = true;
          reason = "junction";
        }
        break;
      }
    }
  }

  if (target_info.r_lane_id == "" &&
      target_info.type == port::HighwayRegionType::UNKNOWN) {
    passive = true;
    reason = "The road on the far right is unknown";
  }

  // 距离匝道2500米以内，不能主动变道
  if ((static_cast<port::TurnType>(
           frame->traffic_info().ramp_turn_info().first) ==
           port::TurnType::INLANE_TO_RAMP ||
       static_cast<port::TurnType>(
           frame->traffic_info().ramp_turn_info().first) ==
           port::TurnType::INLANE_RIGHT_CONFLUENCE) &&
      frame->traffic_info().ramp_turn_info().second < 2500) {
    passive = true;
    reason = "Approaching the ramp!!!";
  }

  // 三変二
  if (static_cast<port::TurnType>(
          frame->traffic_info().ramp_turn_info().first) ==
          port::TurnType::INLANE_LEFT_CONFLUENCE &&
      frame->traffic_info().ramp_turn_info().second < 1000) {
    passive = true;
    reason = "Approaching the confluence!!!";
  }

  if (reference_line_info != nullptr &&
      !reference_line_info->reference_line().empty()) {
    // 非目标车道下，参考线长度过短，不能行驶
    if (reference_line_info->origin_reference_line().back().s() < 250.0 ||
        std::fabs(reference_line_info->origin_reference_line().back().s() -
                  reference_line_info->reference_line().front().s()) < 250.0) {
      // VTI-17391 拼接过static_map后，此方法不能用
      passive = true;
      reason = "The reference line is too short.";
    }
    if (!passive &&
        std::abs(reference_line_info->reference_line().front().y()) >
            util::kHighwayLaneWidth + 0.5) {
      // VTI-17391,默认车道距离自车过远就为static_map
      passive = true;
      reason = "Roads using static_map.";
    }
    // 合流车道处理
    const double& cur_back_s = reference_line_info->reference_line().back().s();
    const auto& width = reference_line_info->GetLaneWidth(cur_back_s);
    const double cur_back_width = width.left_width - width.right_width;
    if (cur_back_width < 0.5) {
      // 认为是合流车道
      passive = true;
      reason = "Confluence lane.";
    }
  }
  // 道路上有锥桶，车道禁止通行
  for (const auto& obstacle : reference_line_info->obstacles().Items()) {
    if (obstacle->IsStaticType() &&
        obstacle->sl_boundary().max_l() * obstacle->sl_boundary().min_l() < 0) {
      // 施工区域
      passive = true;
      reason = "Construction area";
      break;
    }
  }
  if (passive) {
    reference_line_info->set_is_drivable({false, reason});
  }

  assert(frame->current_reference_line_info() != nullptr);
  assert(frame->target_reference_line_info() != nullptr);

  // 匝道尽头、汇入主路和三变二的场景
  if (frame->current_reference_line_info()->is_drivable().first == false) {
    // 当前车道被禁用时，说明此时要回到目标车道，并且不考虑超车的代价
    frame->target_reference_line_info()->set_cal_overtake_cost(false);
    frame->current_reference_line_info()->set_cal_overtake_cost(false);
  }

  // 将当前道路、目标车道和上一帧行驶的道路解除禁用
  frame->current_reference_line_info()->set_is_drivable(
      {true, "current reference line must be drivable"});
  if (frame->last_drived_reference_line_info_mapping()) {
    frame->last_drived_reference_line_info_mapping()->set_is_drivable(
        {true, "last driven reference line must be drivable"});
  }
  if (!solid &&
      frame->target_reference_line_info()->is_drivable().first == false) {
    frame->target_reference_line_info()->set_is_drivable(
        {true, "target reference line must be drivable"});
  }
  return tpnc::Status::OK();
}

}  // namespace rule
}  // namespace pnd
}  // namespace trunk
