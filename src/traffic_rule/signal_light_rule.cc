#include "signal_light_rule.h"

#include "util/util.h"

namespace trunk {
namespace pnd {
namespace rule {
REGISTER(trunk::pnd::rule, Rule, SignalLight);

tpnc::Status SignalLightRule::ApplyRule(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  // 红绿灯的让行rule
  if (frame->traffic_info().traffic_light_status().empty()) {
    return tpnc::Status::OK();
  }
  int target_lane_turn_type = 0;
  double distance_to_junction = 0.0;
  GetJunctionData(frame, target_lane_turn_type, distance_to_junction);

  if (distance_to_junction > 40.0 && distance_to_junction < 0.0 &&
      target_lane_turn_type == -1) {
    return tpnc::Status::OK();
  }

  int traffic_light_status;
  if (!FindTrafficLight(frame->traffic_info().traffic_light_status(),
                        target_lane_turn_type, traffic_light_status)) {
    return tpnc::Status::OK();
  }
  if (static_cast<TrafficLightColor>(traffic_light_status) !=
          TrafficLightColor::GREEN &&
      static_cast<TrafficLightColor>(traffic_light_status) !=
          TrafficLightColor::BLACK) {
    // TODO:添加虚拟障碍物
    auto* m_obs = reference_line_info->AddVirtualStaticObstacle(
        util::VirtualObstacleIdGenerator(), 40.0 - 5.0, 40.0);
    if (m_obs == nullptr) {
      return tpnc::Status::ERROR("fail to add destination virtual obstacle");
    }
    m_obs->set_stop_reason(port::StopReason::kRedLight);
  }
  return tpnc::Status::OK();
}

void SignalLightRule::GetJunctionData(const std::shared_ptr<port::Frame>& frame,
                                      int& turn_type,
                                      double& distance_to_junction) {
  // distance_to_junction为车身最前处于路口的距离
  turn_type = -1;
  distance_to_junction = std::numeric_limits<double>::min();

  if (frame->target_reference_line_info()->target_info() &&
      frame->target_reference_line_info()->target_info()->type ==
          port::HighwayRegionType::JUNCTION) {
    // 路口处不执行该判断
    return;
  }

  const auto* model = tpnc::Singleton<tmodel::Truck>::GetInstance();
  if (model == nullptr) {
    return;
  }

  for (int cur_idx = frame->lane_id2range_idx().at(frame->target_lane_id());
       cur_idx < frame->ranges().size(); ++cur_idx) {
    for (const auto& ii : frame->ranges().at(cur_idx).lane_id2line_idx()) {
      if (frame->GetInfoById(ii.first)->type ==
          port::HighwayRegionType::JUNCTION) {
        turn_type = static_cast<int>(frame->GetInfoById(ii.first)->type);
        distance_to_junction =
            tutil::TwoPointsDistance(
                frame->GetReferenceLineById(ii.first)->reference_line().at(
                    frame->GetInfoById(ii.first)->begin_idx),
                tport::Point2D(0, 0)) -
            model->geometry().length();
        return;
      }
    }
  }
}

bool SignalLightRule::FindTrafficLight(
    const std::vector<std::pair<int, int>>& traffic_light_status,
    int junction_turn_type, int& light_status) {
  for (const auto& traffic_light : traffic_light_status) {
    if (static_cast<TrafficLightTurnType>(traffic_light.second) ==
        TrafficLightTurnType::LEFT_TURN_RIGHT_TURN_AND_STRAIGHT) {
      light_status = traffic_light.first;
      return junction_turn_type != 2;
    }

    if (junction_turn_type == 0 &&
        (static_cast<TrafficLightTurnType>(traffic_light.second) ==
             TrafficLightTurnType::STRAIGHT ||
         static_cast<TrafficLightTurnType>(traffic_light.second) ==
             TrafficLightTurnType::LEFT_TURN_AND_STRAIGHT ||
         static_cast<TrafficLightTurnType>(traffic_light.second) ==
             TrafficLightTurnType::RIGHT_TURN_AND_STRAIGHT)) {
      light_status = traffic_light.first;
      return true;
    } else if (junction_turn_type == 1 &&
               (static_cast<TrafficLightTurnType>(traffic_light.second) ==
                    TrafficLightTurnType::LEFT_TURN ||
                static_cast<TrafficLightTurnType>(traffic_light.second) ==
                    TrafficLightTurnType::LEFT_TURN_AND_STRAIGHT ||
                static_cast<TrafficLightTurnType>(traffic_light.second) ==
                    TrafficLightTurnType::LEFT_TURN_AND_UTURN)) {
      light_status = traffic_light.first;
      return true;
    } else if (junction_turn_type == 2 &&
               (static_cast<TrafficLightTurnType>(traffic_light.second) ==
                    TrafficLightTurnType::RIGHT_TURN ||
                static_cast<TrafficLightTurnType>(traffic_light.second) ==
                    TrafficLightTurnType::RIGHT_TURN_AND_STRAIGHT)) {
      light_status = traffic_light.first;
      return static_cast<TrafficLightTurnType>(traffic_light.second) !=
             TrafficLightTurnType::RIGHT_TURN_AND_STRAIGHT;
    } else if (junction_turn_type == 3 &&
               (static_cast<TrafficLightTurnType>(traffic_light.second) ==
                    TrafficLightTurnType::UTURN ||
                static_cast<TrafficLightTurnType>(traffic_light.second) ==
                    TrafficLightTurnType::LEFT_TURN_AND_UTURN)) {
      light_status = traffic_light.first;
      return true;
    }
  }
  return false;
}

}  // namespace rule
}  // namespace pnd
}  // namespace trunk
