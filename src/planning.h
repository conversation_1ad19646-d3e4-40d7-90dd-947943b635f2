// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <absl/container/flat_hash_map.h>

#include "port/frame.h"

namespace trunk {
namespace pnd {

namespace scenario {
class Scenario;
}

namespace rule {
class Rule;
}

class SysParam;

class Planning {
 public:
  Planning();

  ~Planning();

  bool Init();

  tpnc::Status RunOnce(const std::shared_ptr<port::Frame>& frame);

  void Reset();

 private:
  absl::flat_hash_map<std::string, std::unique_ptr<scenario::Scenario>>
      scenarios_;

  std::string last_scenario_name_;

  std::vector<std::unique_ptr<rule::Rule>> traffic_rules_;

  const SysParam* config_;
};

}  // namespace pnd
}  // namespace trunk
