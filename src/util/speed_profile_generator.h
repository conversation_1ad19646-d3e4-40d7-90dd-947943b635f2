// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/common.h>

#include "../port/reference_line_info.h"

namespace trunk {
namespace pnd {
namespace util {

class SpeedProfileGenerator {
 public:
  SpeedProfileGenerator() = delete;
  ~SpeedProfileGenerator() = delete;

  static const port::SpeedSTPoints GenerateInitSpeedProfile(
      const tport::TrajectoryPoint& planning_init_point,
      const port::ReferenceLineInfo* reference_line_info);

  static const port::SpeedSTPoints GenerateSpeedHotStart(
      const tport::TrajectoryPoint& planning_init_point);

  static const port::SpeedSTPoints GenerateFallbackSpeedProfile(
      const tport::TrajectoryPoint& planning_init_point);

  /**
   * @brief 按照指定减速度生成规划速度
   * */
  static const port::SpeedData GenerateStopProfile(const double init_speed,
                                                   const double total_time,
                                                   const double init_acc);
};

}  // namespace util
}  // namespace pnd
}  // namespace trunk
