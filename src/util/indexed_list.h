// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <unordered_map>
#include <vector>

namespace trunk {
namespace pnd {
namespace util {

/**
 * 此模板类收录到common里
 */

template <typename I, typename T>
class IndexedList {
 public:
  /**
   * @brief copy object into the container. If the id is already exist,
   * overwrite the object in the container.
   * @param id the id of the object
   * @param object the const reference of the objected to be copied to the
   * container.
   * @return The pointer to the object in the container.
   */
  T* Add(const I id, const T& object) {
    // 重复键覆盖
    if (object_dict_.count(id)) {
      object_dict_.erase(id);
    }
    object_dict_.emplace(id, object);
    auto* ptr = &object_dict_.at(id);
    object_list_.push_back(ptr);
    return ptr;
  }

  /**
   * @brief Find object by id in the container
   * @param id the id of the object
   * @return the raw pointer to the object if found.
   * @return nullptr if the object is not found.
   */
  T* Find(const I id) {
    auto it = object_dict_.find(id);
    if (it != object_dict_.end()) {
      return &it->second;
    } else {
      return nullptr;
    }
  }

  /**
   * @brief Find object by id in the container
   * @param id the id of the object
   * @return the raw pointer to the object if found.
   * @return nullptr if the object is not found.
   */
  const T* Find(const I id) const {
    auto it = object_dict_.find(id);
    if (it != object_dict_.end()) {
      return &it->second;
    } else {
      return nullptr;
    }
  }

  /**
   * @brief List all the items in the container.
   * @return the list of raw pointers of the objects in the container.
   */
  const std::vector<T*>& Items() const { return object_list_; }

  /**
   * @brief List all the items in the container.
   * @return the unordered_map of ids and objects in the container.
   */
  const std::unordered_map<I, T>& Dict() const { return object_dict_; }

  /**
   * @brief Copy the container with objects.
   */
  IndexedList& operator=(const IndexedList& other) {
    this->object_list_.clear();
    this->object_dict_.clear();
    for (const auto& item : other.Dict()) {
      Add(item.first, item.second);
    }
    return *this;
  }

 private:
  std::vector<T*> object_list_;
  std::unordered_map<I, T> object_dict_;
};

}  // namespace util
}  // namespace pnd
}  // namespace trunk
