// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/common.h>

#include <cstddef>

#include "../param/sys_param.h"
#include "../port/frame.h"

namespace trunk {
namespace pnd {
namespace util {

extern const SysParam& sys_config_;

class HistoryFrame : private std::shared_ptr<port::Frame> {
 public:
  HistoryFrame();
  // port::Frame* GetHistoryFrame();
  bool SetHistoryFrame(const std::shared_ptr<port::Frame>& frame);
  void UpdateHistoryFrame(const tport::VehiclePose& vehicle);
  port::Frame* operator->();
  bool operator==(std::nullptr_t);
  bool operator!=(std::nullptr_t);
};

extern const tcommon::model::Truck& model_;

class Truck {
 public:
  Truck() = default;
  void LoadModel();
  MEMBER_BASIC_TYPE(double, base2front, 0.0);
  MEMBER_BASIC_TYPE(double, base2tail, 0.0);
  MEMBER_BASIC_TYPE(double, width, 0.0);
  MEMBER_BASIC_TYPE(double, expand_width, 0.0);
  MEMBER_BASIC_TYPE(double, half_expand_width, 0.0);
  MEMBER_BASIC_TYPE(double, length, 0.0);
  MEMBER_BASIC_TYPE(double, head_wheelbase, 0.0);
  MEMBER_BASIC_TYPE(double, max_steering_angle, 0.0);
  MEMBER_BASIC_TYPE(double, max_curvature, 0.0);
};

extern Truck truck_;
extern HistoryFrame last_frame_;
extern const double kHighwayLaneWidth;
extern const double kHighwayLaneHalfWidth;
extern const double kHighwayEmergencyLaneWidth;
extern const double kHighwayEmergencyLaneHalfWidth;
extern const double kDt;

}  // namespace util
}  // namespace pnd
}  // namespace trunk
