// Copyright 2023, trunk Inc. All rights reserved

#include "speed_profile_generator.h"

#include "log.h"
#include "util/key.h"

namespace trunk {
namespace pnd {
namespace util {

const port::SpeedSTPoints SpeedProfileGenerator::GenerateInitSpeedProfile(
    const tport::TrajectoryPoint& planning_init_point,
    const port::ReferenceLineInfo* reference_line_info) {
  port::SpeedSTPoints speed_profile;
  if (util::last_frame_ == nullptr) {
    TRUNK_LOG_WARN << "last frame is empty";
    return speed_profile;
  }
  const auto* last_reference_line_info =
      util::last_frame_->drive_reference_line_info();
  if (last_reference_line_info == nullptr) {
    TRUNK_LOG_DEBUG << "last reference line info is empty";
    return speed_profile;
  }
  if (!reference_line_info->IsStartFrom(*last_reference_line_info)) {
    TRUNK_LOG_DEBUG
        << "Current reference line is not started previous drived line";
    return speed_profile;
  }
  const auto& last_speed_data = last_reference_line_info->speed_data();

  if (!last_speed_data.empty()) {
    auto init_point_in_last = planning_init_point;
    tutil::TransformUtm::TransformUtmToVehicleCoordCompose(
        util::last_frame_->env().vehicle(), init_point_in_last);
    const double init_point_s_in_last =
        tutil::TransformFrenet::TransformToFrenet(
            last_reference_line_info->reference_line(), init_point_in_last)
            .s();
    const double last_init_point_s =
        tutil::TransformFrenet::TransformToFrenet(
            last_reference_line_info->reference_line(),
            util::last_frame_->init_point())
            .s();
    const double s_diff = init_point_s_in_last - last_init_point_s;
    const auto& last_init_point = util::last_frame_->init_point();
    auto last_sl_point = tutil::TransformFrenet::TransformToFrenet(
        last_reference_line_info->reference_line(), last_init_point);
    auto sl_point = tutil::TransformFrenet::TransformToFrenet(
        last_reference_line_info->reference_line(), planning_init_point);
    /* double s_diff = sl_point.s() - last_sl_point.s(); */
    double start_time = 0.0;
    double start_s = 0.0;
    bool is_updated_start = false;
    for (const auto& speed_point : last_speed_data) {
      if (speed_point.s() - 0.0 < s_diff) {
        continue;
      }
      if (!is_updated_start) {
        start_time = speed_point.t();
        start_s = speed_point.s();
        is_updated_start = true;
      }
      port::SpeedSTPoint refined_speed_point;
      refined_speed_point.set_s(speed_point.s() - s_diff);
      refined_speed_point.set_t(speed_point.t() - start_time);
      refined_speed_point.set_v(speed_point.v());
      refined_speed_point.set_a(speed_point.a());
      refined_speed_point.set_da(speed_point.da());
      speed_profile.push_back(std::move(refined_speed_point));
    }
  }
  return speed_profile;
}

const port::SpeedSTPoints SpeedProfileGenerator::GenerateSpeedHotStart(
    const tport::TrajectoryPoint& planning_init_point) {
  port::SpeedSTPoints hot_start_speed_profile;
  double s = 0.0;
  double t = 0.0;
  constexpr double kLowestV = 5.0;          // m/s
  constexpr double kHighestV = 20.0;        // m/s
  constexpr double kTimeLength = 8.0;       // s
  constexpr double kTimeMinInterval = 0.1;  // s
  double v = tutil::clamp(planning_init_point.v(), kLowestV, kHighestV);
  while (t < kTimeLength) {
    port::SpeedSTPoint speed_point;
    speed_point.set_s(s);
    speed_point.set_t(t);
    speed_point.set_v(v);
    hot_start_speed_profile.push_back(std::move(speed_point));
    t += kTimeMinInterval;
    s += v * kTimeMinInterval;
  }
  return hot_start_speed_profile;
}

const port::SpeedSTPoints SpeedProfileGenerator::GenerateFallbackSpeedProfile(
    const tport::TrajectoryPoint& planning_init_point) {
  // TODO: Polynomial strategy
  return GenerateStopProfile(planning_init_point.v(), 8.0,
                             planning_init_point.a());
}

const port::SpeedData SpeedProfileGenerator::GenerateStopProfile(
    const double init_speed, const double total_time, const double init_acc) {
  TRUNK_LOG_ERROR << "Using fallback stopping profile: Slowing down the car!";
  constexpr double acc = -1.0;
  double pre_s = 0.0;
  double pre_v = init_speed;
  port::SpeedData speed_data;
  for (double t = 0.0; t < total_time; t += util::kDt) {
    double s = 0.0;
    double v = 0.0;
    s = std::fmax(pre_s, pre_s + (pre_v + 0.5 * acc * util::kDt) * util::kDt);
    v = std::fmax(0.0, pre_v + util::kDt * acc);
    speed_data.AppendSpeedSTPoint(s, t, v, acc, 0.0);
    pre_s = s;
    pre_v = v;
  }
  return speed_data;
}

}  // namespace util
}  // namespace pnd
}  // namespace trunk
