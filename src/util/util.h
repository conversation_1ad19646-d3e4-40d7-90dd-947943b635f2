// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <cfloat>
#include <cmath>
#include <cstdlib>
#include <unordered_set>

#include "key.h"
#include "log.h"
#include "param/sys_param.h"
#include "trunk/common/util/tools/tools.h"

namespace trunk {
namespace pnd {
namespace util {

template <typename T>
void CalcTrajTrailerTheta(const bool forward_flag,
                          const double start_trailer_phi, T* const traj) {
  if (traj->empty()) {
    TRUNK_LOG_ERROR << "traj is empty";
    return;
  }
  const auto config = tpnc::Singleton<tmodel::Truck>::GetInstance();
  if (config == nullptr) {
    TRUNK_LOG_ERROR << "config is nullptr";
    return;
  }
  const double trailer_wheelbase = config->trailer().geometry().wheelbase();
  double trailer_phi = start_trailer_phi;
  traj->at(0).set_trailer_theta(trailer_phi);
  for (size_t i = 1; i < traj->size(); ++i) {
    const double s =
        forward_flag ? tutil::TwoPointsDistance(traj->at(i), traj->at(i - 1))
                     : -tutil::TwoPointsDistance(traj->at(i), traj->at(i - 1));
    trailer_phi = tutil::NormalizeAngle(
        trailer_phi + s / trailer_wheelbase *
                          std::sin(traj->at(i - 1).theta() - trailer_phi));
    traj->at(i).set_trailer_theta(trailer_phi);
  }
}
template <typename T>
const port::SLBoundary GenerateSLBound(const T& ref_line,
                                       const tport::Contour2D& contour) {
  port::SLBoundary bound;
  if (contour.empty()) {
    return bound;
  }
  std::vector<double> vec_s;
  std::vector<double> vec_l;
  vec_s.reserve(contour.size());
  vec_l.reserve(contour.size());
  for (const auto& pt : contour) {
    const auto sl_point =
        tutil::TransformFrenet::TransformToFrenet(ref_line, pt);
    vec_s.push_back(sl_point.s());
    vec_l.push_back(sl_point.l());
  }
  std::sort(vec_s.begin(), vec_s.end());
  std::sort(vec_l.begin(), vec_l.end());
  bound.set_min_s(vec_s.front());
  bound.set_max_s(vec_s.back());
  bound.set_min_l(vec_l.front());
  bound.set_max_l(vec_l.back());
  return bound;
}

/**
 * uniformly slice a segment [start, end] to num + 1 pieces
 * the result sliced will contain the n + 1 points that slices the provided
 * segment. `start` and `end` will be the first and last element in `sliced`.
 */
// TODO: common 3.7版本录入
template <typename T>
void uniform_slice(const T start, const T end, uint32_t num,
                   std::vector<T>& sliced) {
  if (num == 0) {
    return;
  }
  const T delta = (end - start) * (1.0 / num);
  sliced.resize(num + 1);
  T s = start;
  for (uint32_t i = 0; i < num; ++i, s += delta) {
    sliced.at(i) = s;
  }
  sliced.at(num) = end;
}

template <typename T>
void FastCalcPathDkappa(T& path) {
  if (path.size() < 2) {
    return;
  }
  // first point kappa
  path.at(0).set_dkappa((path.at(1).kappa() - path.at(0).kappa()) /
                        (path.at(1).s() - path.at(0).s()));
  // mid points kappa
  for (int i = 1; i < path.size() - 1; ++i) {
    path[i].set_dkappa((path[i + 1].kappa() - path[i - 1].kappa()) /
                       (path[i + 1].s() - path[i - 1].s()));
  }
  // last point kappa
  const int last_index = path.size() - 1;
  path.at(last_index)
      .set_dkappa(
          (path.at(last_index).kappa() - path.at(last_index - 1).kappa()) /
          (path.at(last_index).s() - path.at(last_index - 1).s()));
}

void ErasePointBeforeSelfVehicle(tport::Trajectory& traj);
bool FliterDecition(const bool& result, const bool& enable, int times);

template <typename T>
T FindProjectionPointByDistance(const std::vector<T>& line,
                                const tport::Point2D& point) {
  size_t size = line.size();
  if (size <= 1) {
    if (size == 0) {
      T p;
      p.set_x(point.x());
      p.set_y(point.y());
      return p;
    }
    return line.front();
  }
  size_t begin_idx = 0;
  size_t end_idx = size;
  double begin_dis = tutil::TwoPointsDistance(line.front(), point);
  double end_dis = tutil::TwoPointsDistance(line.back(), point);
  while (size > 2) {
    // 中点位置一定<=实际size的一半
    size_t half_size = size / 2;
    if (begin_dis < end_dis) {
      end_idx -= half_size;
      end_dis = tutil::TwoPointsDistance(line.at(end_idx - 1), point);
    } else {
      begin_idx += half_size;
      begin_dis = tutil::TwoPointsDistance(line.at(begin_idx), point);
    }
    size -= half_size;
  }
  const auto& p1 = line.at(begin_idx);
  const auto& p2 = line.at(begin_idx + 1);
  const auto p = p1 - p2;
  const auto dis = std::sqrt(p.x() * p.x() + p.y() * p.y());

  if (dis <= DBL_EPSILON) {
    TRUNK_LOG_ERROR << "The interpolation endpoints are too close! distance = "
                    << dis << ", p1 = " << p1 << ", p2 = " << p2;
  }
  return tutil::FindProjectPointInPath(line.at(begin_idx),
                                       line.at(begin_idx + 1), point);
}

template <typename T>
const T FindNearestPointByDistance(T begin, T end,
                                   const tport::Point2D& point) {
  size_t size = std::distance(begin, end);
  if (size == 0) {
    return begin;
  }
  double begin_dis = tutil::TwoPointsDistance(*begin, point);
  double end_dis = tutil::TwoPointsDistance(*(end - 1), point);
  while (size > 1) {
    // 中点位置一定<=实际size的一半
    size_t half_size = size / 2;
    if (begin_dis < end_dis) {
      std::advance(end, -half_size);
      end_dis = tutil::TwoPointsDistance(*(end - 1), point);
    } else {
      std::advance(begin, half_size);
      begin_dis = tutil::TwoPointsDistance(begin, point);
    }
    size -= half_size;
  }
  return begin;
}

/**
 * @brief Check if a line segment has overlap with this polygon.
 * @param line_segment The target line segment. To check if it has
 *        overlap with this polygon.
 * @return Whether the target line segment has overlap with this
 *         polygon or not.
 */

template <class T>
tutil::Point GetFarthestPointInDirection(const std::vector<T>& polygon,
                                         const tutil::Direction& direction) {
  int index = std::numeric_limits<int>::max();
  double max = std::numeric_limits<double>::lowest();
  for (int i = 0; i < polygon.size(); ++i) {
    // double project = polygon.at(i).Dot(direction);
    double project =
        polygon.at(i).x() * direction.x() + polygon.at(i).y() * direction.y();
    if (project > max) {
      max = project;
      index = i;
    }
  }
  return tutil::Point(polygon.at(index).x(), polygon.at(index).y());
}

inline double StraightLineByTwoPoint(const double x1, const double y1,
                                     const double x2, const double y2,
                                     const double x) {
  return (y2 - y1) / (x2 - x1) * (x - x1) + y1;
}

inline double ParabolaByTwoPoint(const double peakx, const double peaky,
                                 const double x0, const double y0,
                                 const double x) {
  const double r = (x - peakx) / (x0 - peakx);
  return peaky + r * r * (y0 - peaky);
}

inline int VirtualObstacleIdGenerator() {
  static int id = 0;
  if (id < -1000) {
    id = 0;
  }
  return --id;
}

inline double GetRoadWidth(
    const port::ReferenceLineInfo* const reference_line_info, double s) {
  const auto lr_width = reference_line_info->GetRoadWidth(s);
  return lr_width.left_width - lr_width.right_width;
}

inline double GetLaneWidth(
    const port::ReferenceLineInfo* const reference_line_info, double s) {
  const auto lr_width = reference_line_info->GetLaneWidth(s);
  return lr_width.left_width - lr_width.right_width;
}

template <typename T>
inline const T SaturationRatio(const T val, const T min, const T max) {
  return std::abs(max - min) < DBL_EPSILON
             ? 1.0
             : std::min(1.0, std::max((val - min) / (max - min), 0.0));
}

template <typename T>
inline const T SaturationDecreasingRatio(const T val, const T min,
                                         const T max) {
  return std::abs(max - min) < DBL_EPSILON
             ? 1.0
             : std::min(1.0, std::max((max - val) / (max - min), 0.0));
}

template <typename T>
inline const T ReLU(const T val) {
  return val > 0 ? val : 0.0;
}

}  // namespace util
}  // namespace pnd
}  // namespace trunk
