// Copyright 2023, trunk Inc. All rights reserved

#include "util.h"

namespace trunk {
namespace pnd {
namespace util {

void ErasePointBeforeSelfVehicle(tport::Trajectory& path) {
  size_t index = tutil::FindNearestPointIndexInVehicleCoord(path);
  while (index > 0 && path[index].x() >= 0) {
    index--;
  }
  tutil::ErasePointBeforeIndex(index, path);
}

bool FliterDecition(const bool& result, const bool& enable, int times) {
  static int count = 0;
  count = enable == result ? ++count : --count;
  if (count < 0) {
    count = 0;
  }
  if (count > times) {
    count = times;
  }
  if (count == times) {
    count = 0;
    TRUNK_LOG_INFO << "times = " << times << " count = " << count;
    return true;
  }
  TRUNK_LOG_INFO << "times = " << times << " count = " << count;
  return false;
}

}  // namespace util
}  // namespace pnd
}  // namespace trunk
