// Copyright 2023, trunk Inc. All rights reserved

#include "key.h"

#include <cstddef>

#include "../log.h"
#include "util.h"

namespace trunk {
namespace pnd {
namespace util {

const SysParam& sys_config_ =
    tpnc::Singleton<tpnc::Config>::GetInstance()->GetConfig<SysParam>("sys");
const tcommon::model::Truck& model_ =
    *tpnc::Singleton<tcommon::model::Truck>::GetInstance();
Truck truck_;

void HistoryFrame::UpdateHistoryFrame(const tport::VehiclePose& vehicle) {
  if (this->get() == nullptr) {
    TRUNK_LOG_INFO << "last frame is nullptr";
    return;
  }
  this->get()->ResetLastPointer();
  for (auto& last_reference_line_info :
       *this->get()->mutable_reference_lines_info()) {
    tutil::TransformUtm::Utm2Vehicle(
        vehicle, *last_reference_line_info.mutable_reference_line());
    tutil::TransformUtm::Utm2Vehicle(
        vehicle, *last_reference_line_info.mutable_trajectory());
    ErasePointBeforeSelfVehicle(*last_reference_line_info.mutable_trajectory());
    if (last_reference_line_info.trajectory().size()) {
      const double s0 =
          tutil::TransformFrenet::TransformToFrenet(
              last_reference_line_info.trajectory(), tport::Point2D())
              .s();
      const auto t0 =
          last_reference_line_info.trajectory().EvaluateByS(s0).relative_time();
      for (auto& p : *last_reference_line_info.mutable_trajectory()) {
        p.set_s(p.s() - s0);
        p.set_relative_time(p.relative_time() - t0);
      }
    }
  }
}
HistoryFrame::HistoryFrame() : std::shared_ptr<port::Frame>(nullptr) {}
// port::Frame* HistoryFrame::GetHistoryFrame() { return this->get(); }
bool HistoryFrame::SetHistoryFrame(const std::shared_ptr<port::Frame>& frame) {
  this->std::shared_ptr<port::Frame>::operator=(frame);
  return true;
}
port::Frame* HistoryFrame::operator->() { return this->get(); }
bool HistoryFrame::operator==(std::nullptr_t) { return this->get() == nullptr; }
bool HistoryFrame::operator!=(std::nullptr_t) { return this->get() != nullptr; }

void Truck::LoadModel() {
  base2front_ = model_.geometry().base2front();
  base2tail_ = sys_config_.with_trailer()
                   ? model_.trailer().geometry().base2tail()
                   : model_.geometry().base2tail();
  length_ = base2tail_ + base2front_;
  width_ = model_.geometry().width();
  expand_width_ =
      model_.geometry().width() + sys_config_.expand_vehicle_width();
  half_expand_width_ = 0.5 * expand_width_;
  max_steering_angle_ = model_.max_steering_angle();
  head_wheelbase_ = model_.geometry().wheelbase();
  max_curvature_ =
      std::tan(tutil::Deg2Rad(max_steering_angle_)) / head_wheelbase_;
}

HistoryFrame last_frame_;
const double kHighwayLaneWidth = 3.75;
const double kHighwayLaneHalfWidth = kHighwayLaneWidth * 0.5;
const double kHighwayEmergencyLaneWidth = 3.0;
const double kHighwayEmergencyLaneHalfWidth = kHighwayEmergencyLaneWidth * 0.5;
const double kDt = 0.25;

}  // namespace util
}  // namespace pnd
}  // namespace trunk
