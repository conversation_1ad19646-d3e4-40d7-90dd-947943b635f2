// Copyright 2023, trunk Inc. All rights reserved

#include "planning.h"

#include "log.h"
#include "param/sys_param.h"
#include "scenario/scenario.h"
#include "traffic_rule/rule.h"
#include "util/key.h"

namespace trunk {
namespace pnd {

Planning::Planning() = default;

Planning::~Planning() = default;

bool Planning::Init() {
  config_ =
      &tpnc::Singleton<tpnc::Config>::GetInstance()->GetConfig<SysParam>("sys");
  for (const auto& rule : config_->traffic_rules()) {
    traffic_rules_.push_back(
        tpnc::Factory<rule::Rule>::GetInstance().GetClassByName(rule));
  }
  for (const auto& scenario : config_->scenarios()) {
    scenarios_[scenario] =
        tpnc::Factory<scenario::Scenario>::GetInstance().GetClassByName(
            scenario);
  }
  for (auto& scenario : scenarios_) {
    if (!scenario.second->Init()) {
      return false;
    }
  }
  return true;
}

tpnc::Status Planning::RunOnce(const std::shared_ptr<port::Frame>& frame) {
  auto start_run_time = absl::Now();
  util::last_frame_.UpdateHistoryFrame(frame->env().vehicle());
  if (!frame->Init()) {
    return tpnc::Status::ERROR("frame init failed");
  }
  // apply traffic rules
  for (auto& reference_line_info : *frame->mutable_reference_lines_info()) {
    for (const auto& rule : traffic_rules_) {
      auto ret = rule->ApplyRuleAndCalRunTime(frame, &reference_line_info);
      if (!ret.ok()) {
        TRUNK_LOG_ERROR << ret.error_message();
      }
    }
  }
  // TODO: 策略模式选择Scenario
  std::string current_scenario_name = "LaneFollow";
  const auto& current_scenario = scenarios_[current_scenario_name];
  if (current_scenario_name != last_scenario_name_) {
    Reset();
  }
  auto ret = current_scenario->Process(frame);
  if (ret.ok()) {
    util::last_frame_.SetHistoryFrame(frame);
  } else {
    TRUNK_LOG_ERROR << ret.error_message();
    if (frame->UseLastFrameTrajectory()) {
      TRUNK_LOG_WARN << "use last frame trajectory";
      return tpnc::Status::OK();
    } else {
      Reset();
      TRUNK_LOG_ERROR
          << "Planning failure!! No trace was available in the previous frame";
    }
  }
  last_scenario_name_ = current_scenario_name;

  frame->set_run_time(absl::ToDoubleMilliseconds(absl::Now() - start_run_time));
  TRUNK_LOG_DEBUG << "one run time: " << frame->run_time();

  return ret;
}

void Planning::Reset() {
  util::last_frame_.SetHistoryFrame(nullptr);
  for (auto& scenario : scenarios_) {
    scenario.second->Reset();
  }
}

}  // namespace pnd
}  // namespace trunk
