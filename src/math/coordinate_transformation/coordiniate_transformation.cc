// Copyright 2023, trunk Inc. All rights reserved

#include "coordiniate_transformation.h"
namespace trunk {
namespace pnd {
namespace math {

void CoordinateRotationAroundOrigin(tport::Point2D& point, const double theta) {
  const double sin_theta = std::sin(theta);
  const double cos_theta = std::cos(theta);
  const double x = point.x();
  point.set_x(cos_theta * x - sin_theta * point.y());
  point.set_y(sin_theta * x + cos_theta * point.y());
}
void CoordinateRotationAroundOrigin(const tport::Point2D& point,
                                    const double theta,
                                    tport::Point2D& result) {
  const double sin_theta = std::sin(theta);
  const double cos_theta = std::cos(theta);
  result.set_x(cos_theta * point.x() - sin_theta * point.y());
  result.set_y(sin_theta * point.x() + cos_theta * point.y());
}

void CoordinateTransformationAroundOrigin(tport::Point2D& point,
                                          const double theta,
                                          const tport::Point2D& displacement) {
  CoordinateRotationAroundOrigin(point, theta);
  point += displacement;
}
void CoordinateTransformationAroundOrigin(const tport::Point2D& point,
                                          const double theta,
                                          const tport::Point2D& displacement,
                                          tport::Point2D& result) {
  CoordinateRotationAroundOrigin(point, theta, result);
  result += displacement;
}

void CoordinateRotationAroundAnchor(tport::Point2D& point,
                                    const tport::Point2D& anchor,
                                    const double theta) {
  point -= anchor;
  CoordinateTransformationAroundOrigin(point, theta, anchor);
}

void CoordinateRotationAroundAnchor(const tport::Point2D& point,
                                    const tport::Point2D& anchor,
                                    const double theta,
                                    tport::Point2D& result) {
  CoordinateTransformationAroundOrigin(point - anchor, theta, anchor, result);
}

void CoordinateRotationAroundAnchorResetOrigin(tport::Point2D& point,
                                               const tport::Point2D& anchor,
                                               const double theta,
                                               const tport::Point2D& origin) {
  point -= anchor;
  CoordinateTransformationAroundOrigin(point, theta, origin);
}

void CoordinateRotationAroundAnchorResetOrigin(const tport::Point2D& point,
                                               const tport::Point2D& anchor,
                                               const double theta,
                                               const tport::Point2D& origin,
                                               tport::Point2D& result) {
  CoordinateTransformationAroundOrigin(point - anchor, theta, origin, result);
}

}  // namespace math
}  // namespace pnd
}  // namespace trunk
