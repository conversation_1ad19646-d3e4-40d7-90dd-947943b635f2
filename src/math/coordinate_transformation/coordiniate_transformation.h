// Copyright 2023, trunk Inc. All rights reserved
#pragma once

#include <math.h>
#include <trunk/common/common.h>

namespace trunk {
namespace pnd {
namespace math {

void CoordinateRotationAroundOrigin(tport::Point2D& point, const double theta);
void CoordinateRotationAroundOrigin(const tport::Point2D& point,
                                    const double theta, tport::Point2D& result);
void CoordinateTransformationAroundOrigin(tport::Point2D& point,
                                          const double theta,
                                          const tport::Point2D& displacement);
void CoordinateTransformationAroundOrigin(const tport::Point2D& point,
                                          const double theta,
                                          const tport::Point2D& displacement,
                                          tport::Point2D& result);
void CoordinateRotationAroundAnchor(tport::Point2D& point,
                                    const tport::Point2D& anchor,
                                    const double theta);
void CoordinateRotationAroundAnchor(const tport::Point2D& point,
                                    const tport::Point2D& anchor,
                                    const double theta, tport::Point2D& result);
void CoordinateRotationAroundAnchorResetOrigin(tport::Point2D& point,
                                               const tport::Point2D& anchor,
                                               const double theta,
                                               const tport::Point2D& origin);

void CoordinateRotationAroundAnchorResetOrigin(const tport::Point2D& point,
                                               const tport::Point2D& anchor,
                                               const double theta,
                                               const tport::Point2D& origin,
                                               tport::Point2D& result);
}  // namespace math
}  // namespace pnd
}  // namespace trunk
