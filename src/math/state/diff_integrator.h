// Copyright 2025, trunk Inc. All rights reserved
#pragma once

#include "discrete_system.h"

namespace trunk {
namespace pnd {
namespace math {

template <int ORDER>
class DiffIntegrator : public DiscreteSystem<ORDER> {
 public:
  DiffIntegrator(const double dt, const Eigen::Matrix<double, ORDER, 1>& x =
                                      Eigen::Matrix<double, ORDER, 1>())
      : DiscreteSystem<ORDER>(dt, x) {
    auto& A_ = (*this->mutable_A());
    A_ = Eigen::Matrix<double, ORDER, ORDER>::Identity();
    A_.block(0, 1, ORDER - 1, ORDER - 1) +=
        dt * Eigen::MatrixXd::Identity(ORDER - 1, ORDER - 1);
    auto& B_ = (*this->mutable_B());
    B_[ORDER - 1] = dt;
  }

 private:
};

}  // namespace math
}  // namespace pnd
}  // namespace trunk
