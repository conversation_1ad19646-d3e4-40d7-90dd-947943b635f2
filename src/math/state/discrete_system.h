// Copyright 2025, trunk Inc. All rights reserved
#pragma once
#include <trunk/common/common.h>
#include <trunk/common/config.h>

#include <Eigen/Core>
#include <Eigen/Dense>
#include <Eigen/Sparse>

namespace trunk {
namespace pnd {
namespace math {

template <int ORDER>
class DiscreteSystem {
 public:
  DiscreteSystem(const double dt, const Eigen::Matrix<double, ORDER, 1>& x =
                                      Eigen::Matrix<double, ORDER, 1>())
      : dt_(dt),
        A_(Eigen::Matrix<double, ORDER, ORDER>::Zero()),
        B_(Eigen::Matrix<double, ORDER, 1>::Zero()),
        x_(x) {}
  void SetInitState(const Eigen::Matrix<double, ORDER, 1>& x,
                    const double t = 0.0) {
    x_ = x;
    t_ = t;
  }
  const Eigen::Matrix<double, ORDER, 1>& Update(const double u) {
    x_ = A_ * x_ + B_ * u;
    t_ += dt_;
    return x_;
  }

 private:
  using Matd = Eigen::Matrix<double, ORDER, ORDER>;
  using Vecd = Eigen::Matrix<double, ORDER, 1>;
  MEMBER_COMPLEX_TYPE(Matd, A);
  MEMBER_COMPLEX_TYPE(Vecd, B);
  MEMBER_COMPLEX_TYPE(Vecd, x);
  MEMBER_BASIC_TYPE(double, t, 0.0);
  MEMBER_BASIC_TYPE(double, dt, 0.0);
};

}  // namespace math
}  // namespace pnd
}  // namespace trunk
