// Copyright 2025, trunk Inc. All rights reserved
#pragma once

#include "discrete_system.h"

namespace trunk {
namespace pnd {
namespace math {

template <int ORDER>
class ZohIntegrator : public DiscreteSystem<ORDER> {
 public:
  ZohIntegrator(const double dt, const Eigen::Matrix<double, ORDER, 1>& x =
                                     Eigen::Matrix<double, ORDER, 1>())
      : DiscreteSystem<ORDER>(dt, x) {
    auto& A_ = (*this->mutable_A());
    auto& B_ = (*this->mutable_B());
    double diag = 1.0;
    for (int i = 0; i < ORDER; ++i) {
      A_.block(0, i, ORDER - i, ORDER - i) +=
          diag * Eigen::MatrixXd::Identity(ORDER - i, ORDER - i);
      diag *= (dt / double(i + 1));
    }
    B_ = dt * A_.block(0, ORDER - 1, ORDER, 1);
    for (int i = 0; i < ORDER; ++i) {
      B_[i] /= double(ORDER - i);
    }
  }
};

}  // namespace math
}  // namespace pnd
}  // namespace trunk
