// Copyright 2024, trunk Inc. All rights reserved
#include "quintic_polynomial.h"

namespace trunk {
namespace pnd {
namespace math {

QuinticPolynomial::QuinticPolynomial(const double t, const double f0,
                                     const double df0, const double ddf0,
                                     const double f1, const double df1,
                                     const double ddf1) {
  c_[0] = f0;
  c_[1] = df0;
  c_[2] = ddf0 * 0.5;
  double p = t;
  double p2 = p * p;
  double p3 = p2 * p;
  double c0 = (f1 - 0.5 * p2 * ddf0 - df0 * p - f0) / p3;
  double c1 = (df1 - ddf0 * p - df0) / p2;
  double c2 = (ddf1 - ddf0) / p;

  c_[3] = 0.5 * (20.0 * c0 - 8.0 * c1 + c2);
  c_[4] = (-15.0 * c0 + 7.0 * c1 - c2) / p;
  c_[5] = (6.0 * c0 - 3.0 * c1 + 0.5 * c2) / p2;
}

double QuinticPolynomial::Evaluate(const unsigned int order,
                                   const double t) const {
  switch (order) {
    case 0: {
      return ((((c_[5] * t + c_[4]) * t + c_[3]) * t + c_[2]) * t + c_[1]) * t +
             c_[0];
    }
    case 1: {
      return (((5.0 * c_[5] * t + 4.0 * c_[4]) * t + 3.0 * c_[3]) * t +
              2.0 * c_[2]) *
                 t +
             c_[1];
    }
    case 2: {
      return (((20.0 * c_[5] * t + 12.0 * c_[4]) * t) + 6.0 * c_[3]) * t +
             2.0 * c_[2];
    }
    case 3: {
      return (60.0 * c_[5] * t + 24.0 * c_[4]) * t + 6.0 * c_[3];
    }
    case 4: {
      return 120.0 * c_[5] * t + 24.0 * c_[4];
    }
    case 5: {
      return 120.0 * c_[5];
    }
    default:
      return 0.0;
  }
}

}  // namespace math
}  // namespace pnd
}  // namespace trunk
