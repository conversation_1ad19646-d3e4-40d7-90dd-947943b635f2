// Copyright 2024, trunk Inc. All rights reserved
#include "quartic_polynomial.h"

namespace trunk {
namespace pnd {
namespace math {

QuarticPolynomial::QuarticPolynomial(const double t, const double f0,
                                     const double df0, const double ddf0,
                                     const double df1, const double ddf1) {
  c_[0] = f0;
  c_[1] = df0;
  c_[2] = ddf0 * 0.5;
  double t2 = t * t;
  double p0 = (df1 - df0 - t * ddf0) / t2;
  double p1 = (ddf1 - ddf0) / t;

  c_[3] = (3.0 * p0 - p1) / 3.0;
  c_[4] = (p1 - 2.0 * p0) / t / 4.0;
}

double QuarticPolynomial::Evaluate(const unsigned int order,
                                   const double t) const {
  switch (order) {
    case 0: {
      return (((c_[4] * t + c_[3]) * t + c_[2]) * t + c_[1]) * t + c_[0];
    }
    case 1: {
      return ((4.0 * c_[4] * t + 3.0 * c_[3]) * t + 2.0 * c_[2]) * t + c_[1];
    }
    case 2: {
      return (12.0 * c_[4] * t + 6.0 * c_[3]) * t + 2.0 * c_[2];
    }
    case 3: {
      return 24.0 * c_[4] * t + 6.0 * c_[3];
    }
    case 4: {
      return 24.0 * c_[4];
    }
    default:
      return 0.0;
  }
}

}  // namespace math
}  // namespace pnd
}  // namespace trunk
