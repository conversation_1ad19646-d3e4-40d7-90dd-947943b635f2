// Copyright 2023, trunk Inc. All rights reserved
#pragma once

#include <string>
namespace trunk {
namespace pnd {
namespace math {

class Curve1d {
 public:
  Curve1d() = default;

  virtual ~Curve1d() = default;

  virtual double Evaluate(const std::uint32_t order,
                          const double param) const = 0;

  virtual double ParamLength() const = 0;

  virtual std::string ToString() const = 0;
};

}  // namespace math
}  // namespace pnd
}  // namespace trunk
