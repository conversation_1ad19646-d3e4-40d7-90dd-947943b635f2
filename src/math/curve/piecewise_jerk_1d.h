// Copyright 2023, trunk Inc. All rights reserved
#pragma once

#include <vector>

#include "const_jerk_1d.h"

namespace trunk {
namespace pnd {
namespace math {

class PiecewiseJerk1d : public Curve1d {
 public:
  PiecewiseJerk1d(const double p, const double v, const double a);

  virtual ~PiecewiseJerk1d() = default;

  double Evaluate(const std::uint32_t order, const double param) const;

  double ParamLength() const;

  std::string ToString() const;

  void AppendSegment(const double jerk, const double param);

 private:
  std::vector<ConstantJerk1d> segments_;

  double last_p_;

  double last_v_;

  double last_a_;

  std::vector<double> param_;
};

}  // namespace math
}  // namespace pnd
}  // namespace trunk
