// Copyright 2024, trunk Inc. All rights reserved
#pragma once

namespace trunk {
namespace pnd {
namespace math {

class QuarticPolynomial {
 public:
  QuarticPolynomial() = default;
  QuarticPolynomial(const QuarticPolynomial&) = default;
  QuarticPolynomial(const double t, const double f0, const double df0,
                    const double ddf, const double df1, const double ddf1);

  double Evaluate(const unsigned int order, const double t) const;

  double c_[5]{};
};

}  // namespace math
}  // namespace pnd
}  // namespace trunk
