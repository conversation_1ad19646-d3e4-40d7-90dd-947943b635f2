// Copyright 2023, trunk Inc. All rights reserved

#include "piecewise_jerk_1d.h"

namespace trunk {
namespace pnd {
namespace math {

PiecewiseJerk1d::PiecewiseJerk1d(const double p, const double v,
                                 const double a) {
  last_p_ = p;
  last_v_ = v;
  last_a_ = a;
  param_.push_back(0.0);
}

// 依据恒定jerk曲线(即3次多项式),根据 Δs 把各段曲线的起点、终点信息恢复出来
void PiecewiseJerk1d::AppendSegment(const double jerk, const double param) {
  // param_（即delta_s）有个累加的行为
  // 这样下面Evaluate的时候就方便知道具体是哪个segment
  param_.push_back(param_.back() + param);

  segments_.emplace_back(last_p_, last_v_, last_a_, jerk, param);

  last_p_ = segments_.back().end_position();

  last_v_ = segments_.back().end_velocity();

  last_a_ = segments_.back().end_acceleration();
}

// 调用 constant_jerk_trajectory1d.cc 的 Evaluate()，输入 s 求出 l、l'、l''
// 此时会判断下该s 属于哪个segment，借助lower_bound()函数
double PiecewiseJerk1d::Evaluate(const std::uint32_t order,
                                 const double param) const {
  auto it_lower = std::lower_bound(param_.begin(), param_.end(), param);
  // 两头越界情况的处理
  if (it_lower == param_.begin()) {
    return segments_[0].Evaluate(order, param);
  }
  if (it_lower == param_.end()) {
    auto index = std::max(0, static_cast<int>(param_.size() - 2));
    return segments_.back().Evaluate(order, param - param_[index]);
  }

  auto index = std::distance(param_.begin(), it_lower);
  return segments_[index - 1].Evaluate(order, param - param_[index - 1]);
}

// 返回曲线总长度
double PiecewiseJerk1d::ParamLength() const { return param_.back(); }

std::string PiecewiseJerk1d::ToString() const { return ""; }

}  // namespace math
}  // namespace pnd
}  // namespace trunk
