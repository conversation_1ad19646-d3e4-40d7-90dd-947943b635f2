// Copyright 2021, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {

class DpTslIntervalLatticeTaskParam {
  MEMBER_BASIC_TYPE(double, t_end, 7.0);
  MEMBER_BASIC_TYPE(double, t_step, 0.1);
  MEMBER_BASIC_TYPE(double, max_s, 180.0);
  MEMBER_BASIC_TYPE(int, sseg_num, 80);
  MEMBER_BASIC_TYPE(int, vseg_num, 10);
  MEMBER_BASIC_TYPE(int, aseg_num, 4);
  MEMBER_BASIC_TYPE(double, max_acc, 1.0);
  MEMBER_BASIC_TYPE(double, min_acc, -4.0);
  MEMBER_BASIC_TYPE(int, lseg_num, 3);
  MEMBER_BASIC_TYPE(int, dlseg_num, 3);
  MEMBER_BASIC_TYPE(int, ddlseg_num, 3);
  MEMBER_BASIC_TYPE(double, max_l, 1.35);
  MEMBER_BASIC_TYPE(double, min_l, -1.35);
  MEMBER_BASIC_TYPE(double, max_dl, 0.5);
  MEMBER_BASIC_TYPE(double, min_dl, -0.5);
  MEMBER_BASIC_TYPE(double, max_steering_angular_velocity, 0.1);
  MEMBER_BASIC_TYPE(int, dddlseg_num, 5);
  MEMBER_BASIC_TYPE(double, max_jerk, 3.0);
  MEMBER_BASIC_TYPE(double, min_jerk, -3.0);
  MEMBER_BASIC_TYPE(int, jerkseg_num, 7);
  MEMBER_BASIC_TYPE(double, v_boundary_relax_ratio, 1.1);

  MEMBER_BASIC_TYPE(double, rss_ego_max_dec, -3.0);
  MEMBER_BASIC_TYPE(double, rss_obs_max_dec, -8.0);
  MEMBER_BASIC_TYPE(double, rss_action_delay, 1.0);
  MEMBER_BASIC_TYPE(double, rss_min_distane, 2.0);
  MEMBER_BASIC_TYPE(double, rss_ego_max_acc, 1.0);

  MEMBER_BASIC_TYPE(double, lat_min_distance, 0.1);
  MEMBER_BASIC_TYPE(double, lat_max_distance, 0.8);
  MEMBER_BASIC_TYPE(double, lat_distance_max_relative_speed, 20.0);
  MEMBER_BASIC_TYPE(double, lat_distance_min_relative_speed, 0.0);

  MEMBER_BASIC_TYPE(bool, using_bandwidth, true);
  MEMBER_BASIC_TYPE(double, v_bandwidth, 0.6);
  MEMBER_BASIC_TYPE(double, k_v0, 0.36);
  MEMBER_BASIC_TYPE(double, k_v1, 1.2);
  MEMBER_BASIC_TYPE(double, s_bandwidth, 0.6);
  MEMBER_BASIC_TYPE(double, k_s0, 0.216);
  MEMBER_BASIC_TYPE(double, k_s1, 1.08);
  MEMBER_BASIC_TYPE(double, k_s2, 1.8);
  MEMBER_BASIC_TYPE(double, lat_bandwidth, 0.1);
  MEMBER_BASIC_TYPE(double, k_l0, 0.001);
  MEMBER_BASIC_TYPE(double, k_l1, 0.03);
  MEMBER_BASIC_TYPE(double, k_l2, 0.3);
  MEMBER_BASIC_TYPE(double, weight_lon_track, 1.0);
  MEMBER_BASIC_TYPE(double, weight_lat_track, 30.0);
  MEMBER_BASIC_TYPE(double, weight_s, 0.1);
  MEMBER_BASIC_TYPE(double, weight_v, 0.1);
  MEMBER_BASIC_TYPE(double, weight_a, 0.1);
  MEMBER_BASIC_TYPE(double, weight_jerk, 0.1);
  MEMBER_BASIC_TYPE(double, weight_l, 0.1);
  MEMBER_BASIC_TYPE(double, weight_dl, 0.1);
  MEMBER_BASIC_TYPE(double, weight_ddl, 0.1);
  MEMBER_BASIC_TYPE(double, weight_dddl, 0.1);

  MEMBER_BASIC_TYPE(double, weight_obstacle, 0.1);

 public:
  void CalcParam() {
    if (!using_bandwidth_) {
      return;
    }
    k_s0_ = s_bandwidth_;
    k_s2_ = 3.0 * k_s0_;
    k_s0_ *= s_bandwidth_;
    k_s1_ = 3.0 * k_s0_;
    k_s0_ *= s_bandwidth_;

    k_v1_ = 2.0 * v_bandwidth_;
    k_v0_ = v_bandwidth_ * v_bandwidth_;

    k_l0_ = lat_bandwidth_;
    k_l2_ = 3.0 * k_l0_;
    k_l0_ *= lat_bandwidth_;
    k_l1_ = 3.0 * k_l0_;
    k_l0_ *= lat_bandwidth_;
  }
};

CONFIG_REGISTER(dp_tsl_interval_lattice_task, DpTslIntervalLatticeTaskParam);

}  // namespace pnd
}  // namespace trunk
