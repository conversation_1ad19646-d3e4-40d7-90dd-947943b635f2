// Copyright 2023, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {

class QpSplineConfig {
  MEMBER_BASIC_TYPE(size_t, number_of_discrete_graph_t, 0);
  MEMBER_BASIC_TYPE(int, spline_order, 0);
  MEMBER_BASIC_TYPE(double, speed_kernel_weight, 0.0);
  MEMBER_BASIC_TYPE(double, accel_kernel_weight, 0.0);
  MEMBER_BASIC_TYPE(double, jerk_kernel_weight, 0.0);
  MEMBER_BASIC_TYPE(double, follow_weight, 0.0);
  MEMBER_BASIC_TYPE(double, stop_weight, 0.0);
  MEMBER_BASIC_TYPE(double, cruise_weight, 0.0);
  MEMBER_BASIC_TYPE(double, regularization_weight, 0.0);
  MEMBER_BASIC_TYPE(double, time_headway, 0.0);
  MEMBER_BASIC_TYPE(double, follow_drag_distance, 17.0);
  MEMBER_BASIC_TYPE(double, dp_st_reference_weight, 0.0);
  MEMBER_BASIC_TYPE(double, init_jerk_kernel_weight, 0.0);
  MEMBER_BASIC_TYPE(double, yield_weight, 0.0);
  MEMBER_BASIC_TYPE(double, yield_drag_distance, 0.0);
};

class STBoundaryConfig {
  MEMBER_BASIC_TYPE(double, boundary_buffer, 0.1);
  MEMBER_BASIC_TYPE(double, back_min_buffer, 0.5);
  MEMBER_BASIC_TYPE(double, back_t, 1.0);
  MEMBER_BASIC_TYPE(double, lat_boundary_buffer, 0.5);
  MEMBER_BASIC_TYPE(double, minimal_kappa, 0.00001);
  MEMBER_BASIC_TYPE(double, point_extension, 1.0);
  MEMBER_BASIC_TYPE(double, lowest_speed, 2.5);
  MEMBER_BASIC_TYPE(int, num_points_to_avg_kappa, 2);
  MEMBER_BASIC_TYPE(double, static_obs_nudge_speed_ratio, 0.6);
  MEMBER_BASIC_TYPE(double, dynamic_obs_nudge_speed_ratio, 0.8);
  MEMBER_BASIC_TYPE(double, centri_jerk_speed_coeff, 1.0);
  MEMBER_BASIC_TYPE(double, stop_distance, 12.0);
  MEMBER_BASIC_TYPE(double, st_boundary_filtering_distance, 0.0);
  MEMBER_BASIC_TYPE(double, active_deceleration_reference, -1.0);
};

// 用于qp spline st和st mapper
class QpSpeedOptimizerParam {
  MEMBER_BASIC_TYPE(double, total_time, 0.0);
  MEMBER_BASIC_TYPE(double, total_path_length, 0.0);
  MEMBER_BASIC_TYPE(double, max_acceleration, 0.0);  // 最大加速能力
  MEMBER_BASIC_TYPE(double, min_deceleration, 0.0);  // 最大制动能力，值为负数
  MEMBER_BASIC_TYPE(double, preferred_max_acceleration, 0.0);
  MEMBER_BASIC_TYPE(double, preferred_min_deceleration, 0.0);
  MEMBER_BASIC_TYPE(double, front_vehicle_max_deceleration, -5.0);
  // rss model中前车减速度
  MEMBER_BASIC_TYPE(double, lc_relax_percent, 0.05);  // 超车时加速

  MEMBER_COMPLEX_TYPE(STBoundaryConfig, st_boundary_config);
  MEMBER_COMPLEX_TYPE(QpSplineConfig, qp_spline_config);
};

CONFIG_REGISTER(qp_speed_optimizer, QpSpeedOptimizerParam);

}  // namespace pnd
}  // namespace trunk
