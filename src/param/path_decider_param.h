#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {
class PathDeciderParam {
  MEMBER_BASIC_TYPE(double, lateral_ignore_buffer, 0.0);
  MEMBER_BASIC_TYPE(double, static_decision_nudge_l_buffer, 0.0);
  MEMBER_BASIC_TYPE(double, nudge_distance_obstacle, 0.0);
  MEMBER_BASIC_TYPE(bool, enable_nudge_decision, false);
  MEMBER_BASIC_TYPE(double, min_radius_stop_distance, 0.0);
};
CONFIG_REGISTER(path_decider, PathDeciderParam);
}  // namespace pnd
}  // namespace trunk
