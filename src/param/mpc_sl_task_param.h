//
// Created by rhine on 2023/11/29.
//

#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {

class MpcSlTaskParam {
  // cost param
  MEMBER_BASIC_TYPE(double, max_total_s, 200.0);
  MEMBER_COMPLEX_TYPE(std::vector<double>, w_tracking);
  MEMBER_COMPLEX_TYPE(std::vector<double>, wT_tracking);
  MEMBER_COMPLEX_TYPE(std::vector<double>, w_loopshaping);
  MEMBER_COMPLEX_TYPE(std::vector<double>, wT_loopshaping);
  MEMBER_COMPLEX_TYPE(std::vector<double>, w_adaptive_initial_state);
  MEMBER_BASIC_TYPE(bool, using_adaptive_initial_state, false);
  MEMBER_BASIC_TYPE(bool, using_soft_boundary, false);
  MEMBER_BASIC_TYPE(double, weight_soft_boundary, 100000.0);
  MEMBER_BASIC_TYPE(double, max_l_soft_boundary, 0.1);
  MEMBER_BASIC_TYPE(double, max_cputime, 0.0)
  MEMBER_BASIC_TYPE(int, max_iter_num, 1000);
};
CONFIG_REGISTER(mpc_sl_task, MpcSlTaskParam);

}  // namespace pnd
}  // namespace trunk
