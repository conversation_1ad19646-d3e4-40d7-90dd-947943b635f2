// Copyright 2023, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/config.h>

#include <vector>

namespace trunk {
namespace pnd {

class LaneFollowStageParam {
  MEMBER_COMPLEX_TYPE(std::vector<std::string>, task_list);
  // pre-process cost
  MEMBER_BASIC_TYPE(double, cost_attenuation_factor, 0.0);
  // in current lane
  MEMBER_BASIC_TYPE(double, lateral_safe_distance, 0.0);
  // overtake-start
  MEMBER_BASIC_TYPE(double, weight_overtake, 0.0);
  MEMBER_BASIC_TYPE(double, overtake_distance_min_time, 0.0);
  MEMBER_BASIC_TYPE(double, overtake_distance_max_time, 0.0);
  MEMBER_BASIC_TYPE(double, overtake_min_distance, 0.0);
  MEMBER_BASIC_TYPE(double, overtake_max_distance, 0.0);
  MEMBER_BASIC_TYPE(double, overtake_min_speed, 0.0);
  MEMBER_BASIC_TYPE(double, overtake_max_speed, 0.0);
  MEMBER_BASIC_TYPE(double, overtake_distance_exponential_weight, 0.0);
  // side collision risk
  MEMBER_BASIC_TYPE(double, side_collision_interval_time, 0.0);
  MEMBER_BASIC_TYPE(double, side_collision_interval_base, 0.0);
  MEMBER_BASIC_TYPE(double, side_collision_redundant_interval_time, 0.0);
  MEMBER_BASIC_TYPE(double, side_collision_redundant_speed_ratio, 0.0);
  MEMBER_BASIC_TYPE(double, side_collision_interval_hysteresis_length, 0.0);
  MEMBER_BASIC_TYPE(double, in_collision_interval_cost, 0.0);

  MEMBER_BASIC_TYPE(double, reference_line_static_obs_cost, 0.0);
  // post-process cost
  MEMBER_BASIC_TYPE(double, map_target_lane_cost, 0.0);
  MEMBER_BASIC_TYPE(double, lane_change_cost, 0.0);
  MEMBER_BASIC_TYPE(double, lane_change_cost_at_lane_change, 0.0);
  MEMBER_BASIC_TYPE(double, weight_total_time, 0.0);
  MEMBER_BASIC_TYPE(double, weight_total_arc_length, 0.0);

  MEMBER_BASIC_TYPE(bool, enable_cancel_lane_change, true);
  MEMBER_BASIC_TYPE(bool, enable_overtake, true);
  MEMBER_BASIC_TYPE(double, cancel_lane_change_speed_threshold, 0.0);
  MEMBER_BASIC_TYPE(double, cancel_lane_change_time_threshold, 0.0);
  MEMBER_BASIC_TYPE(double, cancel_lane_change_cost, 0.0);

  MEMBER_BASIC_TYPE(double, speed_dp_faliure_cost, 0.0);
  MEMBER_BASIC_TYPE(double, dec_cost, 0.0);
  MEMBER_BASIC_TYPE(double, trajectory_shape_cost, 0.0);
  MEMBER_BASIC_TYPE(double, paral_obs_cost, 0.0);
  MEMBER_BASIC_TYPE(double, overtake_ttc_threshold, 23.0);
  MEMBER_BASIC_TYPE(double, min_ttc_threshold, 13.0);

  MEMBER_BASIC_TYPE(double, continue_LC_wait_time, 5.0);
  MEMBER_BASIC_TYPE(double, cancel_LC_wait_time, 3.0);
};
class AuxiliaryLaneStageParam {
  MEMBER_COMPLEX_TYPE(std::vector<std::string>, task_list);
  // pre-process cost
  // in current lane
  MEMBER_BASIC_TYPE(double, lateral_safe_distance, 0.0);
  // side collision risk
  MEMBER_BASIC_TYPE(double, side_collision_interval_time, 0.0);
  MEMBER_BASIC_TYPE(double, side_collision_interval_base, 0.0);
  MEMBER_BASIC_TYPE(double, side_collision_redundant_interval_time, 0.0);
  MEMBER_BASIC_TYPE(double, side_collision_redundant_speed_ratio, 0.0);
  MEMBER_BASIC_TYPE(double, side_collision_interval_hysteresis_length, 0.0);

  MEMBER_BASIC_TYPE(double, min_ttc_threshold, 10);
};

class ScenarioLaneFollowParam {
  MEMBER_COMPLEX_TYPE(LaneFollowStageParam, lane_follow);
  MEMBER_COMPLEX_TYPE(AuxiliaryLaneStageParam, auxiliary_lane);
};
CONFIG_REGISTER(lane_follow, ScenarioLaneFollowParam);

}  // namespace pnd
}  // namespace trunk
