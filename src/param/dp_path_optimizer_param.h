// Copyright 2023, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {

class DpPathCostParam {
  MEMBER_BASIC_TYPE(double, path_l_cost_param_l0, 0.0);
  MEMBER_BASIC_TYPE(double, path_l_cost_param_b, 0.0);
  MEMBER_BASIC_TYPE(double, path_l_cost_param_k, 0.0);
  MEMBER_BASIC_TYPE(double, path_l_cost, 0.0);
  MEMBER_BASIC_TYPE(double, path_dl_cost, 0.0);
  MEMBER_BASIC_TYPE(double, path_ddl_cost, 0.0);
  MEMBER_BASIC_TYPE(double, path_end_l_cost, 0.0);
  MEMBER_BASIC_TYPE(double, static_decision_nudge_l_buffer, 0.0);
  MEMBER_BASIC_TYPE(double, obstacle_collision_cost, 0.0);
  MEMBER_BASIC_TYPE(double, obstacle_collision_distance, 0.0);
  MEMBER_BASIC_TYPE(double, obstacle_ignore_distance, 0.0);
  MEMBER_BASIC_TYPE(double, obstacle_risk_distance, 0.0);
};
class DpPathOptimizerParam {
  MEMBER_BASIC_TYPE(double, longit_sample_min_length, 0.0);
  MEMBER_BASIC_TYPE(double, longit_sample_step_time, 0.0);
  MEMBER_BASIC_TYPE(double, longit_sample_total_time, 0.0);
  // 采样相关参数
  MEMBER_BASIC_TYPE(double, sample_points_num_each_level, 0.0);
  MEMBER_BASIC_TYPE(double, sample_distance, 0.0);
  MEMBER_BASIC_TYPE(double, sample_time, 0.0);
  MEMBER_BASIC_TYPE(double, step_length_min, 20.0);
  MEMBER_BASIC_TYPE(double, step_length_max, 40.0);
  MEMBER_BASIC_TYPE(double, max_stop_speed, 0.0);
  MEMBER_BASIC_TYPE(double, path_resolution, 0.0);
  MEMBER_BASIC_TYPE(double, prediction_total_time, 0.0);
  MEMBER_BASIC_TYPE(double, eval_time_interval, 0.0);
  MEMBER_BASIC_TYPE(double, lateral_ignore_buffer, 0.0);
  MEMBER_BASIC_TYPE(double, sidepass_distance, 0.0);
  MEMBER_BASIC_TYPE(double, static_able_LC_speed_threshold, 0.0);
  MEMBER_BASIC_TYPE(double, solid_dp_width, 0.0);
  MEMBER_BASIC_TYPE(double, nudge_width, 0.0);
  MEMBER_BASIC_TYPE(double, max_lateral_distance, 0.0);
  MEMBER_COMPLEX_TYPE(DpPathCostParam, dp_path_cost_param);
};
CONFIG_REGISTER(dp_path_optimizer, DpPathOptimizerParam);

}  // namespace pnd
}  // namespace trunk
