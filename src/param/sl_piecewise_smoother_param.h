// Copyright 2023, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {

class SlPiecewiseSmootherParam {
  MEMBER_BASIC_TYPE(double, max_steering_angle_velocity, 0.01);
  MEMBER_BASIC_TYPE(double, boundary_0order_derivative, 0.5);
  MEMBER_BASIC_TYPE(double, ratio_max_curvature, 0.9);
  MEMBER_BASIC_TYPE(double, ds, 2.0);
  MEMBER_COMPLEX_TYPE(std::vector<double>, weight_norm);
  MEMBER_BASIC_TYPE(double, boundary_1order_derivative, 1.0);
  MEMBER_BASIC_TYPE(double, abs_tol, 1e-5);
  MEMBER_BASIC_TYPE(double, rel_tol, 1e-5);
  MEMBER_BASIC_TYPE(double, prim_infe_tol, 1e-4);
  MEMBER_BASIC_TYPE(double, dual_infe_tol, 1e-4);
};

CONFIG_REGISTER(sl_piecewise_smoother, SlPiecewiseSmootherParam);

}  // namespace pnd
}  // namespace trunk
