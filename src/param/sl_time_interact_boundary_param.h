// Copyright 2021, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/config.h>

#include <vector>

namespace trunk {
namespace pnd {

class SlTimeInteractBoundaryParam {
  MEMBER_BASIC_TYPE(bool, preview_only_lane_change, false);
  MEMBER_BASIC_TYPE(bool, using_relative_steering_angular_velocity, false);
  MEMBER_BASIC_TYPE(bool, using_relative_curvature, false);
  MEMBER_BASIC_TYPE(bool, mean_deviation, false);
  MEMBER_BASIC_TYPE(double, reference_lane_change_time, 6.0);
  MEMBER_BASIC_TYPE(double, reference_lane_change_prepare_time, 1.0);
  MEMBER_BASIC_TYPE(double, ds, 2.0);
  MEMBER_BASIC_TYPE(double, max_length, 160.0);
  MEMBER_BASIC_TYPE(double, dl_boundary, 0.5);
  MEMBER_BASIC_TYPE(double, ratio_max_curvature, 0.9);
  MEMBER_COMPLEX_TYPE(std::vector<double>, max_steering_angular_velocity_table);
  MEMBER_BASIC_TYPE(double, min_lateral_safe_distance, 0.2);
  MEMBER_BASIC_TYPE(double, end_delay, 2.0);
  MEMBER_BASIC_TYPE(double, start_delay, 2.0);
  MEMBER_BASIC_TYPE(double, nudge_condition_latertal_distance, 0.0);
  MEMBER_BASIC_TYPE(double, nudge_disp_time_headway, 3.0);
  MEMBER_BASIC_TYPE(double, nudge_disp_lat_dis, 0.1);
  MEMBER_COMPLEX_TYPE(std::vector<double>, speed_table);
  MEMBER_COMPLEX_TYPE(std::vector<double>, max_lateral_acc_table);
  MEMBER_COMPLEX_TYPE(std::vector<double>, lane_change_max_lateral_acc_table);
  MEMBER_COMPLEX_TYPE(std::vector<double>, relative_speed_table);
  MEMBER_COMPLEX_TYPE(std::vector<double>, nudge_lat_dis_table);
  MEMBER_COMPLEX_TYPE(std::vector<double>, nudge_cone_lat_dis_table);
};

CONFIG_REGISTER(sl_time_interact_boundary, SlTimeInteractBoundaryParam);

}  // namespace pnd
}  // namespace trunk
