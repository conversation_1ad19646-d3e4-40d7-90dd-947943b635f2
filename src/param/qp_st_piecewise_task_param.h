// Copyright 2024, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {

class QpStPiecewiseTaskParam {
  MEMBER_COMPLEX_TYPE(std::vector<double>, weight_norm);
  MEMBER_COMPLEX_TYPE(std::vector<double>, weight_ref);
  MEMBER_COMPLEX_TYPE(std::vector<double>, weight_dp);
  MEMBER_COMPLEX_TYPE(std::vector<double>, weight_end);
  MEMBER_BASIC_TYPE(double, total_time, 7.0);
  MEMBER_BASIC_TYPE(double, total_length, 150.0);
  MEMBER_BASIC_TYPE(double, dt, 0.2);
  MEMBER_BASIC_TYPE(double, max_jerk, 0.2);
  MEMBER_BASIC_TYPE(double, min_jerk, 0.2);
  MEMBER_BASIC_TYPE(double, max_acc, 3.0);
  MEMBER_BASIC_TYPE(double, max_dec, -5.0);
  MEMBER_BASIC_TYPE(double, rss_ego_max_dec, -3.0);
  MEMBER_BASIC_TYPE(double, rss_obs_max_dec, -8.0);
  MEMBER_BASIC_TYPE(double, rss_action_delay, 1.0);
  MEMBER_BASIC_TYPE(double, min_safe_distance, 5.0);
  MEMBER_BASIC_TYPE(double, lane_change_speed_relax_ratio, 0.05);
  MEMBER_BASIC_TYPE(double, abs_tol, 1e-5);
  MEMBER_BASIC_TYPE(double, rel_tol, 1e-5);
  MEMBER_BASIC_TYPE(double, prim_infe_tol, 1e-4);
  MEMBER_BASIC_TYPE(double, dual_infe_tol, 1e-4);
};

CONFIG_REGISTER(qp_st_piecewise_task, QpStPiecewiseTaskParam);

}  // namespace pnd
}  // namespace trunk
