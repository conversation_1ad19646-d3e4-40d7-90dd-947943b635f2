// Copyright 2023, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {

class CurvatureSmootherParam {
  MEMBER_BASIC_TYPE(double, max_steering_angle_velocity, 0.01);
  MEMBER_BASIC_TYPE(double, ratio_max_curvature, 0.9);
  MEMBER_BASIC_TYPE(double, ds, 2.0);
  MEMBER_BASIC_TYPE(int, prediction_points_number, 3);
};

CONFIG_REGISTER(curvature_smoother, CurvatureSmootherParam);

}  // namespace pnd
}  // namespace trunk
