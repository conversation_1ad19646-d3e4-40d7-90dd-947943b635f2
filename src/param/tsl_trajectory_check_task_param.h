// Copyright 2021, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {

class TslTrajectoryCheckTaskParam {
  MEMBER_BASIC_TYPE(double, max_steering_angular_velocity, 0.03);
  MEMBER_BASIC_TYPE(double, max_acceleration, 1.0);
  MEMBER_BASIC_TYPE(double, min_acceleration, -4.0);
  MEMBER_BASIC_TYPE(double, max_jerk, 3.0);
  MEMBER_BASIC_TYPE(double, min_jerk, -3.0);
  MEMBER_BASIC_TYPE(double, min_safe_distance, 3.0);
  MEMBER_BASIC_TYPE(double, rss_ego_max_dec, -3.0);
  MEMBER_BASIC_TYPE(double, rss_obs_max_dec, -8.0);
  MEMBER_BASIC_TYPE(double, rss_action_delay, 1.0);
  MEMBER_BASIC_TYPE(double, yield_distane, 2.0);
  MEMBER_BASIC_TYPE(double, max_follow_speed_diff, 2.0);
  MEMBER_BASIC_TYPE(double, max_nudge_distance, 1.8);
};

CONFIG_REGISTER(tsl_trajectory_check_task, TslTrajectoryCheckTaskParam);

}  // namespace pnd
}  // namespace trunk
