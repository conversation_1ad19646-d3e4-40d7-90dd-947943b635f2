// Copyright 2021, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/config.h>

#include <vector>

namespace trunk {
namespace pnd {

class QpSlPiecewiseTaskParam {
  // size=4, denote weight of L2 norm of i-th order derivative of l(s)
  MEMBER_COMPLEX_TYPE(std::vector<double>, weight_norm);
  // size=3, denote weight of end of square
  MEMBER_COMPLEX_TYPE(std::vector<double>, weight_end);
  // weight of Euclidean distance between l(s) and ref_path_data
  MEMBER_BASIC_TYPE(double, abs_tol, 1e-5);
  MEMBER_BASIC_TYPE(double, rel_tol, 1e-5);
  MEMBER_BASIC_TYPE(double, prim_infe_tol, 1e-4);
  MEMBER_BASIC_TYPE(double, dual_infe_tol, 1e-4);
};

CONFIG_REGISTER(qp_sl_piecewise_task, QpSlPiecewiseTaskParam);

}  // namespace pnd
}  // namespace trunk
