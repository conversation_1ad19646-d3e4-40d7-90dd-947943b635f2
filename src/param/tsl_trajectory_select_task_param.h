// Copyright 2021, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {

class TslTrajectorySelectTaskParam {
  MEMBER_BASIC_TYPE(double, weight_lat_terminal, 1.0);
  MEMBER_BASIC_TYPE(double, weight_lat_velocity, 0.0);
  MEMBER_BASIC_TYPE(double, weight_lat_acceleration, 0.0);
  MEMBER_BASIC_TYPE(double, weight_lat_jerk, 0.0);
  MEMBER_BASIC_TYPE(double, weight_lon_velocity, 1.0);
  MEMBER_BASIC_TYPE(double, weight_lon_acceleration, 0.01);
  MEMBER_BASIC_TYPE(double, weight_lon_jerk, 0.001);
  MEMBER_BASIC_TYPE(double, weight_obstacle_front_distance, 0.0);
  MEMBER_BASIC_TYPE(double, weight_obstacle_back_distance, 0.0);
  MEMBER_BASIC_TYPE(double, weight_obstacle_lateral_distance, 0.0);
  MEMBER_BASIC_TYPE(double, weight_obstacle_oblique_distance, 0.0);

  MEMBER_BASIC_TYPE(double, weight_lat, 1.0);
  MEMBER_BASIC_TYPE(double, weight_lon, 1.0);
  MEMBER_BASIC_TYPE(double, weight_obstacle, 1.0);
  MEMBER_BASIC_TYPE(double, weight_nudge, 2.0);
  MEMBER_BASIC_TYPE(double, weight_follow, 1.0);
};

CONFIG_REGISTER(tsl_trajectory_select_task, TslTrajectorySelectTaskParam);

}  // namespace pnd
}  // namespace trunk
