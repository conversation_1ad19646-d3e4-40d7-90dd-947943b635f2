// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {

// 用于speed limit decider
class StGraphParam {
  // param
  MEMBER_BASIC_TYPE(double, static_obs_nudge_speed_ratio, 0.0);
  MEMBER_BASIC_TYPE(double, dynamic_obs_nudge_speed_ratio, 0.0);
  MEMBER_BASIC_TYPE(double, centri_jerk_speed_coeff, 0.0);
  MEMBER_BASIC_TYPE(double, minimal_kappa, 0.0);
  MEMBER_BASIC_TYPE(int, num_points_to_avg_kappa, 0.0);
  MEMBER_BASIC_TYPE(double, lowest_speed, 0.0);
  MEMBER_BASIC_TYPE(bool, enable_nudge_slowdown, true);
  MEMBER_BASIC_TYPE(double, high_speed_centric_acceleration_limit, 0.8)
  MEMBER_BASIC_TYPE(double, low_speed_centric_acceleration_limit, 1.2)
  MEMBER_BASIC_TYPE(double, high_speed_threshold, 12.58)
  MEMBER_BASIC_TYPE(double, low_speed_threshold, 7.5)
};
CONFIG_REGISTER(st_graph, StGraphParam);

}  // namespace pnd
}  // namespace trunk
