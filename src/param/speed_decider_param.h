#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {

class SpeedDeciderParam {
  MEMBER_BASIC_TYPE(double, min_stop_distance_obstacle, 6.0);
  MEMBER_BASIC_TYPE(double, follow_min_obs_lateral_distance, 2.5);
  MEMBER_BASIC_TYPE(double, follow_min_time_sec, 0.1);
  MEMBER_BASIC_TYPE(double, follow_time_buffer, 2.5);
  MEMBER_BASIC_TYPE(double, follow_min_distance, 3.0);
  MEMBER_BASIC_TYPE(double, yield_distance_pedestrian_bycicle, 5.0);
  MEMBER_BASIC_TYPE(double, yield_distance, 3.0);
};

CONFIG_REGISTER(speed_decider, SpeedDeciderParam);

}  // namespace pnd
}  // namespace trunk
