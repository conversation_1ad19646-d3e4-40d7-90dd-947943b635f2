// Copyright 2024, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {

class TtcFcwTaskParam {
  MEMBER_BASIC_TYPE(double, ttc_level_1, 3.0);
  MEMBER_BASIC_TYPE(double, ttc_level_2, 2.0);
  MEMBER_BASIC_TYPE(double, ttc_level_3, 1.0);
  MEMBER_BASIC_TYPE(double, valid_speed, 1.0);  // m/s
  MEMBER_BASIC_TYPE(bool, enable_aeb, true);
  MEMBER_BASIC_TYPE(double, aeb_dec, -3.0);
};

CONFIG_REGISTER(ttc_fcw, TtcFcwTaskParam);

}  // namespace pnd
}  // namespace trunk
