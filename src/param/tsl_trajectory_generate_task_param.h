// Copyright 2021, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {

class TslTrajectoryGenerateTaskParam {
  MEMBER_BASIC_TYPE(double, dt, 0.1);
  MEMBER_BASIC_TYPE(double, min_t, 7.0);
  MEMBER_BASIC_TYPE(double, max_t, 7.0);
  MEMBER_BASIC_TYPE(double, step_t, 2.0);
  MEMBER_BASIC_TYPE(int, v_sample_num, 13);
  MEMBER_BASIC_TYPE(double, min_l, -1.5);
  MEMBER_BASIC_TYPE(double, max_l, 1.5);
  MEMBER_BASIC_TYPE(double, step_l, 0.5);
};

CONFIG_REGISTER(tsl_trajectory_generate_task, TslTrajectoryGenerateTaskParam);

}  // namespace pnd
}  // namespace trunk
