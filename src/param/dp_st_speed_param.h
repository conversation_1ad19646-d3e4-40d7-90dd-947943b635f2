//
// Created by rhine on 2023/11/29.
//

#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {

class DpStSpeedOptimizerParam {
  // cost param
  MEMBER_BASIC_TYPE(double, default_speed_cost, 0.0);
  MEMBER_BASIC_TYPE(double, default_obstacle_cost, 0.0);
  MEMBER_BASIC_TYPE(double, obstacle_weight, 0.0);
  MEMBER_BASIC_TYPE(double, reference_weight, 0.0);
  MEMBER_BASIC_TYPE(double, reserved1, 0.0);
  MEMBER_BASIC_TYPE(double, reserved2, 0.0);

  // param
  MEMBER_BASIC_TYPE(double, positive_jerk_coeff, 0.0);
  MEMBER_BASIC_TYPE(double, negative_jerk_coeff, 0.0);
  MEMBER_BASIC_TYPE(double, max_acceleration, 0.0);
  MEMBER_BASIC_TYPE(double, max_deceleration, 0.0);
  MEMBER_BASIC_TYPE(double, accel_penalty, 0.0);
  MEMBER_BASIC_TYPE(double, decel_penalty, 0.0);
  MEMBER_BASIC_TYPE(double, exceed_speed_penalty, 0.0);
  MEMBER_BASIC_TYPE(double, low_speed_penalty, 0.0);
  MEMBER_BASIC_TYPE(double, keep_clear_low_speed_penalty, 0.0);
  MEMBER_BASIC_TYPE(int, matrix_dimension_t, 0);
  MEMBER_BASIC_TYPE(int, matrix_dimension_s, 0);
  MEMBER_BASIC_TYPE(double, total_path_length, 0.0);
  MEMBER_BASIC_TYPE(double, reserved3, 0.0);
  MEMBER_BASIC_TYPE(double, reserved4, 0.0);
  MEMBER_BASIC_TYPE(double, reserved5, 0.0);
  MEMBER_BASIC_TYPE(double, reserved6, 0.0);
  MEMBER_BASIC_TYPE(double, reserved7, 0.0);
  MEMBER_BASIC_TYPE(double, reserved8, 0.0);

  MEMBER_BASIC_TYPE(double, total_time, 0.0);
  MEMBER_BASIC_TYPE(double, reserved9, 0.0);
};
CONFIG_REGISTER(dp_st_speed_optimizer, DpStSpeedOptimizerParam);

}  // namespace pnd
}  // namespace trunk
