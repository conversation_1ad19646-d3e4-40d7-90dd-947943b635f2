// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/config.h>

#include <vector>

namespace trunk {
namespace pnd {

class MpcStTaskParam {
  // cost param
  MEMBER_BASIC_TYPE(double, dt, 0.1);
  MEMBER_BASIC_TYPE(double, total_time, 7.0);
  MEMBER_BASIC_TYPE(double, total_length, 180.0);
  MEMBER_BASIC_TYPE(double, lane_change_speed_relax_ratio, 0.05);
  MEMBER_COMPLEX_TYPE(std::vector<double>, w_tracking);
  MEMBER_COMPLEX_TYPE(std::vector<double>, wT_tracking);
  MEMBER_COMPLEX_TYPE(std::vector<double>, w_loopshaping);
  MEMBER_COMPLEX_TYPE(std::vector<double>, wT_loopshaping);
  MEMBER_BASIC_TYPE(double, v_boundary_relax_ratio, 0.1);
  MEMBER_BASIC_TYPE(double, max_jerk, 2.0);
  MEMBER_BASIC_TYPE(double, min_jerk, -2.0);
  MEMBER_BASIC_TYPE(double, max_acc, 1.3);
  MEMBER_BASIC_TYPE(double, min_acc, -4.0);
  MEMBER_BASIC_TYPE(double, min_safe_distance, 3.0);
  MEMBER_BASIC_TYPE(double, follow_speed_weight, 0.5);
  MEMBER_BASIC_TYPE(double, rss_ego_max_dec, -3.0);
  MEMBER_BASIC_TYPE(double, rss_obs_max_dec, -8.0);
  MEMBER_BASIC_TYPE(double, rss_action_delay, 1.0);
  MEMBER_BASIC_TYPE(double, yield_safe_distance, 3.0);
  MEMBER_BASIC_TYPE(bool, enable_time_headway, true);
  MEMBER_BASIC_TYPE(int, max_iter_num, 1000);
  MEMBER_BASIC_TYPE(double, max_cputime, 0.0)
};

CONFIG_REGISTER(mpc_st_task, MpcStTaskParam);

}  // namespace pnd
}  // namespace trunk
