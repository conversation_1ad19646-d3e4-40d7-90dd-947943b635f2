// Copyright 2023, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/config.h>

namespace trunk {
namespace pnd {

class FemPosDeviationSmootherParam {
  MEMBER_BASIC_TYPE(double, weight_smooth, 0.0);
  MEMBER_BASIC_TYPE(double, weight_length, 0.0);
  MEMBER_BASIC_TYPE(double, weight_similarity, 0.0);
  MEMBER_BASIC_TYPE(double, x_variation, 0.0);
  MEMBER_BASIC_TYPE(double, y_variation, 0.0);
  MEMBER_BASIC_TYPE(double, ds, 2.0);
  MEMBER_BASIC_TYPE(double, kappa_compensation_ratio, 1.1);
  MEMBER_BASIC_TYPE(double, kappa_compensation_bias, 0.001);
  MEMBER_BASIC_TYPE(double, ratio_max_curvature, 0.9);
};

CONFIG_REGISTER(fem_pos_deviation_smoother, FemPosDeviationSmootherParam);

}  // namespace pnd
}  // namespace trunk
