// Copyright 2021, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/config.h>

#include <vector>

namespace trunk {
namespace pnd {

class QpSlSplineTaskParam {
  MEMBER_BASIC_TYPE(uint32_t, spline_order, 5);
  MEMBER_COMPLEX_TYPE(std::vector<double>, weight_norm);
  MEMBER_BASIC_TYPE(double, weight_regular, 0.0);
};

CONFIG_REGISTER(qp_sl_spline_task, QpSlSplineTaskParam);

}  // namespace pnd
}  // namespace trunk
