// Copyright 2023, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/config.h>

#include <vector>

namespace trunk {
namespace pnd {

class SysParam {
  MEMBER_BASIC_TYPE(double, main_loop_frequency, 5.0);
  MEMBER_BASIC_TYPE(bool, with_trailer, true);
  MEMBER_BASIC_TYPE(bool, enable_manual_steering_reset, true);
  MEMBER_BASIC_TYPE(bool, enable_multithreading, true);
  MEMBER_COMPLEX_TYPE(std::vector<std::string>, traffic_rules);
  MEMBER_COMPLEX_TYPE(std::vector<std::string>, scenarios);
  MEMBER_BASIC_TYPE(std::string, smoother, "");
  MEMBER_BASIC_TYPE(double, config_speed_limit, 80.0);  // km/h
  MEMBER_BASIC_TYPE(double, static_obstacle_velocity_threshold, 3.0);
  MEMBER_BASIC_TYPE(double, time_resolution, 0.0);
  MEMBER_BASIC_TYPE(double, stitch_time, 0.0);
  MEMBER_BASIC_TYPE(double, path_stitch_time, 2.0);
  MEMBER_BASIC_TYPE(double, ref_delay_dis, 4.0);
  MEMBER_BASIC_TYPE(double, replan_lat_err_thres, 1.0);
  MEMBER_BASIC_TYPE(double, replan_lon_err_thres, 5.0);
  MEMBER_BASIC_TYPE(double, ref_line_front_length, 300.0);
  MEMBER_BASIC_TYPE(double, ref_line_back_length, 30.0);
  MEMBER_BASIC_TYPE(double, lane_change_lat_thres, 0.5);
  MEMBER_BASIC_TYPE(double, lane_change_lon_thres, -10.0);
  MEMBER_BASIC_TYPE(double, turn_signal_lat_thres, 0.6);
  MEMBER_BASIC_TYPE(double, expand_vehicle_width, 0.0);
  MEMBER_BASIC_TYPE(double, section_nearest_point_dis, 15.0);
  MEMBER_BASIC_TYPE(double, ramp_predeceleration_distance, 100.0);
  MEMBER_BASIC_TYPE(double, ramp_estimate_speed_limit, 35.0);
  MEMBER_BASIC_TYPE(double, turn_state_delay, 2.0);
  MEMBER_COMPLEX_TYPE(std::vector<double>, reserved_float);
  MEMBER_COMPLEX_TYPE(std::vector<int>, reserved_int);
};

CONFIG_REGISTER(sys, SysParam);

}  // namespace pnd
}  // namespace trunk
