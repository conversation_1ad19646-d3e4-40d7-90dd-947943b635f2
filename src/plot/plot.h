
#pragma once
#include <port/obstacle.h>
#include <port/sl/path_data.h>
#include <port/sl/path_point.h>
#include <port/st/speed_data.h>
#include <port/st/st_boundary.h>

#include <unordered_map>
#include <vector>

#ifdef PLOT
#include "plot/matplotlibcpp.h"
namespace plt = matplotlibcpp;
#endif

// 如果绘制报错 可以排查一下matplibplot 的版本 (matplotlib==3.1.2 可用)

/*
1. 实例化  plot::Plot plt

2. 绘图函数调用

3. 显示 Show()

*/

namespace trunk {
namespace pnd {
namespace plot {

class Plot {
 public:
  Plot();
  Plot(int num) : num_(num) { Init(); };
  ~Plot() = default;
  void plotData(const std::vector<double>& x, const std::vector<double>& y,
                const std::string& color, const std::string& label);
  template <typename T>
  void plotPath(const T& path_data, const std::string& color,
                const std::string& label) {
#ifdef PLOT
    std::vector<double> x, y;
    for (const auto& point : path_data) {
      x.push_back(point.s());
      y.push_back(point.l());
    }

    std::unordered_map<std::string, std::string> keywords;
    keywords["color"] = color;
    keywords["label"] = label;
    // plt::subplot(num_, 1, 1);
    plt::plot(x, y, keywords);
    plt::xlabel("s");
    plt::ylabel("l");
#endif
  };
  void plotDynameObs(const tport::Contour2D& obstacle_contour,
                     const std::string& color);
  void initNum(const int& num);
  void plotSTBoundary(const port::STBoundary& st_boundary,
                      const std::string& color);

  // Path DP visualization methods
  void plotSamplePoints(
      const std::vector<std::vector<tport::SLPoint>>& sample_points,
      const std::string& color = "blue", const std::string& marker = "o");
  void plotSelectedPath(const std::vector<tport::SLPoint>& selected_path,
                        const std::string& color = "red",
                        const std::string& label = "Selected Path");
  void plotPathWithCost(const std::vector<tport::SLPoint>& path,
                        const std::vector<double>& costs,
                        const std::string& label = "Path with Cost");
  void plotObstacles(const port::IndexedObstacles& obstacles,
                     const std::string& color = "black");
  void Show();

 private:
  void Init();
  int num_ = 1;
  int current_num_ = 1;
};
}  // namespace plot
}  // namespace pnd
}  // namespace trunk
