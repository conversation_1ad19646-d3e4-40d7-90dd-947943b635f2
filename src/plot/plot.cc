#include "plot/plot.h"

namespace trunk {
namespace pnd {
namespace plot {

Plot::Plot() {}
void Plot::initNum(const int& num) {
  num_ = num;
  Init();
#ifdef PLOT
  plt::clf();  // 清除之前画的图
#endif
}

void Plot::Init() {
#ifdef PLOT
  // plt::backend("TkAgg");
  plt::clf();         // 清除上一帧的数据
  plt::ion();         // 加这句为了动态画图
  plt::figure(num_);  // 初始化图片数量
#endif
}
void Plot::Show() {
#ifdef PLOT
  plt::legend();
  plt::pause(0.00000001);
  plt::show(false);  // 非阻塞绘图
  plt::show(false);
  plt::clf();
#endif
}

void Plot::plotData(const std::vector<double>& x, const std::vector<double>& y,
                    const std::string& color, const std::string& label) {
#ifdef PLOT
  std::unordered_map<std::string, std::string> keywords;
  keywords["color"] = color;
  keywords["label"] = label;
  // plt::subplot(2, 1, 1);
  plt::plot(x, y, keywords);
  plt::legend();
  plt::xlabel("x");
  plt::ylabel("y");
#endif
}

// void Plot::plotPath(const T& path_data, const std::string& color,
//                     const std::string& label) {
//   // plt::title(title);
//   std::vector<double> x, y;
//   for (const auto& point : path_data) {
//     x.push_back(point.s());
//     y.push_back(point.l());
//   }

//   std::map<std::string, std::string> keywords;
//   keywords["color"] = color;
//   keywords["label"] = "path";
//   plt::subplot(num_, 1, current_num_++);  // 绘制子图， 可以注释掉
//   plt::plot(x, y, keywords);
//   plt::legend();
//   plt::xlabel("s");
//   plt::ylabel("l");
// }

void Plot::plotDynameObs(const tport::Contour2D& obstacle_contour,
                         const std::string& color) {
#ifdef PLOT
  std::vector<double> x, y;
  for (const auto& point : obstacle_contour) {
    x.push_back(point.x());
    y.push_back(point.y());
  }
  std::unordered_map<std::string, std::string> keywords;
  keywords["color"] = color;
  keywords["label"] = "obs";
  // plt::subplot(2, 1, 1);
  plt::plot(x, y, keywords);
#endif
}

void Plot::plotSTBoundary(const port::STBoundary& st_boundary,
                          const std::string& color) {
#ifdef PLOT
  std::vector<double> x, y;
  for (const auto upper_point : st_boundary.upper_points()) {
    x.push_back(upper_point.t());
    y.push_back(upper_point.s());
  }
  for (int i = st_boundary.lower_points().size() - 1; i >= 0; --i) {
    x.push_back(st_boundary.lower_points()[i].t());
    y.push_back(st_boundary.lower_points()[i].s());
  }
  if (!x.empty() && !y.empty()) {
    x.push_back(x.front());
    y.push_back(y.front());
  }
  std::unordered_map<std::string, std::string> keywords;
  keywords["color"] = color;
  keywords["label"] = "S-T";
  // plt::subplot(num_, 1, 2);
  plt::plot(x, y, keywords);
  // if (PyErr_Occurred()) {
  //   PyErr_Print();  // 打印 Python 端的错误
  //   return;
  // }
#endif
}

// Path DP visualization methods implementation
void Plot::plotSamplePoints(
    const std::vector<std::vector<tport::SLPoint>>& sample_points,
    const std::string& color, const std::string& marker) {
#ifdef PLOT
  for (size_t level = 0; level < sample_points.size(); ++level) {
    std::vector<double> x, y;
    for (const auto& point : sample_points[level]) {
      x.push_back(point.s());
      y.push_back(point.l());
    }
    if (!x.empty()) {
      std::unordered_map<std::string, std::string> keywords;
      keywords["color"] = color;
      keywords["marker"] = marker;
      keywords["linestyle"] = "None";
      keywords["markersize"] = "3";
      keywords["alpha"] = "0.6";
      plt::plot(x, y, keywords);
    }
  }
  plt::xlabel("s (m)");
  plt::ylabel("l (m)");
#endif
}

void Plot::plotSelectedPath(const std::vector<tport::SLPoint>& selected_path,
                            const std::string& color,
                            const std::string& label) {
#ifdef PLOT
  std::vector<double> x, y;
  for (const auto& point : selected_path) {
    x.push_back(point.s());
    y.push_back(point.l());
  }
  if (!x.empty()) {
    std::unordered_map<std::string, std::string> keywords;
    keywords["color"] = color;
    keywords["label"] = label;
    keywords["linewidth"] = "3";
    plt::plot(x, y, keywords);
  }
  plt::xlabel("s (m)");
  plt::ylabel("l (m)");
#endif
}

void Plot::plotPathWithCost(const std::vector<tport::SLPoint>& path,
                            const std::vector<double>& costs,
                            const std::string& label) {
#ifdef PLOT
  if (path.size() != costs.size()) {
    return;
  }

  std::vector<double> x, y, colors;
  for (size_t i = 0; i < path.size(); ++i) {
    x.push_back(path[i].s());
    y.push_back(path[i].l());
    colors.push_back(costs[i]);
  }

  if (!x.empty()) {
    // Use scatter plot with color mapping for cost visualization
    plt::scatter_colored(x, y, colors, 50.0);
    plt::xlabel("s (m)");
    plt::ylabel("l (m)");
  }
#endif
}

void Plot::plotObstacles(const port::IndexedObstacles& obstacles,
                         const std::string& color) {
#ifdef PLOT
  for (const auto* obstacle : obstacles.Items()) {
    const auto& sl_boundary = obstacle->sl_boundary();

    // Plot obstacle as rectangle in SL coordinate
    std::vector<double> x = {sl_boundary.min_s(), sl_boundary.max_s(),
                             sl_boundary.max_s(), sl_boundary.min_s(),
                             sl_boundary.min_s()};
    std::vector<double> y = {sl_boundary.min_l(), sl_boundary.min_l(),
                             sl_boundary.max_l(), sl_boundary.max_l(),
                             sl_boundary.min_l()};

    std::unordered_map<std::string, std::string> keywords;
    keywords["color"] = color;
    keywords["alpha"] = "0.3";
    keywords["linewidth"] = "2";
    plt::plot(x, y, keywords);
  }
#endif
}
}  // namespace plot
}  // namespace pnd
}  // namespace trunk
