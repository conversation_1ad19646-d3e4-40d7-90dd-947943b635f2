// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include "Eigen/Core"
#include "log.h"

namespace trunk {
namespace pnd {
namespace solver {

class AffineConstraint {
 public:
  AffineConstraint() = default;
  explicit AffineConstraint(bool is_equality);
  explicit AffineConstraint(const Eigen::MatrixXd& constraint_matrix,
                            const Eigen::MatrixXd& constraint_boundary,
                            bool is_equality);

  void SetIsEquality(const bool is_equality);

  const Eigen::MatrixXd& constraint_matrix() const;
  const Eigen::MatrixXd& constraint_boundary() const;
  bool AddConstraint(const Eigen::MatrixXd& constraint_matrix,
                     const Eigen::MatrixXd& constraint_boundary);

 private:
  Eigen::MatrixXd constraint_matrix_;
  Eigen::MatrixXd constraint_boundary_;
  bool is_equality_ = true;
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
