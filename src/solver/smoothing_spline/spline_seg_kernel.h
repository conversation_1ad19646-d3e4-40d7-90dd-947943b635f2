// Copyright 2023, trunk Inc. All rights reserved

/**
 * @file : spline_seg_kernel.h
 * @brief: generating integrated kernels for smoothing spline
 *
 *           x' P x  = int_0 ^x  (f(x)^(k))^2 dx, k = 0, 1, 2, 3
 *           P is the kernel of k-th smooth kernel
 **/

#pragma once

#include <string>
#include "Eigen/Core"

namespace trunk {
namespace pnd {
namespace solver {

class SplineSegKernel {
 public:
  SplineSegKernel();
  // generating kernel matrix
  Eigen::MatrixXd Kernel(uint32_t num_params, double accumulated_x);

  // only support N <= 3 cases
  Eigen::MatrixXd NthDerivativeKernel(uint32_t n, uint32_t num_params,
                                      double accumulated_x);

  static SplineSegKernel* Instance() {
    static SplineSegKernel instance;
    return &instance;
  };

 private:
  Eigen::MatrixXd DerivativeKernel(uint32_t num_of_params,
                                   double accumulated_x);
  Eigen::MatrixXd SecondOrderDerivativeKernel(uint32_t num_of_params,
                                              double accumulated_x);
  Eigen::MatrixXd ThirdOrderDerivativeKernel(uint32_t num_of_params,
                                             double accumulated_x);

  void IntegratedTermMatrix(uint32_t num_of_params, double x,
                            const std::string& type,
                            Eigen::MatrixXd* term_matrix) const;
  void CalculateFx(uint32_t num_of_params);
  void CalculateDerivative(uint32_t num_of_params);
  void CalculateSecondOrderDerivative(uint32_t num_of_params);
  void CalculateThirdOrderDerivative(uint32_t num_of_params);

  const uint32_t reserved_order_ = 5;
  Eigen::MatrixXd kernel_fx_;
  Eigen::MatrixXd kernel_derivative_;
  Eigen::MatrixXd kernel_second_order_derivative_;
  Eigen::MatrixXd kernel_third_order_derivative_;
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
