// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <osqp/osqp.h>
#include "spline_1d_solver.h"

namespace trunk {
namespace pnd {
namespace solver {

class OsqpSpline1dSolver : public Spline1dSolver {
 public:
  OsqpSpline1dSolver(const std::vector<double>& x_knots, uint32_t order);
  virtual ~OsqpSpline1dSolver();

  bool Solve() override;

  void CleanUp();

  void ResetOsqp();

 private:
  OSQPSettings* settings_ = nullptr;
  OSQPWorkspace* work_ = nullptr;  // Workspace
  OSQPData* data_ = nullptr;       // OSQPData
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
