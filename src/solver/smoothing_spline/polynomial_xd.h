// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <cassert>
#include <cinttypes>
#include <vector>

namespace trunk {
namespace pnd {
namespace solver {

class PolynomialXd {
 public:
  PolynomialXd() = default;
  explicit PolynomialXd(std::uint32_t order);
  explicit PolynomialXd(const std::vector<double>& params);
  double operator()(double value) const;
  double operator[](std::uint32_t index) const;
  void SetParams(const std::vector<double>& params);

  static PolynomialXd DerivedFrom(const PolynomialXd& base);
  static PolynomialXd IntegratedFrom(const PolynomialXd& base,
                                     double intercept = 0.0);

  std::uint32_t order() const;
  const std::vector<double>& params() const;

 private:
  std::vector<double> params_;
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
