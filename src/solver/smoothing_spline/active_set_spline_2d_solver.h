// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <qpOASES.hpp>

#include <memory>
#include <vector>
#include "spline_2d.h"
#include "spline_2d_constraint.h"
#include "spline_2d_kernel.h"
#include "spline_2d_solver.h"

namespace trunk {
namespace pnd {
namespace solver {

class ActiveSetSpline2dSolver final : public Spline2dSolver {
 public:
  ActiveSetSpline2dSolver(const std::vector<double>& t_knots, uint32_t order);

  void Reset(const std::vector<double>& t_knots, uint32_t order) override;

  // customize setup
  Spline2dConstraint* mutable_constraint() override;
  Spline2dKernel* mutable_kernel() override;
  Spline2d* mutable_spline() override;

  // solve
  bool Solve() override;

  // extract
  const Spline2d& spline() const override;

 private:
  std::unique_ptr<::qpOASES::SQProblem> sqp_solver_;

  int last_num_constraint_ = 0;
  int last_num_param_ = 0;
  bool last_problem_success_ = false;
};

}  // namespace util
}  // namespace pnd
}  // namespace trunk
