// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <vector>

#include <osqp/osqp.h>

#include "spline_2d.h"
#include "spline_2d_solver.h"

namespace trunk {
namespace pnd {
namespace solver {

class OsqpSpline2dSolver final : public Spline2dSolver {
 public:
  OsqpSpline2dSolver(const std::vector<double>& t_knots, uint32_t order);

  void Reset(const std::vector<double>& t_knots, uint32_t order) override;

  // customize setup
  Spline2dConstraint* mutable_constraint() override;
  Spline2dKernel* mutable_kernel() override;
  Spline2d* mutable_spline() override;

  // solve
  bool Solve() override;

  // extract
  const Spline2d& spline() const override;
  // Dense Matrix To Compressed Sparse Column Matrix
  template <typename T, int M, int N, typename D>
  void DenseToCSCMatrix(const Eigen::Matrix<T, M, N>& dense_matrix,
                        std::vector<T>* data, std::vector<D>* indices,
                        std::vector<D>* indptr) {
    constexpr double epsilon = 1e-9;
    int data_count = 0;
    for (int c = 0; c < dense_matrix.cols(); ++c) {
      indptr->emplace_back(data_count);
      for (int r = 0; r < dense_matrix.rows(); ++r) {
        if (std::fabs(dense_matrix(r, c)) < epsilon) {
          continue;
        }
        data->emplace_back(dense_matrix(r, c));
        ++data_count;
        indices->emplace_back(r);
      }
    }
    indptr->emplace_back(data_count);
  }

 private:
  OSQPSettings* settings_ = nullptr;
  OSQPWorkspace* work_ = nullptr;  // Workspace
  OSQPData* data_ = nullptr;       // OSQPData

  int last_num_constraint_ = 0;
  int last_num_param_ = 0;
  bool last_problem_success_ = false;
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
