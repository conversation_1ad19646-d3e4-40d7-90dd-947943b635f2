// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <utility>
#include <vector>

#include "polynomial_xd.h"

namespace trunk {
namespace pnd {
namespace solver {

class Spline2dSeg {
 public:
  // order represent the number of parameters (not the highest order);
  explicit Spline2dSeg(uint32_t order);
  explicit Spline2dSeg(const std::vector<double>& x_param,
                       const std::vector<double>& y_param);
  ~Spline2dSeg() = default;

  bool SetParams(const std::vector<double>& x_param,
                 const std::vector<double>& y_param);

  std::pair<double, double> operator()(double t) const;
  double x(double t) const;
  double y(double t) const;
  double DerivativeX(double t) const;
  double DerivativeY(double t) const;
  double SecondDerivativeX(double t) const;
  double SecondDerivativeY(double t) const;
  double ThirdDerivativeX(double t) const;
  double ThirdDerivativeY(double t) const;

  const PolynomialXd& spline_func_x() const;
  const PolynomialXd& spline_func_y() const;
  const PolynomialXd& DerivativeX() const;
  const PolynomialXd& DerivativeY() const;
  const PolynomialXd& SecondDerivativeX() const;
  const PolynomialXd& SecondDerivativeY() const;
  const PolynomialXd& ThirdDerivativeX() const;
  const PolynomialXd& ThirdDerivativeY() const;

 private:
  PolynomialXd spline_func_x_;
  PolynomialXd spline_func_y_;
  PolynomialXd derivative_x_;
  PolynomialXd derivative_y_;
  PolynomialXd second_derivative_x_;
  PolynomialXd second_derivative_y_;
  PolynomialXd third_derivative_x_;
  PolynomialXd third_derivative_y_;
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
