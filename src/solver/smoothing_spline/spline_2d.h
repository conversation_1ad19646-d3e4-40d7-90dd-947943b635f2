// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <utility>
#include <vector>

#include "Eigen/Core"

#include "polynomial_xd.h"
#include "spline_2d_seg.h"

namespace trunk {
namespace pnd {
namespace solver {

class Spline2d {
 public:
  Spline2d(const std::vector<double>& t_knots, uint32_t order);
  std::pair<double, double> operator()(double t) const;
  double x(double t) const;
  double y(double t) const;
  double DerivativeX(double t) const;
  double DerivativeY(double t) const;
  double SecondDerivativeX(double t) const;
  double SecondDerivativeY(double t) const;
  double ThirdDerivativeX(double t) const;
  double ThirdDerivativeY(double t) const;
  bool set_splines(const Eigen::MatrixXd& params, uint32_t order);
  const Spline2dSeg& smoothing_spline(uint32_t index) const;
  const std::vector<double>& t_knots() const;
  uint32_t spline_order() const;

 private:
  uint32_t find_index(double x) const;

 private:
  std::vector<Spline2dSeg> splines_;
  std::vector<double> t_knots_;
  uint32_t spline_order_;
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
