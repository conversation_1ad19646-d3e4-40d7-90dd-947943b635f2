// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <vector>

#include "Eigen/Core"
#include "port/frame.h"
#include "spline_2d.h"

namespace trunk {
namespace pnd {
namespace solver {

class Spline2dKernel {
 public:
  Spline2dKernel(const std::vector<double>& t_knots, uint32_t spline_order);

  // customized input output
  void AddRegularization(double regularization_param);
  bool AddKernel(const Eigen::MatrixXd& kernel, const Eigen::MatrixXd& offset,
                 double weight);
  bool AddKernel(const Eigen::MatrixXd& kernel, double weight);

  Eigen::MatrixXd* mutable_kernel_matrix();
  Eigen::MatrixXd* mutable_offset();

  const Eigen::MatrixXd kernel_matrix() const;
  const Eigen::MatrixXd offset() const;

  // build-in kernel methods
  void AddDerivativeKernelMatrix(double weight);
  void AddSecondOrderDerivativeMatrix(double weight);
  void AddThirdOrderDerivativeMatrix(double weight);

  // reference line kernel, x_coord in strictly increasing order
  //  bool AddReferenceLineKernelMatrix(
  //      const std::vector<double>& t_coord,
  //      const std::vector<tport::Point2D>& ref_points, double weight);
  bool AddReferenceLineKernelMatrix(
      const std::vector<double>& t_coord,
      const std::vector<tport::PathPoint>& ref_points, double weight);

 private:
  void AddNthDerivativeKernelMatrix(uint32_t n, double weight);
  uint32_t find_index(double x) const;

 private:
  Eigen::MatrixXd kernel_matrix_;
  Eigen::MatrixXd offset_;
  std::vector<double> t_knots_;
  uint32_t spline_order_;
  size_t total_params_;
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
