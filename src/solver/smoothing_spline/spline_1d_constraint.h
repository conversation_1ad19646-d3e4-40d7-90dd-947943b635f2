// Copyright 2023, trunk Inc. All rights reserved
/**
 * @file : spline_1d_constraint.h
 * @brief: wrapp up solver constraint interface with direct methods and preset
 *methods
 **/

#pragma once

#include <algorithm>
#include <vector>
#include "Eigen/Core"
#include "affine_constraint.h"
#include "spline_1d.h"

namespace trunk {
namespace pnd {
namespace solver {

class Spline1dConstraint {
 public:
  explicit Spline1dConstraint(const Spline1d& pss);
  Spline1dConstraint(const std::vector<double>& x_knots, uint32_t order);

  // direct methods
  bool AddInequalityConstraint(const Eigen::MatrixXd& constraint_matrix,
                               const Eigen::MatrixXd& constraint_boundary);
  bool AddEqualityConstraint(const Eigen::MatrixXd& constraint_matrix,
                             const Eigen::MatrixXd& constraint_boundary);

  // preset method
  /**
   * @brief: inequality boundary constraints
   * if no boundary, do specify either by std::infinity or
   * let vector.size() = 0
   **/
  bool AddBoundary(const std::vector<double>& x_coord,
                   const std::vector<double>& lower_bound,
                   const std::vector<double>& upper_bound);

  bool AddDerivativeBoundary(const std::vector<double>& x_coord,
                             const std::vector<double>& lower_bound,
                             const std::vector<double>& upper_bound);

  bool AddSecondDerivativeBoundary(const std::vector<double>& x_coord,
                                   const std::vector<double>& lower_bound,
                                   const std::vector<double>& upper_bound);

  bool AddThirdDerivativeBoundary(const std::vector<double>& x_coord,
                                  const std::vector<double>& lower_bound,
                                  const std::vector<double>& upper_bound);

  /**
   * @brief: equality constraint to guarantee joint smoothness
   * boundary equality constriant constraint on fx, dfx, ddfx ... in vector
   * form; upto third order
   **/
  bool AddPointConstraint(double x, double fx);
  bool AddPointDerivativeConstraint(double x, double dfx);
  bool AddPointSecondDerivativeConstraint(double x, double ddfx);
  bool AddPointThirdDerivativeConstraint(double x, double dddfx);

  bool AddPointConstraintInRange(double x, double fx, double range);
  bool AddPointDerivativeConstraintInRange(double x, double dfx, double range);
  bool AddPointSecondDerivativeConstraintInRange(double x, double ddfx,
                                                 double range);
  bool AddPointThirdDerivativeConstraintInRange(double x, double dddfx,
                                                double range);
  // guarantee upto values are joint
  bool AddSmoothConstraint();

  // guarantee upto derivative are joint
  bool AddDerivativeSmoothConstraint();

  // guarantee upto second order derivative are joint
  bool AddSecondDerivativeSmoothConstraint();

  // guarantee upto third order derivative are joint
  bool AddThirdDerivativeSmoothConstraint();

  /**
   * @brief: Add monotone constraint inequality, guarantee the monotone city at
   * evaluated point. customized monotone inequality constraint at x_coord
   **/
  bool AddMonotoneInequalityConstraint(const std::vector<double>& x_coord);

  // default inequality constraint at knots
  bool AddMonotoneInequalityConstraintAtKnots();

  /**
   * @brief: output interface inequality constraint
   **/
  const AffineConstraint& inequality_constraint() const;
  const AffineConstraint& equality_constraint() const;

 private:
  uint32_t FindIndex(double x) const;

  bool FilterConstraints(const std::vector<double>& x_coord,
                         const std::vector<double>& lower_bound,
                         const std::vector<double>& upper_bound,
                         std::vector<double>* filtered_lower_bound_x,
                         std::vector<double>* filtered_lower_bound,
                         std::vector<double>* filtered_upper_bound_x,
                         std::vector<double>* filtered_upper_bound);
  void GeneratePowerX(double x, uint32_t order,
                      std::vector<double>* power_x) const;

  using AddConstraintInRangeFunc =
      std::function<bool(const std::vector<double>&, const std::vector<double>&,
                         const std::vector<double>&)>;
  bool AddConstraintInRange(AddConstraintInRangeFunc func, double x, double val,
                            double range);

 private:
  AffineConstraint inequality_constraint_;
  AffineConstraint equality_constraint_;
  std::vector<double> x_knots_;
  uint32_t spline_order_;
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
