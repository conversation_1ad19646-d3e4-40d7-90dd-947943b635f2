// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <math.h>

#include <vector>

#include "Eigen/Core"
#include "affine_constraint.h"
#include "spline_2d.h"
#include "port/frame.h"

namespace trunk {
namespace pnd {
namespace solver {

class Spline2dConstraint {
 public:
  Spline2dConstraint(const std::vector<double>& t_knots, uint32_t order);

  // direct method
  bool AddInequalityConstraint(const Eigen::MatrixXd& constraint_matrix,
                               const Eigen::MatrixXd& constraint_boundary);
  bool AddEqualityConstraint(const Eigen::MatrixXd& constraint_matrix,
                             const Eigen::MatrixXd& constraint_boundary);

  // preset method
  /**
   *   @brief: inequality boundary constraints
   *   if no boundary, do specify either by std::infinity or let vector.size() =
   *0
   **/
  bool Add2dBoundary(const std::vector<double>& t_coord,
                     const std::vector<double>& angle,
                     const std::vector<tport::Point2D>& ref_point,
                     const std::vector<double>& longitudinal_bound,
                     const std::vector<double>& lateral_bound);

  // ref point refer to derivative reference point
  bool Add2dDerivativeBoundary(const std::vector<double>& t_coord,
                               const std::vector<double>& angle,
                               const std::vector<tport::Point2D>& ref_point,
                               const std::vector<double>& longitudinal_bound,
                               const std::vector<double>& lateral_bound);

  // ref point refer to second derivative ref point
  bool Add2dSecondDerivativeBoundary(
      const std::vector<double>& t_coord, const std::vector<double>& angle,
      const std::vector<tport::Point2D>& ref_point,
      const std::vector<double>& longitudinal_bound,
      const std::vector<double>& lateral_bound);

  // ref point refer to third derivative ref point
  bool Add2dThirdDerivativeBoundary(
      const std::vector<double>& t_coord, const std::vector<double>& angle,
      const std::vector<tport::Point2D>& ref_point,
      const std::vector<double>& longitudinal_bound,
      const std::vector<double>& lateral_bound);

  bool AddPointConstraint(double t, double x, double y);
  bool AddPointDerivativeConstraint(double t, double dx, double dy);
  bool AddPointSecondDerivativeConstraint(double t, double ddx, double ddy);
  bool AddPointThirdDerivativeConstraint(double t, double dddx, double dddy);
  bool AddPointAngleConstraint(double t, double angle);

  // guarantee upto values are joint
  bool AddSmoothConstraint();

  // guarantee upto derivative are joint
  bool AddDerivativeSmoothConstraint();

  // guarantee upto second order derivative are joint
  bool AddSecondDerivativeSmoothConstraint();

  // guarantee upto third order derivative are joint
  bool AddThirdDerivativeSmoothConstraint();

  /**
   *   @brief: output interface inequality constraint
   **/
  const AffineConstraint& inequality_constraint() const;
  const AffineConstraint& equality_constraint() const;

 private:
  uint32_t FindIndex(double t) const;
  std::vector<double> AffineCoef(double angle, double t) const;
  std::vector<double> AffineDerivativeCoef(double angle, double t) const;
  std::vector<double> AffineSecondDerivativeCoef(double angle, double t) const;
  std::vector<double> AffineThirdDerivativeCoef(double angle, double t) const;
  std::vector<double> PolyCoef(double t) const;
  std::vector<double> DerivativeCoef(double t) const;
  std::vector<double> SecondDerivativeCoef(double t) const;
  std::vector<double> ThirdDerivativeCoef(double t) const;
  double SignDistance(const tport::Point2D& xy_point, double angle) const;
  bool AddPointKthOrderDerivativeConstraint(
      double t, double x_kth_derivative, double y_kth_derivative,
      const std::vector<double>& kth_coeff);

  double InnerProd(double x0, double y0, double x1, double y1) const;

 private:
  AffineConstraint inequality_constraint_;
  AffineConstraint equality_constraint_;
  std::vector<double> t_knots_;
  uint32_t spline_order_;
  uint32_t total_param_;
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
