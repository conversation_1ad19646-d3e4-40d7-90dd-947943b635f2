// Copyright 2023, trunk Inc. All rights reserved

/**
 * @file : spline_1d_seg.h
 * @brief: polynomial smoothing spline
 **/

#pragma once

#include <vector>

#include "Eigen/Core"
#include "polynomial_xd.h"

namespace trunk {
namespace pnd {
namespace solver {

class Spline1dSeg {
 public:
  // order represents the highest order.
  explicit Spline1dSeg(uint32_t order);
  explicit Spline1dSeg(const std::vector<double>& params);
  ~Spline1dSeg() = default;

  void SetParams(const std::vector<double>& params);
  double operator()(double x) const;
  double Derivative(double x) const;
  double SecondOrderDerivative(double x) const;
  double ThirdOrderDerivative(double x) const;

  const PolynomialXd& spline_func() const;
  const PolynomialXd& Derivative() const;
  const PolynomialXd& SecondOrderDerivative() const;
  const PolynomialXd& ThirdOrderDerivative() const;

 private:
  inline void SetSplineFunc(const PolynomialXd& spline_func);

  PolynomialXd spline_func_;
  PolynomialXd derivative_;
  PolynomialXd second_order_derivative_;
  PolynomialXd third_order_derivative_;
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
