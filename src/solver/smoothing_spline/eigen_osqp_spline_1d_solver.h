// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <eigen3/Eigen/Dense>
#include <eigen3/Eigen/Sparse>
#include "spline_1d_solver.h"

namespace trunk {
namespace pnd {
namespace solver {

class EigenOsqpSpline1dSolver : public Spline1dSolver {
 public:
  EigenOsqpSpline1dSolver(const std::vector<double>& x_knots, uint32_t order);
  virtual ~EigenOsqpSpline1dSolver() {}

  bool Solve() override;

  void CleanUp();

  void ResetEigenOsqp();

 private:
  // allocate QP problem matrices and vectors
  Eigen::SparseMatrix<double> hessian;
  Eigen::VectorXd gradient;
  Eigen::SparseMatrix<double> linearMatrix;
  Eigen::VectorXd lowerBound;
  Eigen::VectorXd upperBound;
  int num_of_vars = 0;
  int num_of_cons = 0;
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
