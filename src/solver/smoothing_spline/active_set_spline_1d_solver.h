// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <qpOASES.hpp>
#include "spline_1d_solver.h"

namespace trunk {
namespace pnd {
namespace solver {

class ActiveSetSpline1dSolver : public Spline1dSolver {
 public:
  ActiveSetSpline1dSolver(const std::vector<double>& x_knots,
                          const uint32_t order)
      : Spline1dSolver(x_knots, order) {}

  virtual ~ActiveSetSpline1dSolver() = default;

  bool Solve() override;

 private:
  std::unique_ptr<::qpOASES::SQProblem> sqp_solver_;
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
