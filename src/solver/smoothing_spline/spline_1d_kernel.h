// Copyright 2023, trunk Inc. All rights reserved

/**
 * @file : spline_1d_kernel.h
 * @brief: wrap up solver constraint interface with direct methods and preset
 *methods
 **/

#pragma once

#include <vector>
#include "Eigen/Core"
#include "spline_1d.h"

namespace trunk {
namespace pnd {
namespace solver {

class Spline1dKernel {
 public:
  explicit Spline1dKernel(const Spline1d& spline1d);
  Spline1dKernel(const std::vector<double>& x_knots, uint32_t spline_order);

  // customized input / output method
  void AddRegularization(double regularized_param);
  bool AddKernel(const Eigen::MatrixXd& kernel, const Eigen::MatrixXd& offset,
                 double weight);
  bool AddKernel(const Eigen::MatrixXd& kernel, double weight);

  Eigen::MatrixXd* mutable_kernel_matrix();
  Eigen::MatrixXd* mutable_offset();

  const Eigen::MatrixXd& kernel_matrix() const;
  const Eigen::MatrixXd& offset() const;

  // build-in kernel methods
  void AddDerivativeKernelMatrix(double weight);
  void AddSecondOrderDerivativeMatrix(double weight);
  void AddThirdOrderDerivativeMatrix(double weight);
  void AddDerivativeKernelMatrixForSplineK(uint32_t k, double weight);
  void AddSecondOrderDerivativeMatrixForSplineK(uint32_t k, double weight);
  void AddThirdOrderDerivativeMatrixForSplineK(uint32_t k, double weight);

  // reference line kernel, x_coord in strictly increasing order (for path
  // optimizer)
  bool AddReferenceLineKernelMatrix(const std::vector<double>& x_coord,
                                    const std::vector<double>& ref_fx,
                                    double weight);

  // distance offset (for speed optimizer, given time optimize the distance can
  // go)
  void AddDistanceOffset(double weight);

 private:
  void AddNthDerivativekernelMatrix(uint32_t n, double weight);
  void AddNthDerivativekernelMatrixForSplineK(uint32_t n, uint32_t k,
                                              double weight);
  uint32_t FindIndex(double x) const;

 private:
  Eigen::MatrixXd kernel_matrix_;
  Eigen::MatrixXd offset_;
  std::vector<double> x_knots_;
  uint32_t spline_order_;
  uint32_t total_params_;
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
