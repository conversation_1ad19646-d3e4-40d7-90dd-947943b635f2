// Copyright 2023, trunk Inc. All rights reserved

#include "eigen_osqp_spline_1d_solver.h"

#include <OsqpEigen/OsqpEigen.h>
#include <absl/strings/str_format.h>
#include <absl/time/clock.h>

namespace trunk {
namespace pnd {
namespace solver {

EigenOsqpSpline1dSolver::EigenOsqpSpline1dSolver(
    const std::vector<double>& x_knots, const uint32_t order)
    : Spline1dSolver(x_knots, order) {}

bool EigenOsqpSpline1dSolver::Solve() {
  const MatrixXd& kernel_matrix = kernel_.kernel_matrix();
  const MatrixXd& offset = kernel_.offset();
  const MatrixXd& inequality_constraint_matrix =
      constraint_.inequality_constraint().constraint_matrix();
  const MatrixXd& inequality_constraint_boundary =
      constraint_.inequality_constraint().constraint_boundary();
  const MatrixXd& equality_constraint_matrix =
      constraint_.equality_constraint().constraint_matrix();
  const MatrixXd& equality_constraint_boundary =
      constraint_.equality_constraint().constraint_boundary();

  const int n = kernel_matrix.rows();
  const int m = equality_constraint_matrix.rows();
  const int p = inequality_constraint_matrix.rows();
  num_of_vars = n;
  num_of_cons = m + p;

  // minimize    0.5 x' P x + q' x
  // subject to    l <= A x <= u

  // hessian matrix
  hessian = kernel_matrix.sparseView();

  // gradient vector
  gradient.resize(n);
  for (int i = 0; i < n; ++i) {
    gradient[i] = offset(i, 0);
  }

  // linear matrix
  Eigen::MatrixXd cons = MatrixXd::Zero(num_of_cons, num_of_vars);
  // lower boundary
  lowerBound = Eigen::VectorXd::Zero(num_of_cons);
  // upper boundary
  upperBound = Eigen::VectorXd::Zero(num_of_cons);

  // inequality linear matrix
  cons.block(0, 0, p, n) = inequality_constraint_matrix;
  // inequality lower boundary
  lowerBound.segment(0, p) = inequality_constraint_boundary;
  // inequality upper boundary
  upperBound.segment(0, p) = OsqpEigen::INFTY * Eigen::VectorXd::Ones(p);

  // equality linear matrix
  if (m) {
    cons.block(p, 0, m, n) = equality_constraint_matrix;
    // equality lower boundary
    lowerBound.segment(p, m) = equality_constraint_boundary;
    // equality upper boundary
    upperBound.segment(p, m) = equality_constraint_boundary;
  }
  linearMatrix = cons.sparseView();

  // instantiate the solver
  OsqpEigen::Solver solver;
  // settings
  solver.settings()->setVerbosity(false);
  solver.settings()->setWarmStart(true);

  solver.data()->setNumberOfVariables(num_of_vars);
  solver.data()->setNumberOfConstraints(num_of_cons);
  if (!solver.data()->setHessianMatrix(hessian))
    return false;
  if (!solver.data()->setGradient(gradient))
    return false;
  if (!solver.data()->setLinearConstraintsMatrix(linearMatrix))
    return false;
  if (!solver.data()->setLowerBound(lowerBound))
    return false;
  if (!solver.data()->setUpperBound(upperBound))
    return false;

  // instantiate the solver
  if (!solver.initSolver())
    return false;

  Eigen::VectorXd solved_params;

  // solve the QP problem
  auto time_before_solve = absl::Now();

  if (!solver.solve())
    return false;

  TRUNK_LOG_DEBUG << absl::StrFormat(
      "QP solve time: %.3fms",
      absl::ToDoubleMilliseconds(absl::Now() - time_before_solve));

  // get the controller input
  solved_params = solver.getSolution();

  return spline_.SetSplineSegs(solved_params, spline_.spline_order());
}

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
