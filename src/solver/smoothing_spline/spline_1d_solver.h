// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <Eigen/Core>
#include <algorithm>
#include <memory>
#include <vector>

#include "log.h"
#include "spline_1d.h"
#include "spline_1d_constraint.h"
#include "spline_1d_kernel.h"

namespace trunk {
namespace pnd {
namespace solver {

using Eigen::MatrixXd;

class Spline1dSolver {
 public:
  Spline1dSolver(const std::vector<double>& x_knots, uint32_t order)
      : spline_(x_knots, order),
        constraint_(x_knots, order),
        kernel_(x_knots, order) {}

  virtual ~Spline1dSolver() = default;

  virtual void Reset(const std::vector<double>& x_knots, uint32_t order) {
    spline_ = Spline1d(x_knots, order);
    constraint_ = Spline1dConstraint(x_knots, order);
    kernel_ = Spline1dKernel(x_knots, order);
  }

  virtual Spline1dConstraint* mutable_spline_constraint() {
    return &constraint_;
  }

  virtual Spline1dKernel* mutable_spline_kernel() { return &kernel_; }

  virtual bool Solve() = 0;

  // output
  virtual const Spline1d& spline() const { return spline_; }

 protected:
  Spline1d spline_;
  Spline1dConstraint constraint_;
  Spline1dKernel kernel_;

  int last_num_constraint_ = 0;
  int last_num_param_ = 0;
  bool last_problem_success_ = false;
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
