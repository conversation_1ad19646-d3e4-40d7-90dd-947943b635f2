#include <trunk/common/config.h>

#include <Eigen/Core>
#include <vector>

#include "Eigen/src/Core/Matrix.h"
#include "piecewise_bezier_curve.h"

class PiecewiseBezierCurveSolver : public PiecewiseBezierCurve {
 public:
  enum SolverType { OSQP, qpOASES };
  PiecewiseBezierCurveSolver(int deg, int max_derivative_order = 3);
  void SetConstraints(const std::vector<double>& sections,
                      const int hodograph_order = 2,
                      const int continuity_order = 3);
  void SetBoundarys(const std::vector<std::array<double, 4>>& lb,
                    const std::vector<std::array<double, 4>>& ub);

  void AddPositionCost(const double weight);
  void AddVelocityCost(const double weight);
  void AddAccelerationCost(const double weight);
  void AddJerkCost(const double weight);
  void AddPositionTerminalCost(const double weight, const double target);
  void AddVelocityTerminalCost(const double weight, const double target);
  void AddAccelerationTerminalCost(const double weight, const double target);
  void AddJerkTerminalCost(const double weight, const double target);
  uint32_t Solve(SolverType type);

 private:
  void AddCost(const int order, const double weight);
  void AddTerminalCost(const int order, const double weight,
                       const double target);
  uint32_t SolveqpOASES();
  uint32_t SolveOSQP();

 private:
  const int deg_ = 5;
  const int max_derivative_order_ = 3;
  int hodograph_order_;
  int continuity_order_;
  template <int DEG>
  using Vecd = Eigen::Matrix<double, 1, DEG>;
  template <int DEG>
  using Matd = Eigen::Matrix<double, DEG, DEG>;
  // The integral of the Bernstein basis vector squared
  static const std::vector<std::vector<Eigen::MatrixXd>> H_;
  // hodograph mapping vector
  static const std::vector<std::vector<Eigen::VectorXd>> Dv_;
  int opti_size_;
  int param_size_;
  Eigen::MatrixXd hessian_;
  Eigen::MatrixXd affine_;
  Eigen::VectorXd gradient_;
  Eigen::VectorXd lb_;
  Eigen::VectorXd ub_;
  Eigen::VectorXd P_;

  double absolute_tolerance_ = 1.0e-3;
  double relative_tolerance_ = 1.0e-3;
  int max_iteraction_ = 4000;
  int polish_refine_refineIter_ = 5;
};
