#include "piecewise_bezier_curve.h"

#include <algorithm>

PiecewiseBezierCurve::PiecewiseBezierCurve(int deg) : deg_(deg) {}
PiecewiseBezierCurve::PiecewiseBezierCurve(int deg,
                                           const std::vector<double>& section)
    : deg_(deg) {
  SetSections(section);
};

PiecewiseBezierCurve::PiecewiseBezierCurve(int deg,
                                           const std::vector<double>& section,
                                           const Eigen::VectorXd& pts)
    : deg_(deg) {
  SetSections(section);
  SetCtrlPts(pts);
};

void PiecewiseBezierCurve::SetSections(const std::vector<double>& section) {
  this->reserve(section.size() - 1);
  for (size_t i = 1; i < section.size(); ++i) {
    this->emplace_back(deg_, section[i - 1], section[i]);
  }
}

void PiecewiseBezierCurve::SetCtrlPts(const Eigen::VectorXd& pts) {
  assert((deg_ + 1) * this->size() == pts.size());
  for (int i = 0; i < this->size(); ++i) {
    this->at(i).SetCtrlPts(pts.block(i * (deg_ + 1), 0, deg_ + 1, 1));
  }
}

std::vector<BezierCurve>::iterator PiecewiseBezierCurve::CalBezierSeg(
    const double t) {
  return std::lower_bound(
      this->begin(), this->end(), t,
      [](BezierCurve& b, const double t) { return b.seg_start() < t; });
}

double PiecewiseBezierCurve::Evaluate(const double t) {
  auto it = CalBezierSeg(t);
  assert(it != this->end());
  return it->Evaluate(t);
}

double PiecewiseBezierCurve::Evaluate1d(const double t) {
  auto it = CalBezierSeg(t);
  assert(it != this->end());
  return it->Evaluate1d(t);
}

double PiecewiseBezierCurve::Evaluate2d(const double t) {
  auto it = CalBezierSeg(t);
  assert(it != this->end());
  return it->Evaluate2d(t);
}

std::array<double, 3> PiecewiseBezierCurve::EvaluateAll(const double t) {
  auto it = CalBezierSeg(t);
  assert(it != this->end());
  return it->EvaluateAll(t);
}
