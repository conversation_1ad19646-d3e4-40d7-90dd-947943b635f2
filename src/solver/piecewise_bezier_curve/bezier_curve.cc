#include "bezier_curve.h"

const std::vector<Eigen::VectorXd> BezierCurve::C_{
    (Vecd<1>() << 1.0).finished(),
    (Vecd<2>() << 1.0, 1.0).finished(),
    (Vecd<3>() << 1.0, 2.0, 1.0).finished(),
    (Vecd<4>() << 1.0, 3.0, 3.0, 1.0).finished(),
    (Vecd<5>() << 1.0, 4.0, 6.0, 4.0, 1.0).finished(),
    (Vecd<6>() << 1.0, 5.0, 10.0, 10.0, 5.0, 1.0).finished(),
    (Vecd<7>() << 1.0, 6.0, 15.0, 20.0, 15.0, 6.0, 1.0).finished()};

const std::vector<Eigen::VectorXd> BezierCurve::A_{
    (Vecd<1>() << 1.0).finished(),
    (Vecd<2>() << 1.0, 1.0).finished(),
    (Vecd<3>() << 1.0, 2.0, 2.0).finished(),
    (Vecd<4>() << 1.0, 3.0, 6.0, 6.0).finished(),
    (Vecd<5>() << 1.0, 4.0, 12.0, 24.0, 24.0).finished(),
    (Vecd<6>() << 1.0, 5.0, 20.0, 60.0, 120.0, 120.0).finished(),
    (Vecd<7>() << 1.0, 6.0, 30.0, 120.0, 360.0, 720.0, 720.0).finished()};

BezierCurve::BezierCurve(int deg, double start, double end)
    : deg_(deg),
      seg_start_(start),
      seg_end_(end),
      seg_length_(end - start),
      length_(1.0, seg_length_, std::pow(seg_length_, 2),
              std::pow(seg_length_, 3)) {}

BezierCurve::BezierCurve(int deg, double start, double end,
                         const Eigen::VectorXd& pts)
    : deg_(deg), seg_start_(start), seg_end_(end), seg_length_(end - start) {
  SetCtrlPts(pts);
};

void BezierCurve::SetCtrlPts(const Eigen::VectorXd& pts) {
  assert(pts.rows() == deg_ + 1);
  ctrl_pts_ = pts;
  ctrl_pts_1d_ =
      deg_ * (ctrl_pts_.block(1, 0, deg_, 1) - ctrl_pts_.block(0, 0, deg_, 1));
  ctrl_pts_2d_ = (deg_ - 1) * (ctrl_pts_1d_.block(1, 0, deg_ - 1, 1) -
                               ctrl_pts_1d_.block(0, 0, deg_ - 1, 1));
}

const std::pair<Eigen::VectorXd, Eigen::VectorXd>
BezierCurve::CalBernsteinFactor(double t) {
  const double ut = (t - seg_start_) / seg_length_;
  std::pair<Eigen::VectorXd, Eigen::VectorXd> u(Eigen::VectorXd(deg_ + 1),
                                                Eigen::VectorXd(deg_ + 1));
  u.first[0] = 1.0;
  u.second[deg_] = 1.0;
  for (int i = 0; i < deg_; ++i) {
    u.first[i + 1] = u.first[i] * ut;
    u.second[deg_ - 1 - i] = u.second[deg_ - i] * (1.0 - ut);
  }
  return u;
}

double BezierCurve::Evaluate(double t) {
  auto u = CalBernsteinFactor(t);
  return (C_[deg_].array() * u.first.array() * u.second.array() *
          ctrl_pts_.array())
      .sum();
};

double BezierCurve::Evaluate1d(double t) {
  auto u = CalBernsteinFactor(t);
  return (C_[deg_].array() * u.first.block(1, 0, deg_, 1).array() *
          u.second.array().block(0, 0, deg_, 1) * ctrl_pts_.array())
      .sum();
};
double BezierCurve::Evaluate2d(double t) {
  auto u = CalBernsteinFactor(t);
  return (C_[deg_].array() * u.first.block(2, 0, deg_ - 1, 1).array() *
          u.second.array().block(0, 0, deg_ - 1, 1) * ctrl_pts_.array())
      .sum();
};

std::array<double, 3> BezierCurve::EvaluateAll(const double t) {
  auto u = CalBernsteinFactor(t);
  return {(C_[deg_].array() * u.first.array() * u.second.array() *
           ctrl_pts_.array())
              .sum(),
          (C_[deg_].array() * u.first.block(1, 0, deg_, 1).array() *
           u.second.array().block(0, 0, deg_, 1) * ctrl_pts_.array())
              .sum(),
          (C_[deg_].array() * u.first.block(2, 0, deg_ - 1, 1).array() *
           u.second.array().block(0, 0, deg_ - 1, 1) * ctrl_pts_.array())
              .sum()};
}
