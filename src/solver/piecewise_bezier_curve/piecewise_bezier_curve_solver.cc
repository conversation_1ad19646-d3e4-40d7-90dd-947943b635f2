#include "solver/piecewise_bezier_curve/piecewise_bezier_curve_solver.h"

#include <OsqpEigen/OsqpEigen.h>
#include <trunk/common/config.h>

#include <Eigen/Core>
#include <qpOASES/QProblem.hpp>
#include <qpOASES/Types.hpp>

#include "Eigen/src/Core/Matrix.h"
#include "solver/piecewise_bezier_curve/piecewise_bezier_curve.h"

const std::vector<std::vector<Eigen::MatrixXd>> PiecewiseBezierCurveSolver::H_{
    {(Matd<1>() << 1.0).finished()},
    {(Matd<2>() << 0.33333333, 0.16666667,  // 0
      0.16666667, 0.33333333                // 1
      )
         .finished(),
     (Matd<2>() << 1., -1.,  //
      -1., 1.)
         .finished()},
    {(Matd<3>() << 0.2, 0.1, 0.03333333,  // 0
      0.1, 0.13333333, 0.1,               // 1
      0.03333333, 0.1, 0.2                // 2
      )
         .finished(),
     (Matd<3>() << 1.33333333, -0.66666667, -0.66666667,  // 0
      -0.66666667, 1.33333333, -0.66666667,               // 1
      -0.66666667, -0.66666667, 1.33333333                //
      )
         .finished(),
     (Matd<3>() << 4., -8., 4.,  //
      -8., 16., -8.,             //
      4., -8., 4.                //
      )
         .finished()},
    {(Matd<4>() << 0.14285714, 0.07142857, 0.02857143, 0.00714286,  //
      0.07142857, 0.08571429, 0.06428571, 0.02857143,               //
      0.02857143, 0.06428571, 0.08571429, 0.07142857,               //
      0.00714286, 0.02857143, 0.07142857, 0.14285714                //
      )
         .finished(),
     (Matd<4>() << 1.8, -0.9, -0.6, -0.3,  //
      -0.9, 1.2, 0.3, -0.6,                //
      -0.6, 0.3, 1.2, -0.9,                //
      -0.3, -0.6, -0.9, 1.8                //
      )
         .finished(),
     (Matd<4>() << 12., -18., 0., 6.,  //
      -18., 36., -18., 0.,             //
      0., -18., 36., -18.,             //
      6., 0., -18., 12.                //
      )
         .finished(),
     (Matd<4>() << 36., -108., 108., -36.,  //
      -108., 324., -324., 108.,             //
      108., -324., 324., -108.,             //
      -36., 108., -108., 36.                //
      )
         .finished()},
    {(Matd<5>() << 0.11111111, 0.05555556, 0.02380952, 0.00793651,
      0.0015873,                                                   //
      0.05555556, 0.06349206, 0.04761905, 0.02539683, 0.00793651,  //
      0.02380952, 0.04761905, 0.05714286, 0.04761905, 0.02380952,  //
      0.00793651, 0.02539683, 0.04761905, 0.06349206, 0.05555556,  //
      0.0015873, 0.00793651, 0.02380952, 0.05555556, 0.11111111    //
      )
         .finished(),
     (Matd<5>() << 2.28571429, -1.14285714, -0.68571429, -0.34285714,
      -0.11428571,                                                    //
      -1.14285714, 1.37142857, 0.34285714, -0.22857143, -0.34285714,  //
      -0.68571429, 0.34285714, 0.68571429, 0.34285714, -0.68571429,   //
      -0.34285714, -0.22857143, 0.34285714, 1.37142857, -1.14285714,  //
      -0.11428571, -0.34285714, -0.68571429, -1.14285714, 2.28571429  //
      )
         .finished(),
     (Matd<5>() << 28.8, -43.2, 4.8, 4.8, 4.8,  //
      -43.2, 76.8, -19.2, -19.2, 4.8,           //
      4.8, -19.2, 28.8, -19.2, 4.8,             //
      4.8, -19.2, -19.2, 76.8, -43.2,           //
      4.8, 4.8, 4.8, -43.2, 28.8                //
      )
         .finished(),
     (Matd<5>() << 192., -480., 288., 96., -96.,  //
      -480., 1344., -1152., 192., 96.,            //
      288., -1152., 1728., -1152., 288.,          //
      96., 192., -1152., 1344., -480.,            //
      -96., 96., 288., -480., 192.                //
      )
         .finished()},
    {(Matd<6>() << 0.09090909, 0.04545455, 0.02020202, 0.00757576, 0.0021645,
      0.00036075,                                                             //
      0.04545455, 0.05050505, 0.03787879, 0.02164502, 0.00901876, 0.0021645,  //
      0.02020202, 0.03787879, 0.04329004, 0.03607504, 0.02164502,
      0.00757576,  //
      0.00757576, 0.02164502, 0.03607504, 0.04329004, 0.03787879,
      0.02020202,                                                             //
      0.0021645, 0.00901876, 0.02164502, 0.03787879, 0.05050505, 0.04545455,  //
      0.00036075, 0.0021645, 0.00757576, 0.02020202, 0.04545455, 0.09090909   //
      )
         .finished(),
     (Matd<6>() << 2.77777778, -1.38888889, -0.79365079, -0.3968254,
      -0.15873016, -0.03968254,  //
      -1.38888889, 1.58730159, 0.3968254, -0.15873016, -0.27777778,
      -0.15873016,  //
      -0.79365079, 0.3968254, 0.63492063, 0.31746032, -0.15873016,
      -0.3968254,  //
      -0.3968254, -0.15873016, 0.31746032, 0.63492063, 0.3968254,
      -0.79365079,  //
      -0.15873016, -0.27777778, -0.15873016, 0.3968254, 1.58730159,
      -1.38888889,  //
      -0.03968254, -0.15873016, -0.3968254, -0.79365079, -1.38888889,
      2.77777778  //
      )
         .finished(),
     (Matd<6>() << 57.14285714, -85.71428571, 11.42857143, 8.57142857,
      5.71428571, 2.85714286,  //
      -85.71428571, 148.57142857, -37.14285714, -22.85714286, -8.57142857,
      5.71428571,  //
      11.42857143, -37.14285714, 34.28571429, 5.71428571, -22.85714286,
      8.57142857,  //
      8.57142857, -22.85714286, 5.71428571, 34.28571429, -37.14285714,
      11.42857143,  //
      5.71428571, -8.57142857, -22.85714286, -37.14285714, 148.57142857,
      -85.71428571,  //
      2.85714286, 5.71428571, 8.57142857, 11.42857143, -85.71428571,
      57.14285714  //
      )
         .finished(),
     (Matd<6>() << 720., -1800., 1200., 0., 0., -120.,  //
      -1800., 4800., -3600., 0., 600., 0.,              //
      1200., -3600., 3600., -1200., 0., 0.,             //
      0., 0., -1200., 3600., -3600., 1200.,             //
      0., 600., 0., -3600., 4800., -1800.,              //
      -120., 0., 0., 1200., -1800., 720.                //
      )
         .finished()},
    {(Matd<7>() << 7.69230769e-02, 3.84615385e-02, 1.74825175e-02,
      6.99300699e-03, 2.33100233e-03, 5.82750583e-04, 8.32500833e-05,  //
      3.84615385e-02, 4.19580420e-02, 3.14685315e-02, 1.86480186e-02,
      8.74125874e-03, 2.99700300e-03, 5.82750583e-04,  //
      1.74825175e-02, 3.14685315e-02, 3.49650350e-02, 2.91375291e-02,
      1.87312687e-02, 8.74125874e-03, 2.33100233e-03,  //
      6.99300699e-03, 1.86480186e-02, 2.91375291e-02, 3.33000333e-02,
      2.91375291e-02, 1.86480186e-02, 6.99300699e-03,  //
      2.33100233e-03, 8.74125874e-03, 1.87312687e-02, 2.91375291e-02,
      3.49650350e-02, 3.14685315e-02, 1.74825175e-02,  //
      5.82750583e-04, 2.99700300e-03, 8.74125874e-03, 1.86480186e-02,
      3.14685315e-02, 4.19580420e-02, 3.84615385e-02,  //
      8.32500833e-05, 5.82750583e-04, 2.33100233e-03, 6.99300699e-03,
      1.74825175e-02, 3.84615385e-02, 7.69230769e-02  //
      )
         .finished(),
     (Matd<7>() << 3.27272727, -1.63636364, -0.90909091, -0.45454545,
      -0.19480519, -0.06493506, -0.01298701,  //
      -1.63636364, 1.81818182, 0.45454545, -0.12987013, -0.25974026,
      -0.18181818, -0.06493506,  //
      -0.90909091, 0.45454545, 0.64935065, 0.32467532, -0.06493506, -0.25974026,
      -0.19480519,  //
      -0.45454545, -0.12987013, 0.32467532, 0.51948052, 0.32467532, -0.12987013,
      -0.45454545,  //
      -0.19480519, -0.25974026, -0.06493506, 0.32467532, 0.64935065, 0.45454545,
      -0.90909091,  //
      -0.06493506, -0.18181818, -0.25974026, -0.12987013, 0.45454545,
      1.81818182, -1.63636364,  //
      -0.01298701, -0.06493506, -0.19480519, -0.45454545, -0.90909091,
      -1.63636364, 3.27272727  //
      )
         .finished(),
     (Matd<7>() << 100., -150., 21.42857143, 14.28571429, 8.57142857,
      4.28571429, 1.42857143,  //
      -150., 257.14285714, -64.28571429, -34.28571429, -12.85714286, 0.,
      4.28571429,  //
      21.42857143, -64.28571429, 51.42857143, 8.57142857, -12.85714286,
      -12.85714286, 8.57142857,  //
      14.28571429, -34.28571429, 8.57142857, 22.85714286, 8.57142857,
      -34.28571429, 14.28571429,  //
      8.57142857, -12.85714286, -12.85714286, 8.57142857, 51.42857143,
      -64.28571429, 21.42857143,  //
      4.28571429, 0., -12.85714286, -34.28571429, -64.28571429, 257.14285714,
      -150.,  //
      1.42857143, 4.28571429, 8.57142857, 14.28571429, 21.42857143, -150.,
      100.  //
      )
         .finished(),
     (Matd<7>() << 2057.14285714, -5142.85714286, 3497.14285714, -102.85714286,
      -102.85714286, -102.85714286, -102.85714286,  //
      -5142.85714286, 13577.14285714, -10182.85714286, 617.14285714,
      617.14285714, 617.14285714, -102.85714286,  //
      3497.14285714, -10182.85714286, 9257.14285714, -1542.85714286,
      -1542.85714286, 617.14285714, -102.85714286,  //
      -102.85714286, 617.14285714, -1542.85714286, 2057.14285714,
      -1542.85714286, 617.14285714, -102.85714286,  //
      -102.85714286, 617.14285714, -1542.85714286, -1542.85714286,
      9257.14285714, -10182.85714286, 3497.14285714,  //
      -102.85714286, 617.14285714, 617.14285714, 617.14285714, -10182.85714286,
      13577.14285714, -5142.85714286,  //
      -102.85714286, -102.85714286, -102.85714286, -102.85714286, 3497.14285714,
      -5142.85714286, 2057.14285714  //
      )
         .finished()},
};

const std::vector<std::vector<Eigen::VectorXd>> PiecewiseBezierCurveSolver::Dv_{
    {(Vecd<1>() << 1.).finished()},
    {(Vecd<1>() << 1.).finished(), (Vecd<2>() << -1., 1.).finished()},
    {(Vecd<1>() << 1.).finished(), (Vecd<2>() << -2., 2.).finished(),
     (Vecd<3>() << 2., -4., 2.).finished()},
    {(Vecd<1>() << 1.).finished(), (Vecd<2>() << -3., 3.).finished(),
     (Vecd<3>() << 6., -12., 6.).finished(),
     (Vecd<4>() << -6., 18., -18, 6.).finished()},
    {(Vecd<1>() << 1.).finished(), (Vecd<2>() << -4., 4.).finished(),
     (Vecd<3>() << 12., -24., 12.).finished(),
     (Vecd<4>() << -24., 72., -72., 24.).finished()},
    {(Vecd<1>() << 1.).finished(), (Vecd<2>() << -5., 5.).finished(),
     (Vecd<3>() << 20., -40., 20.).finished(),
     (Vecd<4>() << -60., 180., -180., 60.).finished()},
    {(Vecd<1>() << 1.).finished(), (Vecd<2>() << -6., 6.).finished(),
     (Vecd<3>() << 30., -60., 30.).finished(),
     (Vecd<4>() << -120., 360., -360., 120.).finished()},
};

PiecewiseBezierCurveSolver::PiecewiseBezierCurveSolver(int deg,
                                                       int max_derivative_order)
    : PiecewiseBezierCurve(deg),
      deg_(deg),
      param_size_(deg_ + 1),
      max_derivative_order_(max_derivative_order) {
  assert(deg >= max_derivative_order);
}

void PiecewiseBezierCurveSolver::SetConstraints(
    const std::vector<double>& sections, const int hodograph_order,
    const int continuity_order) {
  SetSections(sections);
  hodograph_order_ = hodograph_order;
  continuity_order_ = continuity_order;
  opti_size_ = param_size_ * this->size();
  hessian_.setZero(opti_size_, opti_size_);
  gradient_.setZero(opti_size_);
  P_.resize(this->size() * param_size_);
  // 连续性约束
  const int continuity_num = (continuity_order + 1) * (this->size() - 1);
  // 各阶导凸包约束 共hodograph_order + 1阶 每阶有参数param_size_ -
  // hodograph_order个
  const int hodograph_num = (param_size_ + (param_size_ - hodograph_order)) *
                            (hodograph_order + 1) / 2 * this->size();
  const int constraints_num = continuity_num + hodograph_num;
  affine_.setZero(constraints_num, opti_size_);

  for (int i = 1; i < this->size(); ++i) {
    for (int j = 0; j <= continuity_order; ++j) {
      affine_.block(hodograph_num + (i - 1) * (continuity_order + 1) + j,
                    (i - 1) * param_size_ + deg_ - j, 1, j + 1) =
          Dv_[deg_][j] / this->at(i - 1).length()[j];
      affine_.block(hodograph_num + (i - 1) * (continuity_order + 1) + j,
                    i * param_size_, 1, j + 1) =
          -Dv_[deg_][j] / this->at(i).length()[j];
    }
  }
  for (int i = 0, count = 0; i <= hodograph_order; ++i) {
    const int piece_hodo_i_num = param_size_ - i;
    for (int j = 0; j < this->size(); ++j) {
      for (int k = 0; k < piece_hodo_i_num; ++k, ++count) {
        affine_.block(count, j * param_size_ + k, 1, i + 1) =
            Dv_[deg_][i] / this->at(j).length()[i];
      }
    }
  }
  lb_.setZero(constraints_num);
  ub_.setZero(constraints_num);
}

void PiecewiseBezierCurveSolver::SetBoundarys(
    const std::vector<std::array<double, 4>>& lb,
    const std::vector<std::array<double, 4>>& ub) {
  assert(lb.size() == ub.size() && lb.size() == this->size());
  for (int i = 0, count = 0; i <= hodograph_order_; ++i) {
    const int piece_hodo_i_num = param_size_ - i;
    for (int j = 0; j < this->size(); ++j, count += piece_hodo_i_num) {
      lb_.block(count, 0, 1, piece_hodo_i_num) =
          lb[j][i] * Eigen::MatrixXd::Ones(piece_hodo_i_num, 1);
      ub_.block(count, 0, 1, piece_hodo_i_num) =
          ub[j][i] * Eigen::MatrixXd::Ones(piece_hodo_i_num, 1);
    }
  }
}

void PiecewiseBezierCurveSolver::AddPositionCost(const double weight) {
  AddCost(0, weight);
}
void PiecewiseBezierCurveSolver::AddVelocityCost(const double weight) {
  AddCost(1, weight);
}
void PiecewiseBezierCurveSolver::AddAccelerationCost(const double weight) {
  AddCost(2, weight);
}
void PiecewiseBezierCurveSolver::AddJerkCost(const double weight) {
  AddCost(3, weight);
}

void PiecewiseBezierCurveSolver::AddCost(const int order, const double weight) {
  for (int i = 0; i < this->size(); ++i) {
    hessian_.block(i * param_size_, i * param_size_, param_size_,
                   param_size_) +=
        weight / this->at(i).length()[order] * H_[deg_][order];
  }
}

void PiecewiseBezierCurveSolver::AddTerminalCost(const int order,
                                                 const double weight,
                                                 const double target) {
  hessian_.block(opti_size_ - 1 - order, opti_size_ - param_size_ - 1 - order,
                 order + 1, order + 1) +=
      weight * Dv_[deg_][order].transpose() * Dv_[deg_][order];
  gradient_.block(opti_size_ - 1 - order, 0, order + 1, 1) =
      -weight * target * Dv_[deg_][order].transpose();
}

void PiecewiseBezierCurveSolver::AddPositionTerminalCost(const double weight,
                                                         const double target) {
  AddTerminalCost(0, weight, target);
}
void PiecewiseBezierCurveSolver::AddVelocityTerminalCost(const double weight,
                                                         const double target) {
  AddTerminalCost(1, weight, target);
}
void PiecewiseBezierCurveSolver::AddAccelerationTerminalCost(
    const double weight, const double target) {
  AddTerminalCost(2, weight, target);
}
void PiecewiseBezierCurveSolver::AddJerkTerminalCost(const double weight,
                                                     const double target) {
  AddTerminalCost(3, weight, target);
}

uint32_t PiecewiseBezierCurveSolver::Solve(SolverType type) {
  if (type == OSQP) {
    return SolveOSQP();
  } else if (type == qpOASES) {
    return SolveqpOASES();
  } else {
    return 100;
  }
  this->SetCtrlPts(P_);
  return 0;
}
uint32_t PiecewiseBezierCurveSolver::SolveqpOASES() {
  qpOASES::QProblem solver(opti_size_, affine_.size() - opti_size_,
                           qpOASES::HST_POSDEF);
  qpOASES::Options options;
  options.setToMPC();
  solver.setOptions(options);
  solver.setPrintLevel(qpOASES::PL_LOW);
  qpOASES::int_t nWSR = 1000;
  qpOASES::returnValue result = solver.init(
      hessian_.transpose().data(), gradient_.data(),
      affine_.block(opti_size_, 0, affine_.rows() - opti_size_, opti_size_)
          .transpose()
          .data(),
      lb_.block(0, 0, opti_size_, 1).data(),
      ub_.block(0, 0, opti_size_, 1).data(),
      lb_.block(opti_size_, 0, lb_.size() - opti_size_, 1).data(),
      ub_.block(opti_size_, 0, ub_.size() - opti_size_, 1).data(), nWSR);
  if (result != qpOASES::SUCCESSFUL_RETURN) {
    return static_cast<uint32_t>(result);
  }
  solver.getPrimalSolution(P_.data());
  return 0;
}
uint32_t PiecewiseBezierCurveSolver::SolveOSQP() {
  OsqpEigen::Solver solver;
  // settings
  solver.settings()->setVerbosity(false);
  solver.settings()->setWarmStart(true);
  solver.settings()->setAbsoluteTolerance(absolute_tolerance_);
  solver.settings()->setRelativeTolerance(relative_tolerance_);
  solver.settings()->setMaxIteraction(max_iteraction_);
  solver.settings()->setPolishRefineIter(polish_refine_refineIter_);
  // set the initial data of the QP solver
  solver.data()->setNumberOfVariables(opti_size_);
  solver.data()->setNumberOfConstraints(affine_.rows());
  Eigen::SparseMatrix<double> sparse_hessian = hessian_.sparseView();
  Eigen::SparseMatrix<double> sparse_affine = affine_.sparseView();
  if (!solver.data()->setHessianMatrix(sparse_hessian)) {
    return 1;
  }
  if (!solver.data()->setGradient(gradient_)) {
    return 2;
  }
  if (!solver.data()->setLinearConstraintsMatrix(sparse_affine)) {
    return 3;
  }
  if (!solver.data()->setLowerBound(lb_)) {
    return 4;
  }
  if (!solver.data()->setUpperBound(ub_)) {
    return 5;
  }
  if (!solver.initSolver()) {
    return 6;
  }
  if (!solver.solve()) {
    return 7;
  }
  P_ = solver.getSolution();
  return 0;
}
