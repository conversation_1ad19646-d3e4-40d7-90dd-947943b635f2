#pragma once

#include <trunk/common/config.h>

#include <Eigen/Core>
#include <vector>

#include "bezier_curve.h"

class PiecewiseBezierCurve : public std::vector<BezierCurve> {
 public:
  PiecewiseBezierCurve(int deg);
  PiecewiseBezierCurve(int deg, const std::vector<double>& section);
  PiecewiseBezierCurve(int deg, const std::vector<double>& section,
                       const Eigen::VectorXd& pts);
  void SetCtrlPts(const Eigen::VectorXd& pts);
  void SetSections(const std::vector<double>& section);

  double Evaluate(const double t);

  double Evaluate1d(const double t);

  double Evaluate2d(const double t);

  std::array<double, 3> EvaluateAll(const double t);

 private:
  std::vector<BezierCurve>::iterator CalBezierSeg(const double t);

 private:
  MEMBER_BASIC_TYPE(int, deg, 0);
};
