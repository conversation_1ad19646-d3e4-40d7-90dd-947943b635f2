#pragma once

#include <trunk/common/config.h>

#include <Eigen/Dense>
#include <vector>

class BezierCurve {
 public:
  BezierCurve(int deg, double start, double end);
  BezierCurve(int deg, double start, double end, const Eigen::VectorXd& pts);

  void SetCtrlPts(const Eigen::VectorXd& pts);

  double Evaluate(const double t);

  double Evaluate1d(const double t);

  double Evaluate2d(const double t);

  std::array<double, 3> EvaluateAll(const double t);

 private:
  const std::pair<Eigen::VectorXd, Eigen::VectorXd> CalBernsteinFactor(
      double t);

 private:
  template <int DEG>
  using Vecd = Eigen::Matrix<double, DEG, 1>;
  MEMBER_BASIC_TYPE(int, deg, 0);
  MEMBER_BASIC_TYPE(double, seg_length, 0.0);
  MEMBER_BASIC_TYPE(double, seg_start, 0.0);
  MEMBER_BASIC_TYPE(double, seg_end, 0.0);
  MEMBER_COMPLEX_TYPE(Vecd<4>, length);
  MEMBER_COMPLEX_TYPE(Eigen::VectorXd, ctrl_pts);
  MEMBER_COMPLEX_TYPE(Eigen::VectorXd, ctrl_pts_1d);
  MEMBER_COMPLEX_TYPE(Eigen::VectorXd, ctrl_pts_2d);
  static const std::vector<Eigen::VectorXd> C_;
  static const std::vector<Eigen::VectorXd> A_;
};
