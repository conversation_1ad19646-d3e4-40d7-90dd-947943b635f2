#pragma once

#include <trunk/common/config.h>

#include <Eigen/Core>

namespace trunk {
namespace pnd {
namespace solver {

class MpcSolver {
 public:
  MpcSolver() = default;
  void SetModel(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B,
                const int Np, const int Nc,
                const bool using_adaptive_initial_state = false,
                const Eigen::MatrixXd& soft_constraints = Eigen::MatrixXd());
  void UpdateWeight();
  void SetTrackingWeight(const Eigen::MatrixXd& q_tracking,
                         const Eigen::MatrixXd& r_tracking);
  void SetLoopShapingWeight(const Eigen::MatrixXd& k_loopshaping,
                            const Eigen::MatrixXd& w_loopshaping);
  void SetTerminalTrackingWeight(const Eigen::MatrixXd& qT_terminal);
  void SetTerminalLoopShapingWeight(const Eigen::MatrixXd& kT_loopshaping,
                                    const Eigen::MatrixXd& wT_loopshaping);
  void SetAdaptiveInitialState(const Eigen::MatrixXd& r0_adaptive);
  void SetSoftConstraintsWeight(const Eigen::MatrixXd& r_softconstraints);

  uint32_t Solve(const Eigen::VectorXd& x0, const Eigen::VectorXd& reference,
                 const std::pair<Eigen::VectorXd, Eigen::VectorXd>& ub,
                 const std::pair<Eigen::VectorXd, Eigen::VectorXd>& xb,
                 int& nWSR, double* const cputime = nullptr);
  const Eigen::VectorXd& GetSolution();

 private:
  int nx_ = 0;
  int nu_ = 0;
  int ns_ = 0;
  int Np_ = 0;
  int Nc_ = 0;

  Eigen::MatrixXd A_;
  Eigen::MatrixXd B_;
  Eigen::MatrixXd Cs_;
  Eigen::MatrixXd AP_;
  Eigen::MatrixXd BP_;
  Eigen::MatrixXd AC_;
  Eigen::MatrixXd BC_;

  Eigen::MatrixXd Ip_;
  Eigen::MatrixXd Ic_;
  Eigen::MatrixXd Icp_;

  Eigen::MatrixXd q_tracking_;
  Eigen::MatrixXd r_tracking_;
  Eigen::MatrixXd qT_tracking_;
  Eigen::MatrixXd k_loopshaping_;
  Eigen::MatrixXd w_loopshaping_;
  Eigen::MatrixXd kT_loopshaping_;
  Eigen::MatrixXd wT_loopshaping_;
  Eigen::MatrixXd r0_softconstraints_;
  Eigen::MatrixXd r_softconstraints_;

  Eigen::MatrixXd Q_tracking_;
  Eigen::MatrixXd R_tracking_;
  Eigen::MatrixXd QT_tracking_;
  Eigen::MatrixXd B_loopshaping_;
  Eigen::MatrixXd W_loopshaping_;
  Eigen::MatrixXd K_loopshaping_;
  Eigen::MatrixXd BT_loopshaping_;
  Eigen::MatrixXd WT_loopshaping_;
  Eigen::MatrixXd KT_loopshaping_;
  Eigen::MatrixXd R0_adaptive_;
  Eigen::MatrixXd R_softconstraints_;

  Eigen::MatrixXd H_tracking_;
  Eigen::MatrixXd HT_tracking_;
  Eigen::MatrixXd H_loopshaping_;
  Eigen::MatrixXd HT_loopshaping_;
  Eigen::MatrixXd H0_adaptive_;
  Eigen::MatrixXd H_softconstraints_;

  Eigen::MatrixXd hessian_;
  Eigen::MatrixXd affine_;
  Eigen::VectorXd gradient_;
  Eigen::VectorXd U_;
  std::pair<Eigen::VectorXd, Eigen::VectorXd> xb_;

  MEMBER_COMPLEX_TYPE(Eigen::VectorXd, x0_relaxation);
  MEMBER_BASIC_TYPE(int, Nx, 0);
  MEMBER_BASIC_TYPE(int, Nu, 0);
  MEMBER_BASIC_TYPE(int, Ns, 0);
  MEMBER_BASIC_TYPE(int, nais, 0);
};

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
