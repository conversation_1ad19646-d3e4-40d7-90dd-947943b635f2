#include "solver/mpc/mpc_solver.h"

#include <Eigen/src/Core/Matrix.h>

#include <Eigen/Dense>
#include <Eigen/Sparse>
#include <eigen3/unsupported/Eigen/KroneckerProduct>
#include <qpOASES/QProblem.hpp>

namespace trunk {
namespace pnd {
namespace solver {
void MpcSolver::SetModel(const Eigen::MatrixXd& A, const Eigen::MatrixXd& B,
                         const int Np, const int Nc,
                         const bool using_adaptive_initial_state,
                         const Eigen::MatrixXd& soft_constraints) {
  nx_ = A.rows();
  nu_ = B.cols();
  Np_ = Np;
  Nc_ = Nc;
  ns_ = soft_constraints.cols();
  nais_ = using_adaptive_initial_state ? nx_ : 0;

  Nx_ = nx_ * (Np_ + 1);
  Nu_ = nu_ * Nc_;
  Ns_ = ns_ * (Np_ + 1);
  assert(Np >= Nc);
  A_.setZero(Nx_, nx_);
  B_.setZero(Nx_, Nu_ + nais_);

  Eigen::MatrixXd tmpA = Eigen::MatrixXd::Identity(nx_, nx_);
  A_.block(0, 0, nx_, nx_) = tmpA;
  for (int i = 1; i <= Np_; ++i) {
    auto tmpB = tmpA * B;
    for (int j = i; j <= Nc_; ++j) {
      B_.block(nx_ * j, nu_ * (j - i), nx_, nu_) = tmpB;
    }
    tmpA = tmpA * A;
    A_.block(nx_ * i, 0, nx_, nx_) = tmpA;
  }
  if (nais_) {
    B_.block(0, Nu_, Nx_, nais_) = A_;
  }
  AP_ = A_.block(Np_ * nx_, 0, nx_, nx_);
  BP_ = B_.block(Np_ * nx_, 0, nx_, Nu_ + nais_);
  AC_ = A_.block(Nc_ * nx_, 0, nx_, nx_);
  BC_ = B_.block(Nc_ * nx_, 0, nx_, Nu_ + nais_);
  affine_.setZero(Nx_, Nu_ + nais_ + Ns_);
  affine_.block(0, 0, Nx_, Nu_ + nais_) = B_;
  if (ns_) {
    affine_.block(0, Nu_ + nais_, Nx_, Ns_) =
        Eigen::KroneckerProduct<Eigen::MatrixXd, Eigen::MatrixXd>(
            Eigen::MatrixXd::Identity(Np_ + 1, Np_ + 1), soft_constraints);
  }
  affine_.transposeInPlace();
  hessian_.setZero(Nu_ + nais_ + Ns_, Nu_ + nais_ + Ns_);
  gradient_.setZero(Nu_ + nais_ + Ns_);

  Ip_ = Eigen::MatrixXd::Identity(Np_ + 1, Np_ + 1);
  Ic_ = Eigen::MatrixXd::Identity(Nc_, Nc_);
  Icp_ = Eigen::MatrixXd::Identity(Nc_, Np_ + 1);
  U_.resize(Nu_ + nais_ + Ns_);
  SetTrackingWeight(Eigen::MatrixXd::Zero(nx_, nx_),
                    Eigen::MatrixXd::Zero(nu_, nu_));
  SetLoopShapingWeight(Eigen::MatrixXd::Zero(nu_, nx_),
                       Eigen::MatrixXd::Zero(nu_, nu_));
  SetTerminalTrackingWeight(Eigen::MatrixXd::Zero(nx_, nx_));
  SetTerminalLoopShapingWeight(Eigen::MatrixXd::Zero(nu_, nx_),
                               Eigen::MatrixXd::Zero(nu_, nu_));
  SetSoftConstraintsWeight(Eigen::MatrixXd::Zero(ns_, ns_));
  SetAdaptiveInitialState(Eigen::MatrixXd::Zero(nais_, nais_));
  UpdateWeight();
}

void MpcSolver::SetTrackingWeight(const Eigen::MatrixXd& q_tracking,
                                  const Eigen::MatrixXd& r_tracking) {
  q_tracking_ = q_tracking;
  r_tracking_ = r_tracking;
  // tracking
  Q_tracking_ = Eigen::KroneckerProduct<Eigen::MatrixXd, Eigen::MatrixXd>(
      Ip_, q_tracking_);
  R_tracking_.setZero(Nu_ + nais_, Nu_ + nais_);
  R_tracking_.block(0, 0, Nu_, Nu_) =
      Eigen::KroneckerProduct<Eigen::MatrixXd, Eigen::MatrixXd>(Ic_,
                                                                r_tracking_);
  H_tracking_ = B_.transpose() * Q_tracking_ * B_ + R_tracking_;
}
void MpcSolver::SetTerminalTrackingWeight(const Eigen::MatrixXd& qT_tracking) {
  qT_tracking_ = qT_tracking;
  // terminal
  QT_tracking_ = qT_tracking_;
  HT_tracking_ = BP_.transpose() * QT_tracking_ * BP_;
}

void MpcSolver::SetLoopShapingWeight(const Eigen::MatrixXd& k_loopshaping,
                                     const Eigen::MatrixXd& w_loopshaping) {
  k_loopshaping_ = k_loopshaping;
  w_loopshaping_ = w_loopshaping;
  // loop shaping
  K_loopshaping_ = Eigen::KroneckerProduct<Eigen::MatrixXd, Eigen::MatrixXd>(
      Icp_, k_loopshaping_);
  W_loopshaping_ = Eigen::KroneckerProduct<Eigen::MatrixXd, Eigen::MatrixXd>(
      Ic_, w_loopshaping_);
  B_loopshaping_ =
      Eigen::MatrixXd::Identity(Nu_, Nu_ + nais_) + (K_loopshaping_ * B_);
  H_loopshaping_ = B_loopshaping_.transpose() * W_loopshaping_ * B_loopshaping_;
}

void MpcSolver::SetTerminalLoopShapingWeight(
    const Eigen::MatrixXd& kT_loopshaping,
    const Eigen::MatrixXd& wT_loopshaping) {
  kT_loopshaping_ = kT_loopshaping;
  wT_loopshaping_ = wT_loopshaping;
  // loop shaping
  // KT_loopshaping_ = Eigen::KroneckerProduct<Eigen::MatrixXd,
  // Eigen::MatrixXd>(
  //     Icp_, kT_loopshaping_);
  // WT_loopshaping_ = Eigen::KroneckerProduct<Eigen::MatrixXd,
  // Eigen::MatrixXd>(
  //     Ic_, wT_loopshaping_);
  // BT_loopshaping_ = Eigen::MatrixXd::Identity(WT_loopshaping_.rows(),
  //                                             WT_loopshaping_.cols()) +
  //                   (KT_loopshaping_ * B_);
  KT_loopshaping_ = kT_loopshaping_;
  WT_loopshaping_ = wT_loopshaping_;
  Eigen::MatrixXd EC = Eigen::MatrixXd::Zero(nu_, Nu_ + nais_);
  EC.block(0, (Nc_ - 1) * nu_, nu_, nu_) = Eigen::MatrixXd::Identity(nu_, nu_);
  BT_loopshaping_ = EC + KT_loopshaping_ * BC_;
  HT_loopshaping_ =
      BT_loopshaping_.transpose() * WT_loopshaping_ * BT_loopshaping_;
}

void MpcSolver::SetAdaptiveInitialState(const Eigen::MatrixXd& r0_adaptive) {
  R0_adaptive_.setZero(Nu_ + nais_, Nu_ + nais_);
  if (nais_) {
    r0_softconstraints_ = r0_adaptive;
    R0_adaptive_.block(Nu_, Nu_, nais_, nais_) = r0_softconstraints_;
  }
  H0_adaptive_ = R0_adaptive_;
}

void MpcSolver::SetSoftConstraintsWeight(
    const Eigen::MatrixXd& r_softconstraints) {
  if (ns_ == 0) {
    return;
  }
  r_softconstraints_ = r_softconstraints;
  // soft constraints
  R_softconstraints_ =
      Eigen::KroneckerProduct<Eigen::MatrixXd, Eigen::MatrixXd>(
          Eigen::MatrixXd::Identity(Np_ + 1, Np_ + 1), r_softconstraints_);
  H_softconstraints_ = R_softconstraints_;
}

void MpcSolver::UpdateWeight() {
  hessian_.block(0, 0, Nu_ + nais_, Nu_ + nais_) +=
      H0_adaptive_ + H_tracking_ + HT_tracking_ + H_loopshaping_ +
      HT_loopshaping_;
  if (ns_) {
    hessian_.block(Nu_ + nais_, Nu_ + nais_, Ns_, Ns_) = H_softconstraints_;
  }
}

uint32_t MpcSolver::Solve(const Eigen::VectorXd& x0,
                          const Eigen::VectorXd& reference,
                          const std::pair<Eigen::VectorXd, Eigen::VectorXd>& ub,
                          const std::pair<Eigen::VectorXd, Eigen::VectorXd>& xb,
                          int& nWSR, double* const cputime) {
  Eigen::VectorXd Ax0 = A_ * x0;
  Eigen::VectorXd g_tracking = B_.transpose() * Q_tracking_ * (Ax0 - reference);
  Eigen::VectorXd g_loopshaping = B_loopshaping_.transpose() * W_loopshaping_ *
                                  K_loopshaping_ * (Ax0 - reference);
  Eigen::MatrixXd gT_tracking =
      BP_.transpose() * QT_tracking_ *
      (AP_ * x0 - reference.block(Np_ * nx_, 0, nx_, 1));
  // Eigen::VectorXd gT_loopshaping =
  //     BT_loopshaping_.transpose() * WT_loopshaping_ * KT_loopshaping_ *
  //     (Ax0 - Eigen::KroneckerProduct<Eigen::VectorXd, Eigen::VectorXd>(
  //                Eigen::VectorXd::Ones(Np_ + 1),
  //                reference.block(Np_ * nx_, 0, nx_, 1)));
  Eigen::VectorXd gT_loopshaping =
      BT_loopshaping_.transpose() * WT_loopshaping_ * KT_loopshaping_ *
      (AC_ * x0 - reference.block(Nc_ * nx_, 0, nx_, 1));
  gradient_.block(0, 0, Nu_ + nais_, 1) =
      g_tracking + g_loopshaping + gT_tracking + gT_loopshaping;

  xb_.first = xb.first - Ax0;
  xb_.second = xb.second - Ax0;

  qpOASES::QProblem solver(Nu_ + nais_ + Ns_, Nx_, qpOASES::HST_POSDEF);
  qpOASES::Options options;
  options.setToMPC();
  solver.setOptions(options);
  solver.setPrintLevel(qpOASES::PL_LOW);

  qpOASES::returnValue result = solver.init(
      hessian_.data(), gradient_.data(), affine_.data(), ub.first.data(),
      ub.second.data(), xb_.first.data(), xb_.second.data(), nWSR, cputime);
  if (result != qpOASES::SUCCESSFUL_RETURN) {
    return static_cast<uint32_t>(result);
  }
  solver.getPrimalSolution(U_.data());
  if (nais_) {
    x0_relaxation_ = U_.block(Nu_, 0, nais_, 1);
  }
  return 0;
}

const Eigen::VectorXd& MpcSolver::GetSolution() { return U_; }

}  // namespace solver
}  // namespace pnd
}  // namespace trunk
