// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#ifdef ENABLE_JIAN
#include <jian/jian.h>
#define TRUNK_LOG_DEBUG JLOGGER_DEBUG_S("pnd_log")
#define TRUNK_LOG_INFO JLOGGER_INFO_S("pnd_log")
#define TRUNK_LOG_WARN JLOGGER_WARN_S("pnd_log")
#define TRUNK_LOG_ERROR JLOGGER_ERROR_S("pnd_log")
#define TRUNK_LOG_FATAL JLOGGER_CRITICAL_S("pnd_log")
#define TRUNK_LOG_EVERY_N_DEBUG(n) JLOGGER_DEBUG_S_EN(n, "pnd_log")
#define TRUNK_LOG_EVERY_N_INFO(n) JLOGGER_INFO_S_EN(n, "pnd_log")
#define TRUNK_LOG_EVERY_N_WARN(n) JLOGGER_WARN_S_EN(n, "pnd_log")
#define TRUNK_LOG_EVERY_N_ERROR(n) JLOGGER_ERROR_S_EN(n, "pnd_log")
#else
#include <glog/logging.h>
#define TRUNK_LOG_DEBUG DLOG(INFO)
#define TRUNK_LOG_INFO LOG(INFO)
#define TRUNK_LOG_WARN LOG(WARNING)
#define TRUNK_LOG_ERROR LOG(ERROR)
#define TRUNK_LOG_FATAL LOG(FATAL)
#define TRUNK_LOG_EVERY_N_DEBUG(n) DLOG_EVERY_N(INFO, n)
#define TRUNK_LOG_EVERY_N_INFO(n) LOG_EVERY_N(INFO, n)
#define TRUNK_LOG_EVERY_N_WARN(n) LOG_EVERY_N(WARNING, n)
#define TRUNK_LOG_EVERY_N_ERROR(n) LOG_EVERY_N(ERROR, n)
#endif
