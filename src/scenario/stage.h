// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <memory>

#include "port/frame.h"
#include "scenario.h"
#include "task/task.h"

namespace trunk {
namespace pnd {
namespace scenario {

class Stage {
 public:
  Stage() = default;
  Stage(std::string s) : name_(s){};
  enum LCStatus { READY = 1, RUNNING = 2, FINISHED = 3, STOP_LC = 4 };
  virtual ~Stage() = default;

  tpnc::Status Process(const std::shared_ptr<port::Frame>& frame);
  virtual bool PreProcess(const std::shared_ptr<port::Frame>& frame) = 0;
  virtual void CalRefCost(const std::shared_ptr<port::Frame>& frame) = 0;

  tpnc::Status PlanOnReferenceLine(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info);
  // Stage状态机跳转
  virtual void Handle(Scenario* context,
                      const std::shared_ptr<port::Frame>& frame) = 0;
  std::string GetName() const { return name_; }

  // 一些公共的函数

 protected:
  std::string name_;
  std::vector<std::unique_ptr<task::Task>> task_list_;
};

}  // namespace scenario
}  // namespace pnd
}  // namespace trunk
