// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include "param/scenario_lane_follow_param.h"
#include "scenario/stage.h"

namespace trunk {
namespace pnd {
namespace scenario {

class LaneFollowStage : public Stage {
 public:
  LaneFollowStage();

  virtual ~LaneFollowStage() = default;

  bool PreProcess(const std::shared_ptr<port::Frame>& frame) override;

  void CalRefCost(const std::shared_ptr<port::Frame>& frame) override;

  void Handle(Scenario* context,
              const std::shared_ptr<port::Frame>& frame) override;

 private:
  // 常量定义
  const double kLateralBuffer = 0.5;         // 横向缓冲距离
  const double kFrontBuffer = 6.0;           // 前向缓冲距离
  const double kStopCostInLaneChange = 3.0;  // 变道时的stop障碍物额外代价

  // 计算并找出最小代价的参考线
  port::ReferenceLineInfo* FindMinCostReferenceLine(
      const std::shared_ptr<port::Frame>& frame);

  // 添加各种代价计算
  void AddPlanningFailureCost(port::ReferenceLineInfo& reference_line_info);
  void AddObstacleCosts(const std::shared_ptr<port::Frame>& frame,
                        port::ReferenceLineInfo& reference_line_info);
  void AddTrajectoryShapeCost(const std::shared_ptr<port::Frame>& frame,
                              port::ReferenceLineInfo& reference_line_info);
  void AddTargetLaneCost(const std::shared_ptr<port::Frame>& frame,
                         port::ReferenceLineInfo& reference_line_info);
  void AddLaneChangeCost(const std::shared_ptr<port::Frame>& frame,
                         port::ReferenceLineInfo& reference_line_info);

  // 障碍物相关的代价计算
  bool IsObstacleValid(const std::shared_ptr<port::Frame>& frame,
                       const port::Obstacle* obstacle,
                       const port::ReferenceLineInfo& reference_line_info);
  void AddParallelObstacleCost(const std::shared_ptr<port::Frame>& frame,
                               port::ReferenceLineInfo& reference_line_info,
                               const port::Obstacle* obstacle);
  void AddOvertakeCost(const std::shared_ptr<port::Frame>& frame,
                       port::ReferenceLineInfo& reference_line_info,
                       const port::Obstacle* obstacle);
  void AddStopObstacleCost(port::ReferenceLineInfo& reference_line_info,
                           const port::Obstacle* obstacle, double dest_stop_s);

  port::ReferenceLineInfo* FilterSelectedLine(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* selected_line);

  // 更新车道变换状态和应用选中的参考线
  void UpdateLaneChangeStatus(const std::shared_ptr<port::Frame>& frame,
                              port::ReferenceLineInfo* selected_line);
  void ApplySelectedLine(const std::shared_ptr<port::Frame>& frame,
                         port::ReferenceLineInfo* selected_line);

  // 等待相关函数
  bool FinishContinusLCWait();
  bool FinishCancelLCWait();

  // 变道状态检查相关函数
  bool ShouldCancelLaneChange(
      const std::shared_ptr<port::Frame>& frame,
      const port::ReferenceLineInfo* selected_line) const;
  bool HasFinishedLaneChange(
      const std::shared_ptr<port::Frame>& frame,
      const port::ReferenceLineInfo* selected_line) const;
  bool CanStartLaneChange(const std::shared_ptr<port::Frame>& frame,
                          const port::ReferenceLineInfo* selected_line) const;

  // CalRefCost 相关辅助函数
  port::ReferenceLineInfo* CalculateReferenceLinesAndSelectBest(
      const std::shared_ptr<port::Frame>& frame);
  void CalculateAllCosts(const std::shared_ptr<port::Frame>& frame,
                         port::ReferenceLineInfo& reference_line_info);
  void EnsureCurrentLaneDrivable(const std::shared_ptr<port::Frame>& frame);
  bool AddDecCost(const port::Obstacle* obstacle,
                  double map2obstacle_speed_diff, double relative_distance,
                  const std::string& policy);
  bool updateSelectedLine(const port::ReferenceLineInfo& reference_line_info,
                          double min_cost, int road_priority,
                          const std::shared_ptr<port::Frame>& frame);

  LCStatus LC_status_ = READY;
  const LaneFollowStageParam* config_;
};

}  // namespace scenario
}  // namespace pnd
}  // namespace trunk
