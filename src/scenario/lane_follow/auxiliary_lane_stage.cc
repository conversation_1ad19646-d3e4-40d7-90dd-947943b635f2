// Copyright 2024, trunk Inc. All rights reserved

#include "auxiliary_lane_stage.h"

#include "log.h"
#include "param/scenario_lane_follow_param.h"
#include "port/reference_line_info.h"
#include "util/key.h"

namespace trunk {
namespace pnd {
namespace scenario {

REGISTER(trunk::pnd::scenario, Stage, AuxiliaryLane);

AuxiliaryLaneStage::AuxiliaryLaneStage() : Stage("auxiliary_lane_stage") {
  config_ = &tpnc::Singleton<tpnc::Config>::GetInstance()
                 ->GetConfig<ScenarioLaneFollowParam>("lane_follow")
                 .auxiliary_lane();
  for (const auto& task : config_->task_list()) {
    task_list_.push_back(
        tpnc::Factory<task::Task>::GetInstance().GetClassByName(task));
  }
}

bool AuxiliaryLaneStage::PreProcess(const std::shared_ptr<port::Frame>& frame) {
  if (!frame->target_reference_line_info()) {
    return false;
  }
  complete_ = true;
  auto& target_lane = *frame->target_reference_line_info();
  for (const auto* obstacle : target_lane.obstacles().Items()) {
    if (obstacle->is_virtual()) {
      continue;
    }
    // 非该车道上的车且距离较远，则跳过
    // 不超车的条件为侵入车道距离<0.75-lateral_safe_distance
    if (obstacle->sl_boundary().max_l() * obstacle->sl_boundary().min_l() >
            0.0 &&
        std::min(std::abs(obstacle->sl_boundary().max_l()),
                 std::abs(obstacle->sl_boundary().min_l())) >
            util::truck_.expand_width() + config_->lateral_safe_distance() -
                util::kHighwayLaneHalfWidth) {
      continue;
    }
    // 相对与障碍物车辆的速度
    const double ego2obstacle_speed_diff =
        frame->init_point().v() - obstacle->obj().velocity();
    // 如果障碍物车辆在侧边，则有侧边的碰撞区间
    // 若比ego快，则安全区间应该在车后，否则在车前
    // 该区间的长度这里采用自车长+base+相对速度时距
    const double collision_interval_vector =
        config_->side_collision_interval_time() * ego2obstacle_speed_diff;
    const double redundant_speed = std::max(
        DBL_EPSILON, frame->init_point().v() *
                         config_->side_collision_redundant_speed_ratio());
    const double max_redundant_value =
        config_->side_collision_redundant_interval_time() *
        frame->init_point().v();
    const double abs_speed_diff = std::abs(ego2obstacle_speed_diff);
    const double abs_linear_redundant =
        abs_speed_diff > redundant_speed
            ? 0.0
            : max_redundant_value * (1.0 - abs_speed_diff / redundant_speed);
    const double abs_saturation_redundant =
        abs_speed_diff > redundant_speed ? 0.0 : max_redundant_value;
    const bool last_is_not_drivable =
        target_lane.last_reference_line_info() &&
        !target_lane.last_reference_line_info()->is_drivable().first;
    // 自车快时， 后方线性冗余，反之前方线性冗余
    const double collision_interval_min =
        std::min(0.0, collision_interval_vector) -
        config_->side_collision_interval_base() - util::truck_.base2tail() -
        (ego2obstacle_speed_diff < 0.0 ? abs_saturation_redundant
                                       : abs_linear_redundant) -
        (last_is_not_drivable
             ? config_->side_collision_interval_hysteresis_length()
             : 0.0);
    const double collision_interval_max =
        std::max(0.0, collision_interval_vector) +
        config_->side_collision_interval_base() + util::truck_.base2front() +
        (ego2obstacle_speed_diff > 0.0 ? abs_saturation_redundant
                                       : abs_linear_redundant) +
        (last_is_not_drivable
             ? config_->side_collision_interval_hysteresis_length()
             : 0.0);

    // 辅路目标车道不禁用
    // 上一帧走辅路，这一目标车道在主路，则主路不禁
    // 障碍物车辆在区间内
    if ((target_lane.target_info() &&
         (!target_lane.target_info()->IsAuxiliaryLane() ||
          target_lane.target_info()->IsAuxiliaryLane() &&
              obstacle->sl_boundary().max_s() >=
                  target_lane.reference_line().front().s())) &&
        obstacle->sl_boundary().min_s() <= collision_interval_max &&
        obstacle->sl_boundary().max_s() >= collision_interval_min) {
      // target_lane.set_is_drivable({false, "auxiliary_lane_stage collision"});
      complete_ = false;
      port::ReferenceLineInfo* cur_lane = nullptr;
      for (auto& ref_lane : *frame->mutable_reference_lines_info()) {
        if (!(frame->last_drived_reference_line_info() &&
              frame->last_drived_reference_line_info() ==
                  ref_lane.last_reference_line_info())) {
          ref_lane.set_is_drivable(
              {false, "auxiliary_lane_stage collision " +
                          std::to_string(obstacle->obj().id())});
        } else {
          ref_lane.set_is_drivable({true, "auxiliary_lane_stage"});
          cur_lane = &ref_lane;
        }
      }
      if (cur_lane) {
        tpnc::TurnSignal turn_signal = tpnc::TurnSignal::NO_TURN;
        for (auto& info : cur_lane->infos()) {
          if (target_lane.target_info()->lane_id == info.l_lane_id) {
            turn_signal = tpnc::TurnSignal::LEFT_TURN;
            break;
          } else if (target_lane.target_info()->lane_id == info.r_lane_id) {
            turn_signal = tpnc::TurnSignal::RIGHT_TURN;
            break;
          }
        }

        // 上下匝道前不加速，避免快速走到道路尽头以及加减速 VTI-14119
        for (auto& p : *cur_lane->mutable_reference_line()) {
          p.set_map_limit_speed(
              std::min(p.map_limit_speed(), frame->init_point().v()));
        }
        for (auto& info : *cur_lane->mutable_infos()) {
          info.speed_limit =
              std::min(info.speed_limit, frame->init_point().v());
        }

        if (turn_signal == tpnc::TurnSignal::NO_TURN) {
          TRUNK_LOG_WARN << "cut check failed!";
          return true;
        }
        double boundary_l =
            turn_signal == tpnc::TurnSignal::RIGHT_TURN
                ? -util::kHighwayLaneHalfWidth + util::truck_.width() * 0.5
                : util::kHighwayLaneHalfWidth - util::truck_.width() * 0.5;

        // const auto& tar_obs =
        // cur_lane->obstacles().Find(obstacle->obj().id()); const double
        // boundary_l = tar_obs->sl_boundary().min_l() > 0.0
        //                               ? tar_obs->sl_boundary().min_l() -
        //                                     util::truck_.expand_width() * 0.5
        //                               : tar_obs->sl_boundary().max_l() +
        //                                     util::truck_.expand_width() *
        //                                     0.5;
        cur_lane->set_ref_l(boundary_l);
        return true;
        // TRUNK_LOG_INFO << "id: " << tar_obs->obj().id()
        // << "l_boundary: " << tar_obs->sl_boundary().min_l()
        // << ", " << tar_obs->sl_boundary().max_l()
        // << ", auxiliary lane bias is " << cur_lane->ref_l();
      } else {
        // 严格来讲，上一帧行驶的轨迹如果不存在则planning
        // reset，不会进入这个stage，因此该指针不会为空
        TRUNK_LOG_WARN << "cur_lane "
                          "是空指针，程序崩溃，这里暴露空指针方便发现问题de"
                          "bug，在稳定后处理";
        TRUNK_LOG_INFO << "last drived ref line"
                       << (uint64_t)frame->last_drived_reference_line_info();
        assert(cur_lane != nullptr);
      }
      return true;
    }
  }
  // if (frame->current_lane()) {
  //   target_lane.set_qp_sl_end_s(
  //       frame->current_lane()->reference_line().back().s() - 6.0);
  // }
  for (auto& ref_lane : *frame->mutable_reference_lines_info()) {
    if (&ref_lane == frame->target_reference_line_info()) {
      ref_lane.set_is_drivable({true, "auxiliary_lane_stage"});
    } else {
      ref_lane.set_is_drivable({false, "auxiliary_lane_stage"});
    }
  }
  return true;
}
void AuxiliaryLaneStage::CalRefCost(const std::shared_ptr<port::Frame>& frame) {
  // 1. 找出最小代价的参考线
  auto selected_line = FindMinCostReferenceLine(frame);
  if (!selected_line) {
    TRUNK_LOG_ERROR << "Failed to find valid reference line";
    return;
  }

  // 2. 更新车道变换状态
  UpdateLaneChangeStatus(frame, selected_line);

  // 3. 应用选中的参考线
  ApplySelectedLine(frame, selected_line);
}

// 计算并找出最小代价的参考线
port::ReferenceLineInfo* AuxiliaryLaneStage::FindMinCostReferenceLine(
    const std::shared_ptr<port::Frame>& frame) {
  double min_cost = std::numeric_limits<double>::max();
  port::ReferenceLineInfo* selected_line = nullptr;

  // 遍历所有参考线
  for (auto& reference_line_info : *frame->mutable_reference_lines_info()) {
    if (!reference_line_info.is_drivable().first) {
      continue;
    }

    AddPlanningFailureCost(reference_line_info);
    AddObstacleCosts(frame, reference_line_info);
    AddTrajectoryShapeCost(frame, reference_line_info);
    AddTargetLaneCost(frame, reference_line_info);

    // 更新最优参考线
    if (updateSelectedLine(reference_line_info, min_cost, frame)) {
      min_cost = reference_line_info.total_cost();
      selected_line = &reference_line_info;
    }
  }

  // 进入匝道时不使用滤波
  return selected_line;
}
bool AuxiliaryLaneStage::updateSelectedLine(
    const port::ReferenceLineInfo& reference_line_info, double min_cost,
    const std::shared_ptr<port::Frame>& frame) {
  if (!reference_line_info.is_drivable().first) {
    return false;
  }

  if (reference_line_info.total_cost() < min_cost) {
    return true;
  }
  return false;
}

void AuxiliaryLaneStage::AddPlanningFailureCost(
    port::ReferenceLineInfo& reference_line_info) {
  if (reference_line_info.planning_failure_cause() != "--") {
    reference_line_info.AddCost(100);
  }
}

void AuxiliaryLaneStage::AddObstacleCosts(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo& reference_line_info) {
  // 确定是否有终点停车位置
  double dest_stop_s = std::numeric_limits<double>::max();
  for (const auto* obstacle : reference_line_info.obstacles().Items()) {
    if (obstacle->stop_reason() == port::StopReason::kDestination) {
      dest_stop_s = obstacle->sl_boundary().min_s();
      break;
    }
  }

  // 遍历所有障碍物,计算相关代价
  for (const auto* obstacle : reference_line_info.obstacles().Items()) {
    if (obstacle->is_virtual()) {
      continue;
    }
    // 检查障碍物是否需要考虑
    if (!IsObstacleValid(frame, obstacle, reference_line_info)) {
      continue;
    }
    // 计算平行障碍物代价
    AddParallelObstacleCost(frame, reference_line_info, obstacle);
    // 计算停车障碍物代价
    if (obstacle->lon_decision() == port::ObstacleDecision::kStop) {
      AddStopObstacleCost(reference_line_info, obstacle, dest_stop_s);
    }
  }
}

bool AuxiliaryLaneStage::IsObstacleValid(
    const std::shared_ptr<port::Frame>& frame, const port::Obstacle* obstacle,
    const port::ReferenceLineInfo& reference_line_info) {
  if (reference_line_info.policy() == "lane_follow") {
    // 直行时，车辆过滤
    if (obstacle->sl_boundary().min_l() > util::truck_.expand_width() ||
        obstacle->sl_boundary().max_l() < -util::truck_.expand_width()) {
      return false;
    }
  }
  if (reference_line_info.policy() == "lane_change") {
    // 变道时车辆过滤
    const double half_width = 0.5 * util::truck_.width();
    bool is_in_target_reigon = false;
    for (const auto& pt : obstacle->obj().xy_contour()) {
      if (tutil::GetClosestLatDistance(
              reference_line_info.lane_boundaries().first, pt) *
              tutil::GetClosestLatDistance(
                  reference_line_info.lane_boundaries().second, pt) <
          0) {
        // 在目标车道上
        is_in_target_reigon = true;
        break;
      }
    }
    if (!is_in_target_reigon) {
      return false;
    }
    if (obstacle->obj().is_static() &&
        obstacle->lon_decision() != port::ObstacleDecision::kStop) {
      // 主要针对锥桶，如果轨迹和障碍物没有碰撞，则过滤掉
      return false;
    }
    const double kLateralBuffer = 0.5;
    // 过滤正前方或者正后方的车辆
    const double ego_l = reference_line_info.sl_init_point().l();
    if (obstacle->sl_boundary().max_l() < ego_l + half_width + kLateralBuffer &&
        obstacle->sl_boundary().min_l() > ego_l - half_width - kLateralBuffer) {
      return false;
    }
  }

  return true;
}

void AuxiliaryLaneStage::AddParallelObstacleCost(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo& reference_line_info,
    const port::Obstacle* obstacle) {
  // 非自车后方的车辆
  if (reference_line_info.policy() != "lane_change") {
    return;
  }

  const double ego_velocity = frame->init_point().v();
  const double obs_velocity = obstacle->obj().velocity();

  // 计算障碍物与自车的相对位置范围
  const double min_s =
      -config_->side_collision_interval_base() - util::truck_.base2tail() -
      config_->min_ttc_threshold() * std::max(0.0, obs_velocity - ego_velocity);

  const double max_s =
      config_->side_collision_interval_base() + util::truck_.base2front() +
      config_->min_ttc_threshold() * std::max(0.0, ego_velocity - obs_velocity);
  if (obstacle->sl_boundary().max_s() >= min_s &&
      obstacle->sl_boundary().min_s() <= max_s) {
    TRUNK_LOG_INFO << "obs: " << obstacle->obj().id()
                   << ", Add parall obstacle cost";
    reference_line_info.AddCost(500);
  }
}

void AuxiliaryLaneStage::AddStopObstacleCost(
    port::ReferenceLineInfo& reference_line_info,
    const port::Obstacle* obstacle, double dest_stop_s) {
  if (obstacle->lon_decision() != port::ObstacleDecision::kStop ||
      reference_line_info.policy() != "lane_change") {
    return;
  }

  if (obstacle->sl_boundary().min_s() < dest_stop_s) {
    TRUNK_LOG_INFO << "obs: " << obstacle->obj().id()
                   << ", Add stop obstacle cost";
    reference_line_info.AddCost(200);
  }
}

void AuxiliaryLaneStage::AddTrajectoryShapeCost(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo& reference_line_info) {
  if (LC_status_ != RUNNING ||
      frame->last_drived_reference_line_info_mapping() == nullptr ||
      &reference_line_info ==
          frame->last_drived_reference_line_info_mapping()) {
    return;
  }

  const auto max_l = std::max_element(
      reference_line_info.path_data().qp_sl_path().begin(),
      reference_line_info.path_data().qp_sl_path().end(),
      [](const port::PathSLPoint& a, const port::PathSLPoint& b) {
        return std::abs(a.l()) < std::abs(b.l());
      });

  if (max_l != reference_line_info.path_data().qp_sl_path().end() &&
      std::max(std::abs(max_l->l() + util::truck_.half_expand_width() / 3.0),
               std::abs(max_l->l() - util::truck_.half_expand_width()) / 3.0) >
          util::kHighwayLaneHalfWidth) {
    reference_line_info.AddCost(2000);
  }
}

void AuxiliaryLaneStage::UpdateLaneChangeStatus(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* selected_line) {
  // 状态跳转
  if (CanStartLaneChange(frame, selected_line)) {
    LC_status_ = RUNNING;  // 开始变道
  } else if (ShouldCancelLaneChange(frame, selected_line)) {
    LC_status_ = STOP_LC;  // 取消变道
  } else if (HasFinishedLaneChange(frame, selected_line)) {
    LC_status_ = FINISHED;  // 完成变道
  }

  // 处理完成变道和取消变道的等待状态
  if (LC_status_ == FINISHED) {
    // 等待下一次变道
    LC_status_ = READY;
  } else if (LC_status_ == STOP_LC) {
    LC_status_ = READY;
  }
}

void AuxiliaryLaneStage::ApplySelectedLine(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* selected_line) {
  // 车道选择
  for (auto& reference_line_info : *frame->mutable_reference_lines_info()) {
    if (reference_line_info.is_drivable().first &&
        &reference_line_info != selected_line) {
      reference_line_info.set_is_drivable({false, "non min cost ban"});
    }
  }
}

void AuxiliaryLaneStage::AddTargetLaneCost(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo& reference_line_info) {
  // 非目标车道成本
  if (frame->target_reference_line_info() != nullptr &&
      &reference_line_info != frame->target_reference_line_info()) {
    reference_line_info.AddTrajectoryCost(20.5);
  }
}

void AuxiliaryLaneStage::AddLaneChangeCost(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo& reference_line_info) {
  // 变道成本
  if (reference_line_info.IsLaneChangeLane()) {
    reference_line_info.AddTrajectoryCost(10);
    // 上一帧在变道,这一帧的变道成本增加 防止频繁变道
    if (util::last_frame_ != nullptr &&
        util::last_frame_->turn_signal() != tpnc::TurnSignal::NO_TURN) {
      reference_line_info.AddTrajectoryCost(10);
    }
  }
}

// 检查是否需要取消变道
bool AuxiliaryLaneStage::ShouldCancelLaneChange(
    const std::shared_ptr<port::Frame>& frame,
    const port::ReferenceLineInfo* selected_line) const {
  return frame->last_drived_reference_line_info_mapping() != nullptr &&
         LC_status_ == RUNNING &&
         selected_line != frame->last_drived_reference_line_info_mapping();
}

// 检查是否完成变道
bool AuxiliaryLaneStage::HasFinishedLaneChange(
    const std::shared_ptr<port::Frame>& frame,
    const port::ReferenceLineInfo* selected_line) const {
  return LC_status_ == RUNNING &&
         frame->last_drived_reference_line_info_mapping() != nullptr &&
         selected_line == frame->last_drived_reference_line_info_mapping() &&
         selected_line == frame->current_reference_line_info();
}

// 检查是否可以开始变道
bool AuxiliaryLaneStage::CanStartLaneChange(
    const std::shared_ptr<port::Frame>& frame,
    const port::ReferenceLineInfo* selected_line) const {
  return LC_status_ == READY &&
         selected_line != frame->current_reference_line_info();
}

void AuxiliaryLaneStage::Handle(Scenario* context,
                                const std::shared_ptr<port::Frame>& frame) {
  if ((frame->current_reference_line_info() != nullptr &&
       frame->target_reference_line_info() != nullptr &&
       frame->current_reference_line_info() ==
           frame->target_reference_line_info())) {
    // 先暂定汇入匝道后，目标车道和当前车道一致
    // TODO:还需要一个当前车辆已经不在匝道的标志
    context->SetCurrentStage("LaneFollow");
    TRUNK_LOG_INFO << "*****LaneFollow Stage!*****";
  }
}

}  // namespace scenario
}  // namespace pnd
}  // namespace trunk
