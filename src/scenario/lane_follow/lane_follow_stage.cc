// Copyright 2023, trunk Inc. All rights reserved

#include "lane_follow_stage.h"

#include <limits>

#include "log.h"
#include "port/reference_line_info.h"
#include "trunk/common/port/base_point.h"
#include "trunk/common/util/coordinate/transformfrenet.h"
#include "util/key.h"
#include "util/util.h"

namespace trunk {
namespace pnd {
namespace scenario {

REGISTER(trunk::pnd::scenario, Stage, LaneFollow);

LaneFollowStage::LaneFollowStage() : Stage("lane_follow_stage") {
  config_ = &tpnc::Singleton<tpnc::Config>::GetInstance()
                 ->GetConfig<ScenarioLaneFollowParam>("lane_follow")
                 .lane_follow();
  for (const auto& task : config_->task_list()) {
    task_list_.push_back(
        tpnc::Factory<task::Task>::GetInstance().GetClassByName(task));
  }
}

bool LaneFollowStage::PreProcess(const std::shared_ptr<port::Frame>& frame) {
  // 人工驾驶
  if (frame->env().vehicle().driver_status() ||
      frame->last_drived_reference_line_info() == nullptr) {
    for (auto& ref_lane : *frame->mutable_reference_lines_info()) {
      if (&ref_lane == frame->current_reference_line_info()) {
        ref_lane.set_is_drivable({true, "manual driving"});
      } else {
        ref_lane.set_is_drivable({false, "manual driving"});
      }
    }
    return true;
  }
  double min_cost = std::numeric_limits<double>::max();
  port::ReferenceLineInfo* selected_line = nullptr;

  // infinite series
  const double max_cost_coefficient =
      1.0 / (1.0 - config_->cost_attenuation_factor());
  const double max_overtake_cost =
      config_->weight_overtake() *
      (config_->lane_change_cost() + config_->map_target_lane_cost());
  const double max_overtake_cost_once =
      max_overtake_cost / max_cost_coefficient;
  int drivable_lanes_num = 0;
  for (auto& reference_line_info : *frame->mutable_reference_lines_info()) {
    const auto& last_nearest_reference_line_info =
        reference_line_info.last_reference_line_info();
    if (last_nearest_reference_line_info) {
      reference_line_info.AddCost(config_->cost_attenuation_factor() *
                                  last_nearest_reference_line_info->cost());
    }
    // determine if there is a destination on reference line.
    double dest_stop_s = -1.0;
    for (const auto* obstacle : reference_line_info.obstacles().Items()) {
      if (obstacle->stop_reason() == port::StopReason::kDestination) {
        dest_stop_s = obstacle->sl_boundary().min_s();
        break;
      }
    }

    for (const auto* obstacle : reference_line_info.obstacles().Items()) {
      if (obstacle->is_virtual()) {
        continue;
      }
      // 非该车道上的车且距离较远，则跳过
      // 不超车的条件为侵入车道距离<0.75-lateral_safe_distance
      if (obstacle->sl_boundary().max_l() * obstacle->sl_boundary().min_l() >
              0.0 &&
          std::min(std::abs(obstacle->sl_boundary().max_l()),
                   std::abs(obstacle->sl_boundary().min_l())) >
              // XXX
              util::truck_.expand_width() + config_->lateral_safe_distance() -
                  util::kHighwayLaneHalfWidth) {
        continue;
      }
      // 相对与障碍物车辆的速度
      const double ego2obstacle_speed_diff =
          frame->env().vehicle().velocity() - obstacle->obj().velocity();
      // 障碍物速度与地图限速差
      const double map2obstacle_speed_diff = std::max(
          0.0, std::max(frame->init_point().v(),
                        // XXX: 超车时会加速，便于连续超车
                        reference_line_info.infos().back().speed_limit) -
                   obstacle->obj().velocity());
      // ego车头到障碍物车尾距离
      const double distance_obstacle2ego_front =
          obstacle->sl_boundary().min_s() - frame->init_point().s() -
          util::truck_.base2front();
      // ego车尾到障碍物车头距离
      const double distance_ego_tail2obstacle = frame->init_point().s() -
                                                util::truck_.base2tail() -
                                                obstacle->sl_boundary().max_s();
      const double distance_ego2line =
          std::abs(tutil::TransformFrenet::TransformToFrenet(
                       reference_line_info.reference_line(), tport::Point2D())
                       .l());  // 自车距离车道线的距离
      const auto lane_width = reference_line_info.GetLaneWidth(0.0);
      const double lane_width_half =
          0.5 * (lane_width.left_width - lane_width.right_width);
      // 上一帧走的不是这条reference_line，上一帧规划失败时用距离判断
      if (reference_line_info.is_drivable().first &&
          (!(frame->last_drived_reference_line_info() &&
             frame->last_drived_reference_line_info() ==
                 reference_line_info.last_reference_line_info()) ||
           (frame->last_drived_reference_line_info() == nullptr &&
            distance_ego2line > lane_width_half))) {
        // 不是当前行驶的车道

        // 如果障碍物车辆在侧边，则有侧边的碰撞区间
        // 若比ego快，则安全区间应该在车后，否则在车前
        // 该区间的长度这里采用自车长+base+相对速度时距
        const double collision_interval_vector =
            config_->side_collision_interval_time() * ego2obstacle_speed_diff;
        const bool last_is_not_drivable =
            last_nearest_reference_line_info &&
            !last_nearest_reference_line_info->is_drivable().first;
        // 自车快时， 后方线性冗余，反之前方线性冗余
        const double collision_interval_min =
            std::min(0.0, collision_interval_vector) -
            config_->side_collision_interval_base() - util::truck_.base2tail() -
            (last_is_not_drivable
                 ? config_->side_collision_interval_hysteresis_length()
                 : 0.0);
        const double collision_interval_max =
            std::max(0.0, collision_interval_vector) +
            config_->side_collision_interval_base() +
            util::truck_.base2front() +
            (last_is_not_drivable
                 ? config_->side_collision_interval_hysteresis_length()
                 : 0.0);

        if ((obstacle->sl_boundary().min_s() <= collision_interval_max &&
             obstacle->sl_boundary().max_s() >= collision_interval_min)) {
          reference_line_info.set_is_drivable(
              {false, "lane_follow_stage collision"});
          reference_line_info.AddCost(config_->in_collision_interval_cost());
        }
      } else if (config_->enable_cancel_lane_change() &&
                 !reference_line_info.infos().front().IsAuxiliaryLane() &&
                 util::last_frame_ != nullptr &&
                 util::last_frame_->turn_signal() !=
                     tpnc::TurnSignal::NO_TURN &&
                 frame->last_drived_reference_line_info_mapping() ==
                     &reference_line_info &&
                 distance_ego2line > lane_width_half &&
                 std::abs(reference_line_info.sl_path_init_point().l()) >
                     util::kHighwayLaneHalfWidth &&
                 obstacle->sl_boundary().min_s() <=
                     reference_line_info.sl_init_point().s() +
                         util::truck_.base2front()) {
        // 为变道的车道 只考虑后方的车辆

        //  在变道的车道行驶，且变道完成度不足一半
        const double cancel_lane_change_time_threshold =
            config_->cancel_lane_change_time_threshold() *
            tutil::clamp(
                (distance_ego2line - lane_width_half) / lane_width_half, 0.0,
                1.0);
        if (
            // 要有碰撞了
            (obstacle->sl_boundary().min_s() <=
                 reference_line_info.sl_init_point().s() +
                     util::truck_.base2front() &&
             obstacle->sl_boundary().max_s() >=
                 reference_line_info.sl_init_point().s() -
                     util::truck_.base2tail()) ||
            (config_->cancel_lane_change_speed_threshold() +
                     ego2obstacle_speed_diff <
                 0.0 &&
             -util::truck_.base2tail() - obstacle->sl_boundary().max_s() -
                     config_->side_collision_interval_base() +
                     ego2obstacle_speed_diff *
                         cancel_lane_change_time_threshold <
                 0.0)) {
          // 障碍物在侧边或者后方障碍物加速了
          // 满足变道条件
          // 取消变道原车道查找
          port::ReferenceLineInfo* origin_lane = nullptr;
          auto* info = reference_line_info.GetInfoByS(
              reference_line_info.sl_path_init_point().s());
          if (info) {
            if (util::last_frame_->turn_signal() ==
                tpnc::TurnSignal::LEFT_TURN) {
              origin_lane = frame->GetReferenceLineById(info->r_lane_id);
            } else {
              origin_lane = frame->GetReferenceLineById(info->l_lane_id);
            }
          }
          if (origin_lane) {
            TRUNK_LOG_INFO
                << "cur_reference_line_info: "
                << frame->last_drived_reference_line_info_mapping()->id()
                << ", lane_cancel!";
            reference_line_info.set_is_drivable({false, "cancel lane change"});
            origin_lane->set_is_drivable({true, "cancel lane change"});
            frame->set_cancel_lane_change_flag(true);
            reference_line_info.AddCost(config_->in_collision_interval_cost());
          } else {
            TRUNK_LOG_WARN << "cancel lane change failure!";
            if (info) {
              TRUNK_LOG_WARN << "lane_id: " << info->lane_id
                             << "--l: " << info->l_lane_id
                             << ", --r: " << info->r_lane_id;
            }
          }

          return true;
        }
      }

      if (!frame->traffic_info().IsMerge() && config_->enable_overtake()) {
        // 超车-当前车道代价计算
        const double overtake_cost =
            obstacle->sl_boundary().min_s() > 0.0
                ? max_overtake_cost_once *
                      util::SaturationRatio(map2obstacle_speed_diff,
                                            config_->overtake_min_speed(),
                                            config_->overtake_max_speed()) *
                      util::SaturationDecreasingRatio(
                          distance_obstacle2ego_front,
                          config_->overtake_min_distance(),
                          config_->overtake_max_distance() +
                              std::pow(
                                  util::ReLU(map2obstacle_speed_diff),
                                  config_
                                      ->overtake_distance_exponential_weight()))
                : 0.0;

        const double sat_overtake_cost =
            tutil::clamp(overtake_cost, 0.0, max_overtake_cost_once);
        reference_line_info.AddCost(sat_overtake_cost);

        if (!obstacle->obj().is_static()) {
          continue;
        }
        if (obstacle->lon_decision() == port::ObstacleDecision::kStop) {
          bool add_stop_obstacle_cost = false;
          if (dest_stop_s < 0.0) {
            add_stop_obstacle_cost = true;
          } else if (obstacle->sl_boundary().min_s() < dest_stop_s) {
            add_stop_obstacle_cost = true;
          }
          if (add_stop_obstacle_cost) {
            reference_line_info.AddCost(
                config_->reference_line_static_obs_cost());
          }
        }
      }
    }  // end of obstacles for
    // 非目标车道成本
    if (&reference_line_info != frame->target_reference_line_info()) {
      reference_line_info.AddTrajectoryCost(config_->map_target_lane_cost());
    }
    // if (!reference_line_info.target_info() ||
    //     !reference_line_info.target_info()->is_target) {
    //   reference_line_info.AddTrajectoryCost(config_->map_target_lane_cost());
    // }
    // 变道成本
    if (reference_line_info.IsLaneChangeLane()) {
      reference_line_info.AddTrajectoryCost(config_->lane_change_cost());
      // 上一帧在变道, 这一帧的变道成本增加 防止频繁变道
      if (util::last_frame_ != nullptr &&
          util::last_frame_->turn_signal() != tpnc::TurnSignal::NO_TURN) {
        reference_line_info.AddTrajectoryCost(
            config_->lane_change_cost_at_lane_change());
      }
    }
    if (reference_line_info.is_drivable().first) {
      ++drivable_lanes_num;
      if (reference_line_info.total_cost() < min_cost) {
        min_cost = reference_line_info.total_cost();
        selected_line = &reference_line_info;
      }
    }
  }  // end of reference lines info
  for (auto& reference_line_info : *frame->mutable_reference_lines_info()) {
    if (reference_line_info.is_drivable().first &&
        &reference_line_info != selected_line) {
      reference_line_info.set_is_drivable({false, "non min cost ban"});
      --drivable_lanes_num;
    }
  }
  assert(frame->current_reference_line_info() != nullptr);
  // 将当前道路和目标车道解除禁用
  frame->current_reference_line_info()->set_is_drivable(
      {true, "non drivable lane, reset"});

  return true;
}
void LaneFollowStage::CalRefCost(const std::shared_ptr<port::Frame>& frame) {
  // 1. 找出最小代价的参考线
  auto selected_line = FindMinCostReferenceLine(frame);
  if (!selected_line) {
    TRUNK_LOG_ERROR << "Failed to find valid reference line";
    return;
  }

  // 2. 更新车道变换状态
  UpdateLaneChangeStatus(frame, selected_line);

  // 3. 应用选中的参考线
  ApplySelectedLine(frame, selected_line);

  // 4. 确保当前道路可行驶
  // EnsureCurrentLaneDrivable(frame);
}

// 计算并找出最小代价的参考线
port::ReferenceLineInfo* LaneFollowStage::FindMinCostReferenceLine(
    const std::shared_ptr<port::Frame>& frame) {
  double min_cost = std::numeric_limits<double>::max();
  int road_priority = -1;
  port::ReferenceLineInfo* selected_line = nullptr;

  // 遍历所有参考线
  for (auto& reference_line_info : *frame->mutable_reference_lines_info()) {
    if (!reference_line_info.is_drivable().first) {
      continue;
    }

    AddPlanningFailureCost(reference_line_info);
    AddObstacleCosts(frame, reference_line_info);
    AddTrajectoryShapeCost(frame, reference_line_info);
    AddTargetLaneCost(frame, reference_line_info);
    AddLaneChangeCost(frame, reference_line_info);

    // 更新最优参考线
    if (updateSelectedLine(reference_line_info, min_cost, road_priority,
                           frame)) {
      min_cost = reference_line_info.total_cost();
      road_priority = reference_line_info.road_priority();
      selected_line = &reference_line_info;
    }
  }

  // 应用滤波器以减少抖动
  return FilterSelectedLine(frame, selected_line);
}

void LaneFollowStage::EnsureCurrentLaneDrivable(
    const std::shared_ptr<port::Frame>& frame) {
  if (frame->current_reference_line_info()) {
    frame->current_reference_line_info()->set_is_drivable(
        {true, "current reference line must be drivable"});
  } else {
    TRUNK_LOG_WARN << "Current reference line is null";
  }
}

void LaneFollowStage::AddPlanningFailureCost(
    port::ReferenceLineInfo& reference_line_info) {
  if (reference_line_info.planning_failure_cause() != "--") {
    reference_line_info.AddCost(config_->speed_dp_faliure_cost());
  }
}

void LaneFollowStage::AddObstacleCosts(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo& reference_line_info) {
  // 确定是否有终点停车位置
  double dest_stop_s = std::numeric_limits<double>::max();
  for (const auto* obstacle : reference_line_info.obstacles().Items()) {
    if (obstacle->stop_reason() == port::StopReason::kDestination) {
      dest_stop_s = obstacle->sl_boundary().min_s();
      break;
    }
  }

  // 遍历所有障碍物,计算相关代价
  for (const auto* obstacle : reference_line_info.obstacles().Items()) {
    if (obstacle->is_virtual()) {
      continue;
    }

    // 检查障碍物是否需要考虑
    if (!IsObstacleValid(frame, obstacle, reference_line_info)) {
      continue;
    }

    // 计算平行障碍物代价
    AddParallelObstacleCost(frame, reference_line_info, obstacle);

    // 计算超车代价
    if (!obstacle->obj().is_static()) {
      AddOvertakeCost(frame, reference_line_info, obstacle);
    }

    // 计算停车障碍物代价
    if (obstacle->lon_decision() == port::ObstacleDecision::kStop) {
      AddStopObstacleCost(reference_line_info, obstacle, dest_stop_s);
    }
  }
}

bool LaneFollowStage::IsObstacleValid(
    const std::shared_ptr<port::Frame>& frame, const port::Obstacle* obstacle,
    const port::ReferenceLineInfo& reference_line_info) {
  if (reference_line_info.policy() == "lane_follow") {
    // 直行时，车辆过滤
    if (obstacle->sl_boundary().min_l() > util::truck_.half_expand_width() ||
        obstacle->sl_boundary().max_l() < -util::truck_.half_expand_width()) {
      return false;
    }
  }
  if (reference_line_info.policy() == "lane_change") {
    // 变道时车辆过滤
    const double ego_l = reference_line_info.self_sl().l();
    const double half_width = 0.5 * util::truck_.width();
    bool is_in_target_reigon = false;
    for (const auto& pt : obstacle->obj().xy_contour()) {
      if (tutil::GetClosestLatDistance(
              reference_line_info.lane_boundaries().first, pt) *
              tutil::GetClosestLatDistance(
                  reference_line_info.lane_boundaries().second, pt) <
          0) {
        // 在目标车道上
        is_in_target_reigon = true;
        break;
      }
    }
    // 车辆处于目标区域
    if (!is_in_target_reigon) {
      // 针对三变四车道
      if (obstacle->sl_boundary().max_l() > -util::kHighwayLaneHalfWidth &&
          obstacle->sl_boundary().min_l() < util::kHighwayLaneHalfWidth) {
        is_in_target_reigon = true;
      }
    }

    if (!is_in_target_reigon) {
      return false;
    }
    // 过滤正前方或者正后方的车辆
    if (obstacle->sl_boundary().max_l() < ego_l + half_width + kLateralBuffer &&
        obstacle->sl_boundary().min_l() > ego_l - half_width - kLateralBuffer) {
      return false;
    }
    // 过滤锥桶障碍物 等静态障碍物
    if (obstacle->obj().is_static()) {
      if (reference_line_info.self_sl().l() > 0.0 &&
          obstacle->sl_boundary().max_l() < -util::truck_.half_expand_width()) {
        return false;
      } else if (reference_line_info.self_sl().l() < 0.0 &&
                 obstacle->sl_boundary().min_l() >
                     util::truck_.half_expand_width()) {
        return false;
      }
    }
  }
  // 额外添加约束，针对VTI-17157场景
  // const auto& lane_width =
  //     reference_line_info.GetLaneWidth(obstacle->sl_boundary().max_s());
  // double kHighwayLaneHalfWidth =
  //     std::abs(lane_width.left_width - lane_width.right_width) / 2.0;
  // if (std::min(std::abs(obstacle->sl_boundary().max_l()),
  //              std::abs(obstacle->sl_boundary().min_l())) >
  //     kHighwayLaneHalfWidth) {
  //   // 不在当前车道
  //   return false;
  // }

  return true;
}

void LaneFollowStage::AddParallelObstacleCost(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo& reference_line_info,
    const port::Obstacle* obstacle) {
  // 非自车后方的车辆
  if (reference_line_info.policy() != "lane_change") {
    return;
  }

  const double ego_velocity = frame->init_point().v();
  const double obs_velocity = obstacle->obj().velocity();

  // 计算障碍物与自车的相对位置范围
  const double min_s = -config_->side_collision_interval_base() -
                       util::truck_.base2tail() -
                       config_->cancel_lane_change_time_threshold() *
                           std::max(0.0, obs_velocity - ego_velocity);

  const double max_s = config_->side_collision_interval_base() +
                       util::truck_.base2front() +
                       config_->cancel_lane_change_time_threshold() *
                           std::max(0.0, ego_velocity - obs_velocity);

  if (obstacle->sl_boundary().max_s() >= min_s &&
      obstacle->sl_boundary().min_s() <= max_s) {
    TRUNK_LOG_INFO << "obs: " << obstacle->obj().id()
                   << ", add parall obstacle cost";
    reference_line_info.AddCost(config_->paral_obs_cost());
  }
}

void LaneFollowStage::AddOvertakeCost(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo& reference_line_info,
    const port::Obstacle* obstacle) {
  if (!reference_line_info.cal_overtake_cost()) {
    return;
  }
  // 超车的障碍误另算。影响自车通行的车辆？
  // 必须严格处于目标车道上
  if (reference_line_info.policy() == "lane_change" &&
      !(obstacle->sl_boundary().max_l() < util::kHighwayLaneHalfWidth &&
        obstacle->sl_boundary().min_l() > -util::kHighwayLaneHalfWidth)) {
    return;
  }

  // 障碍物速度与地图限速差
  const double map2obstacle_speed_diff =
      std::max(0.0, std::max(frame->init_point().v(),
                             reference_line_info.infos().back().speed_limit) -
                        obstacle->obj().velocity());
  // ego车头到障碍物车尾距离
  const double distance_obstacle2ego_front = obstacle->sl_boundary().min_s() -
                                             frame->init_point().s() -
                                             util::truck_.base2front();

  // 变道时不允许减速 主要针对目标车道上有慢车的时候
  if (AddDecCost(obstacle, map2obstacle_speed_diff, distance_obstacle2ego_front,
                 reference_line_info.policy())) {
    TRUNK_LOG_INFO << "obs: " << obstacle->obj().id()
                   << ", add dec_cost: " << config_->dec_cost()
                   << ", map2obstacle_speed_diff: " << map2obstacle_speed_diff;
    reference_line_info.AddCost(config_->dec_cost());
  }

  // 超车代价计算
  if (distance_obstacle2ego_front < config_->overtake_min_distance() ||
      distance_obstacle2ego_front > config_->overtake_max_distance()) {
    // 超车范围
    return;
  }
  // ego车尾到障碍物车头距离
  const double distance_ego_tail2obstacle = frame->init_point().s() -
                                            util::truck_.base2tail() -
                                            obstacle->sl_boundary().max_s();
  // TTC计算
  const double ttc = distance_obstacle2ego_front /
                     (map2obstacle_speed_diff + tport::kMathEpsilon);
  const double max_cost_coefficient =
      1.0 / (1.0 - config_->cost_attenuation_factor());
  const double max_overtake_cost =
      config_->weight_overtake() *
      (config_->lane_change_cost() + config_->map_target_lane_cost());
  double max_overtake_cost_once = max_overtake_cost / max_cost_coefficient;

  if (reference_line_info.policy() == "lane_change") {
    // 非当前车道上的车辆ttc代价要高一点，
    // 目的：当两条车道都有满足ttc的超车预选车辆时，避免频繁变道
    max_overtake_cost_once =
        max_overtake_cost_once +
        std::abs(config_->lane_change_cost() - config_->map_target_lane_cost());
  }
  if (config_->enable_overtake() && ttc < config_->overtake_ttc_threshold()) {
    // 超车时距
    // TODO:增加超车等待阶段（评估换道可行性）
    // 1. 车速小于一定值 2. 车速的方差稳定在一定阈值内？
    TRUNK_LOG_INFO << "obs: " << obstacle->obj().id()
                   << ", add overtake_cost: " << max_overtake_cost_once
                   << ", ttc: " << ttc;
    reference_line_info.AddCost(max_overtake_cost_once);
    reference_line_info.set_speed_pressure(true);
    return;
  }
  // 如果当前车道上前方没有影响速度的障碍物
  // reference_line_info.set_speed_pressure(false);
}
bool LaneFollowStage::AddDecCost(const port::Obstacle* obstacle,
                                 double map2obstacle_speed_diff,
                                 double relative_distance,
                                 const std::string& policy) {  // 检查障碍物决策
  if (policy != "lane_change") {
    return false;
  }
  // 目标车道上是否存在影响自车通行效率的车辆
  if (obstacle->sl_boundary().min_s() <
      util::truck_.base2front() + kFrontBuffer) {
    return obstacle->lon_decision() == port::ObstacleDecision::kYield ||
           obstacle->lon_decision() == port::ObstacleDecision::kFollow;
  } else {
    // 前方慢车
    // TODO:对于前方稳定的慢车
    return map2obstacle_speed_diff > 2.78;
  }
  return false;
}

void LaneFollowStage::AddStopObstacleCost(
    port::ReferenceLineInfo& reference_line_info,
    const port::Obstacle* obstacle, double dest_stop_s) {
  if (obstacle->lon_decision() != port::ObstacleDecision::kStop ||
      reference_line_info.policy() != "lane_change") {
    return;
  }

  if (obstacle->sl_boundary().min_s() < dest_stop_s) {
    reference_line_info.AddCost(config_->reference_line_static_obs_cost());
    // if (reference_line_info.policy() == "lane_change") {
    //   reference_line_info.AddCost(kStopCostInLaneChange);
    // }
    TRUNK_LOG_INFO << "add stop_cost: "
                   << config_->reference_line_static_obs_cost();
  }
}

void LaneFollowStage::AddTrajectoryShapeCost(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo& reference_line_info) {
  if (LC_status_ != RUNNING ||
      frame->last_drived_reference_line_info_mapping() == nullptr ||
      &reference_line_info ==
          frame->last_drived_reference_line_info_mapping()) {
    return;
  }

  const auto max_l = std::max_element(
      reference_line_info.path_data().qp_sl_path().begin(),
      reference_line_info.path_data().qp_sl_path().end(),
      [](const port::PathSLPoint& a, const port::PathSLPoint& b) {
        return std::abs(a.l()) < std::abs(b.l());
      });

  if (max_l != reference_line_info.path_data().qp_sl_path().end() &&
      std::max(std::abs(max_l->l() + util::truck_.half_expand_width() / 3.0),
               std::abs(max_l->l() - util::truck_.half_expand_width()) / 3.0) >
          util::kHighwayLaneHalfWidth) {
    reference_line_info.AddCost(config_->trajectory_shape_cost());
  }
}

port::ReferenceLineInfo* LaneFollowStage::FilterSelectedLine(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* selected_line) {
  // 添加滤波,减弱由于感知跳变导致的决策不一致性
  if (frame->last_drived_reference_line_info_mapping() != nullptr &&
      frame->last_drived_reference_line_info_mapping()->is_drivable().first) {
    bool result =
        selected_line != frame->last_drived_reference_line_info_mapping();
    if (!util::FliterDecition(result, true, 5)) {
      selected_line = frame->last_drived_reference_line_info_mapping();
      selected_line->set_is_drivable(
          std::make_pair(true, "in filtered, use last"));
    }
  }
  return selected_line;
}

void LaneFollowStage::UpdateLaneChangeStatus(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* selected_line) {
  // 状态跳转
  if (CanStartLaneChange(frame, selected_line)) {
    LC_status_ = RUNNING;  // 开始变道
  } else if (ShouldCancelLaneChange(frame, selected_line)) {
    LC_status_ = STOP_LC;  // 取消变道
  } else if (HasFinishedLaneChange(frame, selected_line)) {
    LC_status_ = FINISHED;  // 完成变道
  }

  // 处理完成变道和取消变道的等待状态
  if (LC_status_ == FINISHED) {
    if (FinishContinusLCWait()) {
      LC_status_ = READY;
    } else {
      selected_line = frame->current_reference_line_info();
    }
  } else if (LC_status_ == STOP_LC) {
    if (FinishCancelLCWait()) {
      LC_status_ = READY;
    } else {
      selected_line = frame->current_reference_line_info();
    }
  }
}

void LaneFollowStage::ApplySelectedLine(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* selected_line) {
  // 车道选择
  for (auto& reference_line_info : *frame->mutable_reference_lines_info()) {
    if (reference_line_info.is_drivable().first &&
        &reference_line_info != selected_line) {
      reference_line_info.set_is_drivable({false, "non min cost ban"});
    }
  }
}

void LaneFollowStage::AddTargetLaneCost(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo& reference_line_info) {
  // 非目标车道成本
  if (frame->target_reference_line_info() != nullptr &&
      &reference_line_info != frame->target_reference_line_info()) {
    reference_line_info.AddTrajectoryCost(config_->map_target_lane_cost());
  }
}

void LaneFollowStage::AddLaneChangeCost(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo& reference_line_info) {
  // 变道成本
  if (reference_line_info.IsLaneChangeLane()) {
    reference_line_info.AddTrajectoryCost(config_->lane_change_cost());
    double lane_change_cost_at_lane_change =
        config_->lane_change_cost_at_lane_change();
    if (frame->target_reference_line_info() == &reference_line_info &&
        frame->current_reference_line_info() == &reference_line_info &&
        !reference_line_info.speed_pressure()) {
      // 这个时候不考虑来回变道的代价
      lane_change_cost_at_lane_change = 0;
    }
    // 上一帧在变道,这一帧的变道成本增加 防止频繁变道
    if (util::last_frame_ != nullptr &&
        util::last_frame_->turn_signal() != tpnc::TurnSignal::NO_TURN) {
      reference_line_info.AddTrajectoryCost(lane_change_cost_at_lane_change);
    }
  }
}

bool LaneFollowStage::FinishContinusLCWait() {
  static int wait_time = 0;
  if (wait_time < config_->continue_LC_wait_time() * 5) {
    wait_time++;
    TRUNK_LOG_INFO << "wait_time: " << wait_time;
    return false;
  }

  wait_time = 0;
  TRUNK_LOG_INFO << "wait time end";
  return true;
}

bool LaneFollowStage::FinishCancelLCWait() {
  static int cancel_wait_time = 0;
  if (cancel_wait_time < config_->cancel_LC_wait_time() * 5) {
    cancel_wait_time++;
    TRUNK_LOG_INFO << "cancel_wait_time: " << cancel_wait_time;
    return false;
  }

  cancel_wait_time = 0;
  TRUNK_LOG_INFO << "cancel_wait_time time end";
  return true;
}
bool LaneFollowStage::updateSelectedLine(
    const port::ReferenceLineInfo& reference_line_info, double min_cost,
    int road_priority, const std::shared_ptr<port::Frame>& frame) {
  if (!reference_line_info.is_drivable().first) {
    return false;
  }

  if (reference_line_info.total_cost() < min_cost) {
    return true;
  }

  // 成本相同时,考虑道路优先级
  if (reference_line_info.total_cost() == min_cost) {
    // 只在目标车道上考虑道路优先级
    if (frame->current_reference_line_info() &&
        frame->current_reference_line_info() ==
            frame->target_reference_line_info() &&
        reference_line_info.road_priority() > road_priority) {
      return true;
    }
  }

  return false;
}

void LaneFollowStage::Handle(Scenario* context,
                             const std::shared_ptr<port::Frame>& frame) {
  // 上一帧有轨迹&&上一帧行驶道路和此帧目标车道，一帧在主路一帧在辅路
  if (/*(frame->last_drived_reference_line_info_mapping() &&
       frame->last_drived_reference_line_info_mapping() !=
           frame->target_reference_line_info() &&
       frame->traffic_info().IsMerge()) ||*/
      (frame->last_drived_reference_line_info() &&
       frame->target_reference_line_info() &&
       frame->last_drived_reference_line_info()->target_info() &&
       frame->target_reference_line_info()->target_info() &&
       (frame->last_drived_reference_line_info()
            ->target_info()
            ->IsAuxiliaryLane() ^
        frame->target_reference_line_info()
            ->target_info()
            ->IsAuxiliaryLane()))) {
    context->SetCurrentStage("AuxiliaryLane");
    TRUNK_LOG_INFO << "*****AuxiliaryLane Stage!*****";
  }
}

// 检查是否需要取消变道
bool LaneFollowStage::ShouldCancelLaneChange(
    const std::shared_ptr<port::Frame>& frame,
    const port::ReferenceLineInfo* selected_line) const {
  return frame->last_drived_reference_line_info_mapping() != nullptr &&
         LC_status_ == RUNNING &&
         selected_line != frame->last_drived_reference_line_info_mapping();
}

// 检查是否完成变道
bool LaneFollowStage::HasFinishedLaneChange(
    const std::shared_ptr<port::Frame>& frame,
    const port::ReferenceLineInfo* selected_line) const {
  return LC_status_ == RUNNING &&
         frame->last_drived_reference_line_info_mapping() != nullptr &&
         selected_line == frame->last_drived_reference_line_info_mapping() &&
         selected_line == frame->current_reference_line_info();
}

// 检查是否可以开始变道
bool LaneFollowStage::CanStartLaneChange(
    const std::shared_ptr<port::Frame>& frame,
    const port::ReferenceLineInfo* selected_line) const {
  return LC_status_ == READY &&
         selected_line != frame->current_reference_line_info();
}

}  // namespace scenario
}  // namespace pnd
}  // namespace trunk
