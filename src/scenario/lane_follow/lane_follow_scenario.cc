// Copyright 2023, trunk Inc. All rights reserved

#include "lane_follow_scenario.h"

#include "scenario/stage.h"

namespace trunk {
namespace pnd {
namespace scenario {

REGISTER(trunk::pnd::scenario, Scenario, LaneFollow);

bool LaneFollowScenario::Init() {
  current_stage_ = tpnc::Factory<scenario::Stage>::GetInstance().GetClassByName(
      "LaneFollow");
  return true;
}

}  // namespace scenario
}  // namespace pnd
}  // namespace trunk
