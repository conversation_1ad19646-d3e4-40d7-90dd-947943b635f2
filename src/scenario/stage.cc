// Copyright 2023, trunk Inc. All rights reserved

#include "stage.h"

#include "log.h"
#include "util/speed_profile_generator.h"

namespace trunk {
namespace pnd {
namespace scenario {

tpnc::Status Stage::Process(const std::shared_ptr<port::Frame>& frame) {
  // PreProcess(frame);
  bool has_drivable_reference_line = false;
  for (auto& reference_line_info : *frame->mutable_reference_lines_info()) {
    TRUNK_LOG_INFO << ">>>>>>>>>>>>>>>>>>reference_line_info_"
                   << reference_line_info.id() << "<<<<<<<<<<<<<<<<<<";
    if (!reference_line_info.is_drivable().first) {
      TRUNK_LOG_INFO << "the line is not drivable, the reason is "
                     << reference_line_info.is_drivable().second;
      continue;
    }
    if (PlanOnReferenceLine(frame, &reference_line_info).ok()) {
      has_drivable_reference_line = true;
    } else {
      reference_line_info.set_is_drivable({false, "planning failed"});
    }
  }
  CalRefCost(frame);
  for (auto& reference_line_info : *frame->mutable_reference_lines_info()) {
    if (!reference_line_info.is_drivable().first) {
      continue;
    }
    // if (!reference_line_info.CombinePathAndSpeedProfile(
    //         frame->init_point().relative_time(), frame->init_point().s())) {
    //   reference_line_info.set_is_drivable({false, ""});
    //   TRUNK_LOG_ERROR << "Fail to aggregate planning trajectory";
    //   continue;
    // }
    auto* m_traj = reference_line_info.mutable_trajectory();
    m_traj->insert(m_traj->begin(), frame->stitch_trajectory().begin(),
                   frame->stitch_trajectory().end() - 1);
    tutil::TransformUtm::Vehicle2Utm(frame->env().vehicle(), *m_traj);
    if (!m_traj->empty()) {
      tutil::FillPathS(*m_traj);
    }
  }
  return has_drivable_reference_line ? tpnc::Status::OK()
                                     : tpnc::Status::ERROR();
}

tpnc::Status Stage::PlanOnReferenceLine(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  auto speed_profile = util::SpeedProfileGenerator::GenerateInitSpeedProfile(
      frame->init_point(), reference_line_info);
  if (speed_profile.empty()) {
    speed_profile =
        util::SpeedProfileGenerator::GenerateSpeedHotStart(frame->init_point());
    TRUNK_LOG_DEBUG << "Using dummy hot start for speed vector";
  }
  reference_line_info->set_speed_data(std::move(speed_profile));

  auto ret = tpnc::Status::OK();
  for (const auto& task : task_list_) {
    TRUNK_LOG_INFO << "---------" << task->GetName() << "---------";
    // auto time_before_execute = absl::Now();
    // ret = task->Execute(frame, reference_line_info);
    ret = task->ExecuteAndCalRunTime(frame, reference_line_info);
    if (!ret.ok()) {
      reference_line_info->set_planning_failure_cause(task->GetName() + ": " +
                                                      ret.error_message());
      TRUNK_LOG_INFO << "Failed to run tasks [" << task->GetName()
                     << "], Error message: " << ret.error_message();
      break;
    }
  }
  return ret;
}

}  // namespace scenario
}  // namespace pnd
}  // namespace trunk
