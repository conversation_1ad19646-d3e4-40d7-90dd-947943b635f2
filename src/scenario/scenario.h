// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include "port/frame.h"

namespace trunk {
namespace pnd {
namespace scenario {

class Stage;

class Scenario {
 public:
  Scenario() = default;

  virtual ~Scenario();

  virtual bool Init() = 0;

  tpnc::Status Process(const std::shared_ptr<port::Frame>& frame);

  void Request(const std::shared_ptr<port::Frame>& frame);

  void SetCurrentStage(std::string stage_name);

  void Reset();

 protected:
  std::string name_;

  std::unique_ptr<Stage> current_stage_;
};

}  // namespace scenario
}  // namespace pnd
}  // namespace trunk
