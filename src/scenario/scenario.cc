// Copyright 2023, trunk Inc. All rights reserved

#include "scenario.h"

#include "log.h"
#include "stage.h"

namespace trunk {
namespace pnd {
namespace scenario {

Scenario::~Scenario() {}

tpnc::Status Scenario::Process(const std::shared_ptr<port::Frame>& frame) {
  if (current_stage_ == nullptr) {
    return tpnc::Status(tcommon::ErrorCode::ERROR, "current stage error");
  }
  current_stage_-><PERSON>le(this, frame);
  TRUNK_LOG_INFO << "@@@@@@" << current_stage_->GetName() << "@@@@@@";
  auto ret = current_stage_->Process(frame);
  auto drive_reference_line_info = frame->FindDriveReferenceLineInfo();

  if (drive_reference_line_info != nullptr) {
    double min_obs_s = std::numeric_limits<double>::max();
    int obs_idx = -1;
    for (const auto& obs : drive_reference_line_info->obstacles().Items()) {
      if (!obs->is_virtual() &&
          (obs->lon_decision() == port::ObstacleDecision::kFollow ||
           obs->lon_decision() == port::ObstacleDecision::kStop) &&
          obs->sl_boundary().min_s() < min_obs_s) {
        min_obs_s = obs->sl_boundary().min_s();
        obs_idx = obs->obj().id();
      }
    }
    if (obs_idx != -1) {
      frame->set_nearest_obstacle_id(obs_idx);
      frame->set_nearest_obstacle_distance(
          min_obs_s - drive_reference_line_info->VehicleSlBoundary().max_s());
      frame->set_nearest_obstacle_velocity(
          drive_reference_line_info->obstacles()
              .Find(obs_idx)
              ->obj()
              .velocity());
    }
  } else if (ret.ok()) {
    return tpnc::Status::ERROR("have no drive_reference_line_info");
  }

  return ret;
}

void Scenario::SetCurrentStage(std::string stage_name) {
  auto tmp_ptr = std::move(current_stage_);
  current_stage_ =
      tpnc::Factory<scenario::Stage>::GetInstance().GetClassByName(stage_name);
}

void Scenario::Request(const std::shared_ptr<port::Frame>& frame) {
  current_stage_->Handle(this, frame);
}

void Scenario::Reset() { Init(); }

}  // namespace scenario
}  // namespace pnd
}  // namespace trunk
