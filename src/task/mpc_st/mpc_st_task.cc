// Copyright 2024, trunk Inc. All right reserved.

#include "mpc_st_task.h"

#include <absl/strings/str_format.h>
#include <absl/time/internal/cctz/include/cctz/civil_time_detail.h>

#include <Eigen/Dense>
#include <Eigen/Sparse>
#include <eigen3/unsupported/Eigen/KroneckerProduct>

#include "log.h"
#include "math/state/diff_integrator.h"
#include "task/st_graph/speed_limit_decider.h"
#include "task/st_graph/st_boundary_mapper.h"
#include "trunk/common/port/base_point.h"
#include "trunk/common/util/tools/tools.h"
#include "util/key.h"
#include "util/util.h"

namespace trunk {
namespace pnd {
namespace task {

REGISTER(trunk::pnd::task, Task, MpcSt);

MpcStTask::MpcStTask() : Task("mpc st task") {
  step_size_ = size_t(config_.total_time() / config_.dt()) + 1;
  util::uniform_slice(0.0, config_.total_time(), step_size_ - 1, t_evaluated_);
  Eigen::MatrixXd A = (Eigen::Matrix3d() << 1.0, config_.dt(), 0.0, 0.0, 1.0,
                       config_.dt(), 0.0, 0.0, 1.0)
                          .finished();
  Eigen::VectorXd B = (Eigen::Vector3d() << 0.0, 0.0, config_.dt()).finished();
  mpc_.SetModel(A, B, step_size_ - 1, step_size_ - 1);
  q_tracking_ = (Eigen::Vector3d() << config_.w_tracking()[0],
                 config_.w_tracking()[1], config_.w_tracking()[2])
                    .finished()
                    .asDiagonal();
  r_tracking_ =
      (Eigen::Matrix<double, 1, 1>() << config_.w_tracking()[3]).finished();
  qT_tracking_ = (Eigen::Vector3d() << config_.wT_tracking()[0],
                  config_.wT_tracking()[1], config_.wT_tracking()[2])
                     .finished()
                     .asDiagonal();
  k_loopshaping_ = (Eigen::Matrix<double, 1, 3>() << config_.w_loopshaping()[0],
                    config_.w_loopshaping()[1], config_.w_loopshaping()[2])
                       .finished();
  w_loopshaping_ =
      (Eigen::Matrix<double, 1, 1>() << config_.w_loopshaping()[3]).finished();
  kT_loopshaping_ =
      (Eigen::Matrix<double, 1, 3>() << config_.wT_loopshaping()[0],
       config_.wT_loopshaping()[1], config_.wT_loopshaping()[2])
          .finished();
  wT_loopshaping_ =
      (Eigen::Matrix<double, 1, 1>() << config_.wT_loopshaping()[3]).finished();
  mpc_.SetTrackingWeight(q_tracking_, r_tracking_);
  mpc_.SetTerminalTrackingWeight(qT_tracking_);
  mpc_.SetLoopShapingWeight(k_loopshaping_, w_loopshaping_);
  mpc_.SetTerminalLoopShapingWeight(kT_loopshaping_, wT_loopshaping_);
  mpc_.UpdateWeight();
  ub_ = std::make_pair(
      config_.min_jerk() * Eigen::VectorXd::Ones(step_size_ - 1, 1),
      config_.max_jerk() * Eigen::VectorXd::Ones(step_size_ - 1, 1));
  xb_ = std::make_pair(
      Eigen::kroneckerProduct(Eigen::VectorXd::Ones(step_size_),
                              Eigen::Vector3d(0.0, 0.0, config_.min_acc())),
      Eigen::kroneckerProduct(Eigen::VectorXd::Ones(step_size_),
                              Eigen::Vector3d(0.0, 0.0, config_.max_acc())));
}

void MpcStTask::CalcMinBoundary(const Eigen::Vector3d x0) {
  min_boundary_.clear();
  min_boundary_.reserve(t_evaluated_.size());
  math::DiffIntegrator<3> lon_sys(config_.dt(), x0);
  for (const double t : t_evaluated_) {
    min_boundary_.push_back(lon_sys.x());
    lon_sys.Update(config_.min_jerk());
    auto x = lon_sys.x();
    x(2) = tutil::clamp(x(2), config_.min_acc(), config_.max_acc());
    x(1) = std::max(0.0, x(1));
    x(0) = std::max(0.0, x(0));
    lon_sys.set_x(x);
  }
}

tpnc::Status MpcStTask::Execute(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  this->reference_line_info_ = reference_line_info;
  auto init_point = frame->init_point();
  init_point.set_s(reference_line_info->TransformToFrenet(init_point).s());
  Eigen::Vector3d x0(init_point.s(), init_point.v(), init_point.a());

  CalcMinBoundary(x0);

  StBoundaryMapper st_boundary_mapper(
      reference_line_info->AdcSlBoundary(), *reference_line_info,
      reference_line_info->path_data(), config_.total_length(),
      config_.total_time());
  SpeedLimitDecider speed_limit_decider(
      reference_line_info->AdcSlBoundary(),
      tpnc::Singleton<tpnc::Config>::GetInstance()->GetConfig<StGraphParam>(
          "st_graph"),
      *reference_line_info, frame->current_reference_line_info(),
      frame->tnp_speed_limit());
  port::SpeedLimit speed_limit;
  std::vector<double> s_reference;
  std::vector<double> v_reference;
  v_reference.reserve(step_size_);
  s_reference.reserve(step_size_);

  st_boundary_mapper.CreateStBoundary();
  // 得到的限速包括地图限速, 曲率jerk限速, nudge限速, 不包括停障跟车yield限速
  speed_limit_decider.GetSpeedLimits(reference_line_info->obstacles(),
                                     &speed_limit);
  if (!EstimateSpeedUpperBound(init_point, speed_limit, &v_reference)) {
    return tpnc::Status::ERROR("Fail to estimate speed upper constraints.");
  }
  for (int i = 0; i < step_size_; ++i) {
    int idx = 3 * i;
    xb_.second[idx] = 1000.0;
    xb_.first[idx] = x0(0);
    GetSConstraintByTime(t_evaluated_[i], xb_.second[idx], xb_.first[idx]);
    xb_.second[idx] = std::max(xb_.second[idx], min_boundary_[i](0) + 5.0);
    xb_.second[idx + 1] =
        std::max(v_reference[i] * config_.v_boundary_relax_ratio(),
                 min_boundary_[i](1) + tport::kMathEpsilon);
  }
  // 注意，此overtake非速度规划标签的OVERTAKE，这里专指变道超车的状态
  if (reference_line_info_->overtake_flag()) {
    for (uint32_t k = 0; k < t_evaluated_.size(); ++k) {
      v_reference[k] *= (1.0 + config_.lane_change_speed_relax_ratio());
    }
  }
  util::uniform_slice(
      std::abs(x0(1) - v_reference.front()) < 1.0 ? v_reference.front() : x0(1),
      v_reference.back(), v_reference.size() - 1, v_reference);

  s_reference.push_back(x0(0));
  for (int i = 1; i < step_size_; ++i) {
    s_reference.push_back(s_reference.back() + v_reference[i] * config_.dt());
  }

  // double tar_v = v_reference.back();
  // double tar_s = s_reference.back();
  // for (const auto& obstacle : reference_line_info_->obstacles().Items()) {
  //   const auto& boundary = obstacle->st_boundary();
  //   if (boundary.boundary_type() == port::STBoundary::BoundaryType::FOLLOW ||
  //       boundary.boundary_type() == port::STBoundary::BoundaryType::STOP ||
  //       boundary.boundary_type() == port::STBoundary::BoundaryType::YIELD) {
  //     double d_min =
  //         config_.enable_time_headway()
  //             ? config_.min_safe_distance() +
  //                   frame->tnp_time_headway() *
  //                       frame->env().vehicle().velocity()
  //             : config_.min_safe_distance() +
  //                   RSSDistance(init_point.v(), obstacle->obj().velocity());

  //    if (t_evaluated_.back() < boundary.min_t()) {
  //      continue;
  //    }
  //    if (t_evaluated_.back() > boundary.max_t()) {
  //      continue;
  //    }
  //    tar_v = std::min(tar_v, obstacle->obj().velocity());
  //    double s_upper = 0.0;
  //    double s_lower = 0.0;
  //    if (boundary.GetUnblockBoundarySRange(t_evaluated_.back(), &s_upper,
  //                                          &s_lower)) {
  //      const double s_keep = s_upper - d_min;
  //      if (s_keep < tar_s) {
  //        tar_s = std::max(x0(0), s_keep);
  //      }
  //    }
  //  }
  //}
  // tar_s = std::min(tar_v * config_.total_time() + x0(0), tar_s);
  // tar_v = std::min((tar_s - x0(0)) / config_.total_time(), tar_v);
  for (const auto& obstacle : reference_line_info_->obstacles().Items()) {
    const auto& boundary = obstacle->st_boundary();
    if (boundary.boundary_type() == port::STBoundary::BoundaryType::FOLLOW ||
        boundary.boundary_type() == port::STBoundary::BoundaryType::STOP ||
        boundary.boundary_type() == port::STBoundary::BoundaryType::YIELD) {
      const double min_obs_v =
          obstacle->obj().trajectory().empty()
              ? obstacle->obj().velocity()
              : std::min(obstacle->obj().velocity(),
                         obstacle->obj().trajectory().back().v());
      double d_min =
          config_.enable_time_headway()
              ? config_.min_safe_distance() +
                    frame->tnp_time_headway() *
                        (config_.follow_speed_weight() *
                             frame->env().vehicle().velocity() +
                         (1.0 - config_.follow_speed_weight()) *
                             std::min(min_obs_v,
                                      frame->env().vehicle().velocity()))
              : config_.min_safe_distance() +
                    RSSDistance(init_point.v(), obstacle->obj().velocity());

      for (size_t i = 0; i < step_size_; ++i) {
        if (t_evaluated_[i] < boundary.min_t()) {
          continue;
        }
        if (t_evaluated_[i] > boundary.max_t()) {
          break;
        }
        v_reference[i] = std::min(v_reference[i], obstacle->obj().velocity());
        double s_upper = 0.0;
        double s_lower = 0.0;
        if (boundary.GetUnblockBoundarySRange(t_evaluated_[i], &s_upper,
                                              &s_lower)) {
          const double s_keep = s_upper - d_min;
          if (s_keep < s_reference[i]) {
            s_reference[i] = std::max(x0(0), s_keep);
          }
        }
      }
    }
  }
  const double tar_s = s_reference.back();
  // 目前用s_reference占位dp画图，可能更具有诊断性

  const double delta_s = (tar_s - x0(0)) / double(step_size_ - 1);

  const double mean_v = (tar_s - x0(0)) / config_.total_time();
  v_reference.front() = mean_v;
  for (int i = 1; i < step_size_; ++i) {
    s_reference[i] = s_reference[i - 1] + delta_s;
    v_reference[i] = mean_v;
  }
  reference_line_info->mutable_dp_speed_data()->clear();
  for (size_t i = 0; i < step_size_; ++i) {
    reference_line_info->mutable_dp_speed_data()->emplace_back(s_reference[i],
                                                               t_evaluated_[i]);
  }

  Eigen::VectorXd reference = Eigen::VectorXd::Zero(3 * step_size_);
  for (int i = 0; i < step_size_; ++i) {
    reference(3 * i) = s_reference[i];
    reference(3 * i + 1) = v_reference[i];
  }

  int nWSR = config_.max_iter_num();
  double cputime = config_.max_cputime();
  auto result = mpc_.Solve(x0, reference, ub_, xb_, nWSR, &cputime) != 0;
  if (result) {
    TRUNK_LOG_WARN << "init state" << x0.transpose();
    for (int i = 0; i < step_size_; ++i) {
      TRUNK_LOG_WARN << absl::StrFormat(
          "t: %.3f, bound_s: (%.3f, %.3f), bound_v: (%.3f, %.3f), s: %.6f, v: "
          "%.6f",
          t_evaluated_[i], xb_.first[3 * i], xb_.second[3 * i],
          xb_.first[3 * i + 1], xb_.second[3 * i + 1], s_reference[i],
          v_reference[i]);
    }
    return tpnc::Status::ERROR("mpc st failure!");
  } else {
    TRUNK_LOG_INFO << "nWSR = " << nWSR << ", cputime = " << cputime;
  }
  auto& solution = mpc_.GetSolution();

  auto& qp_st = *reference_line_info->mutable_speed_data();
  qp_st.clear();
  qp_st.reserve(step_size_ + 1);
  qp_st.emplace_back(x0(0), 0.0, x0(1), x0(2), solution[0]);
  for (size_t i = 1; i < step_size_ - 1; i++) {
    const auto& last = qp_st.back();
    qp_st.emplace_back(last.s() + last.v() * config_.dt(), t_evaluated_[i],
                       last.v() + last.a() * config_.dt(),
                       last.a() + last.da() * config_.dt(), solution[i]);
  }
  const auto& last = qp_st.back();
  qp_st.emplace_back(last.s() + last.v() * config_.dt(),
                     t_evaluated_[step_size_ - 1],
                     last.v() + last.a() * config_.dt(),
                     last.a() + last.da() * config_.dt(), 0.0);

  if (!reference_line_info->CombinePathAndSpeedProfile(
          frame->init_point().relative_time(), frame->init_point().s())) {
    reference_line_info->set_is_drivable({false, ""});
    TRUNK_LOG_ERROR << "Fail to aggregate planning trajectory";
    return tpnc::Status::ERROR("Fail to aggregate planning trajectory");
  }
  return tpnc::Status::OK();
}

// 参考飞书RSS文档https://trunk.feishu.cn/docx/FHVhddOgpotp07xOKzlcU7PInPc
double MpcStTask::RSSDistance(const double cur_v, const double v_front) const {
  return std::max(
      0.0,
      cur_v * config_.rss_action_delay() +
          0.5 * config_.max_acc() * std::pow(config_.rss_action_delay(), 2) +
          std::pow(cur_v + config_.rss_action_delay() * config_.max_acc(), 2) /
              2.0 / std::fabs(config_.rss_ego_max_dec()) -
          v_front * v_front / 2.0 / std::fabs(config_.rss_obs_max_dec()));
}

bool MpcStTask::GetSConstraintByTime(const double time, double& s_upper_bound,
                                     double& s_lower_bound) const {
  s_upper_bound = config_.total_length();

  // for (const port::STBoundary& boundary : boundaries) {
  for (const auto& obstacle : reference_line_info_->obstacles().Items()) {
    const auto& boundary = obstacle->st_boundary();
    double s_upper = 200.0;
    double s_lower = 0.0;
    if (!boundary.GetUnblockBoundarySRange(time, &s_upper, &s_lower)) {
      continue;
    }
    if (boundary.boundary_type() == port::STBoundary::BoundaryType::STOP ||
        boundary.boundary_type() == port::STBoundary::BoundaryType::FOLLOW ||
        boundary.boundary_type() == port::STBoundary::BoundaryType::YIELD) {
      s_upper_bound = std::max(
          std::fmin(s_upper_bound, s_upper),
          reference_line_info_->sl_init_point().s() + tport::kMathEpsilon);
    } else if (boundary.boundary_type() ==
               port::STBoundary::BoundaryType::OVERTAKE) {
      s_lower_bound = std::fmax(s_lower_bound, s_lower);
    } else {
      // TRUNK_LOG_WARN << "未处理的boundary type: "
      //                 << boundary.TypeName(boundary.boundary_type());
    }
  }
  return true;
}

bool MpcStTask::EstimateSpeedUpperBound(
    const tport::TrajectoryPoint& init_point,
    const port::SpeedLimit& speed_limit,
    std::vector<double>* speed_upper_bound) const {
  if (speed_upper_bound == nullptr) {
    return false;
  }

  speed_upper_bound->clear();

  // use v to estimate position: not accurate, but feasible in cyclic
  // processing. We can do the following process multiple times and use
  // previous cycle's results for better estimation.
  port::SpeedData last_speed_data;
  const double v = init_point.v();
  const double s0 = tutil::TransformFrenet::TransformToFrenet(
                        reference_line_info_->reference_line(), init_point)
                        .s();
  if (util::last_frame_ != nullptr) {
    const port::ReferenceLineInfo* last_reference_line_info =
        util::last_frame_->drive_reference_line_info();
    if (last_reference_line_info) {
      last_speed_data = last_reference_line_info->speed_data();
    }
  }

  if (static_cast<double>(t_evaluated_.size() +
                          speed_limit.speed_limit_points().size()) <
      static_cast<double>(t_evaluated_.size()) *
          std::log(
              static_cast<double>(speed_limit.speed_limit_points().size()))) {
    uint32_t i = 0;
    uint32_t j = 0;
    while (i < t_evaluated_.size() &&
           j + 1 < speed_limit.speed_limit_points().size()) {
      // 应该是根据当前速度预估t时刻到达的s，或者根据上一帧的规划速度预估t时刻的s
      double distance = v * t_evaluated_[i] + s0;
      if (!last_speed_data.empty() && distance < last_speed_data.back().s()) {
        port::SpeedSTPoint p;
        last_speed_data.EvaluateByTime(t_evaluated_[i], &p);
        distance = p.s();
      }
      constexpr double kDistanceEpsilon = 1e-6;
      if (fabs(distance - speed_limit.speed_limit_points()[j].first) <
          kDistanceEpsilon) {
        speed_upper_bound->push_back(
            speed_limit.speed_limit_points()[j].second);
        ++i;
      } else if (distance < speed_limit.speed_limit_points()[j].first) {
        ++i;
      } else if (distance <= speed_limit.speed_limit_points()[j + 1].first) {
        speed_upper_bound->push_back(speed_limit.GetSpeedLimitByS(distance));
        ++i;
      } else {
        ++j;
      }
    }  // end of while
  }
  auto cmp = [](const std::pair<double, double>& p1, const double s) {
    return p1.first < s;
  };

  const auto& speed_limit_points = speed_limit.speed_limit_points();
  for (size_t i = speed_upper_bound->size(); i < t_evaluated_.size(); ++i) {
    const double t = t_evaluated_[i];
    double s = v * t + s0;
    if (!last_speed_data.empty() && s < last_speed_data.back().s()) {
      port::SpeedSTPoint p;
      last_speed_data.EvaluateByTime(t, &p);
      s = p.s();
    }

    // NOTICE: we are using binary search here based on two assumptions:
    // (1) The s in speed_limit_points increase monotonically.
    // (2) The evaluated_t_.size() << number of speed_limit_points.size()
    //
    // If either of the two assumption is failed, a new algorithm must be
    // used to replace the binary search.

    const auto& it = std::lower_bound(speed_limit_points.begin(),
                                      speed_limit_points.end(), s, cmp);
    if (it != speed_limit_points.end()) {
      speed_upper_bound->push_back(it->second);
    } else {
      speed_upper_bound->push_back(speed_limit_points.back().second);
    }
  }

  return true;
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
