// Copyright 2024, trunk Inc. All rights reserved.

#pragma once

#include <Eigen/Core>

#include "param/mpc_st_task_param.h"
#include "port/st/speed_limit.h"
#include "solver/mpc/mpc_solver.h"
#include "task/task.h"

namespace trunk {
namespace pnd {
namespace task {

class MpcStTask : public Task {
 public:
  MpcStTask();

  virtual ~MpcStTask() = default;

  virtual tpnc::Status Execute(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info);

 private:
  double RSSDistance(const double cur_v, const double v_front) const;

  bool GetSConstraintByTime(const double time, double& s_upper_bound,
                            double& s_lower_bound) const;

  bool EstimateSpeedUpperBound(const tport::TrajectoryPoint& init_point,
                               const port::SpeedLimit& speed_limit,
                               std::vector<double>* speed_upper_bound) const;

  void CalcQPMatrix(const double v, bool speed_tracking_flag = false);
  void CalcMinBoundary(const Eigen::Vector3d x0);

  void UpdateMpcMatrix();

 private:
  const MpcStTaskParam& config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()->GetConfig<MpcStTaskParam>(
          "mpc_st_task");

  int step_size_;
  port::ReferenceLineInfo* reference_line_info_ = nullptr;
  std::vector<double> t_evaluated_;
  solver::MpcSolver mpc_;

  Eigen::MatrixXd q_tracking_;
  Eigen::MatrixXd r_tracking_;
  Eigen::MatrixXd qT_tracking_;
  Eigen::MatrixXd k_loopshaping_;
  Eigen::MatrixXd w_loopshaping_;
  Eigen::MatrixXd kT_loopshaping_;
  Eigen::MatrixXd wT_loopshaping_;

  std::pair<Eigen::VectorXd, Eigen::VectorXd> ub_;
  std::pair<Eigen::VectorXd, Eigen::VectorXd> xb_;
  std::vector<Eigen::Vector3d> min_boundary_;
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
