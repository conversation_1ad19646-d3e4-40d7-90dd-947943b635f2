// Copyright 2023, trunk Inc. All rights reserved.

#pragma once

#include "param/qp_sl_spline_task_param.h"
#include "solver/smoothing_spline/eigen_osqp_spline_1d_solver.h"
#include "task/qp_path/sl_time_interact_boundary/sl_time_interact_boundary.h"
#include "task/task.h"

namespace trunk {
namespace pnd {
namespace task {

// 所需输入
// speedData-粗规划的轨迹点
// 输出speedData-平滑后的轨迹点
class QpSlSplineTask : public Task {
 public:
  QpSlSplineTask();
  virtual ~QpSlSplineTask() = default;

  virtual tpnc::Status Execute(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info);

 private:
  const QpSlSplineTaskParam& config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()
          ->GetConfig<QpSlSplineTaskParam>("qp_sl_spline_task");

  port::ReferenceLineInfo* reference_line_info_;

  solver::EigenOsqpSpline1dSolver sl_solver_;
  SlTimeInteractBoundary boundary_;
};
}  // namespace task
}  // namespace pnd
}  // namespace trunk
