// Copyright 2023, trunk Inc. All rights reserved.

#include "qp_sl_spline_task.h"

namespace trunk {
namespace pnd {
namespace task {

REGISTER(trunk::pnd::task, Task, QpSlSpline);

QpSlSplineTask::QpSlSplineTask()
    : Task("qp sl spline task"), sl_solver_({}, 0) {}

tpnc::Status QpSlSplineTask::Execute(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  this->reference_line_info_ = reference_line_info;
  if (!boundary_.CalBoundary(frame, reference_line_info_)) {
    TRUNK_LOG_WARN << "boundary calc failure";
    reference_line_info_->set_is_drivable({false, "boundary calc failure"});
    return tpnc::Status::ERROR("Qp sl spline solver set failure");
  }
  sl_solver_.Reset(boundary_.s_evaluated(), config_.spline_order());
  const auto& constraint = sl_solver_.mutable_spline_constraint();

  // start constrain -- equation
  if (!constraint->AddPointConstraintInRange(
          boundary_.init_sl_point().s(), boundary_.init_sl_point().l(), 0.0)) {
    return tpnc::Status::ERROR("Add start 0th-order equation error!");
  }

  // start 1th derivative constrain
  if (!constraint->AddPointDerivativeConstraintInRange(
          boundary_.init_sl_point().s(), boundary_.init_sl_point().dl(), 0.0)) {
    return tpnc::Status::ERROR("Add start 1th-order equation error!");
  }

  // start 2th derivative constrain
  if (!constraint->AddPointSecondDerivativeConstraintInRange(
          boundary_.init_sl_point().s(), boundary_.init_sl_point().ddl(),
          0.0)) {
    return tpnc::Status::ERROR("Add start 2th-order equation error!");
  }

  // boundary vector
  if (!constraint->AddBoundary(boundary_.s_evaluated(), boundary_.l().first,
                               boundary_.l().second)) {
    TRUNK_LOG_WARN << "Add 0th-order boundary error!";
    boundary_.Show();
    return tpnc::Status::ERROR("Add 0th-order boundary error!");
  }

  if (!constraint->AddDerivativeBoundary(boundary_.s_evaluated(),
                                         boundary_.dl().first,
                                         boundary_.dl().second)) {
    return tpnc::Status::ERROR("Add 1th-order boundary error!");
  }

  if (!constraint->AddSecondDerivativeBoundary(boundary_.s_evaluated(),
                                               boundary_.ddl().first,
                                               boundary_.ddl().second)) {
    return tpnc::Status::ERROR("Add 2th-order boundary error!");
  }

  if (!constraint->AddThirdDerivativeBoundary(boundary_.s_evaluated(),
                                              boundary_.dddl().first,
                                              boundary_.dddl().second)) {
    return tpnc::Status::ERROR("Add 3th-order boundary error!");
  }

  // continuity constrain
  if (boundary_.s_evaluated().size() >= 3 &&
      !constraint->AddThirdDerivativeSmoothConstraint()) {
    TRUNK_LOG_WARN << "AddThirdDerivativeSmoothConstraint error!";
    return tpnc::Status::ERROR("AddThirdDerivativeSmoothConstraint error!");
  }

  auto cost = sl_solver_.mutable_spline_kernel();
  // regular
  cost->AddRegularization(config_.weight_regular());
  // 0th cost -- bias
  if (!cost->AddReferenceLineKernelMatrix(boundary_.s_evaluated(),
                                          boundary_.reference_l(),
                                          config_.weight_norm()[0])) {
    TRUNK_LOG_WARN << "QP SL add reference line kernel failure!";
    return tpnc::Status::ERROR("QP SL add reference line kernel failure!");
  }

  // 1th cost
  cost->AddDerivativeKernelMatrix(config_.weight_norm()[1]);
  // 2th cost
  cost->AddSecondOrderDerivativeMatrix(config_.weight_norm()[2]);
  // 3th cost
  cost->AddThirdOrderDerivativeMatrix(config_.weight_norm()[3]);

  if (!sl_solver_.Solve()) {
    return tpnc::Status::ERROR("Qp sl spline sovle failure");
  }
  const auto& optimized_spline = sl_solver_.spline();
  for (const auto& s : boundary_.s_evaluated()) {
    reference_line_info_->mutable_path_data()
        ->mutable_qp_sl_path()
        ->emplace_back(s, sl_solver_.spline()(s),
                       sl_solver_.spline().Derivative(s),
                       sl_solver_.spline().SecondOrderDerivative(s));
  }
  reference_line_info_->QpSlPath2XyPath(frame->path_stitch_trajectory());
  return tpnc::Status::OK();
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
