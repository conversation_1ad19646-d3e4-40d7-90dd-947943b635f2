// Copyright 2023, trunk Inc. All right reserved.

#include "qp_sl_piecewise_task.h"

#include "log.h"
#include "math/curve/piecewise_jerk_1d.h"

namespace trunk {
namespace pnd {
namespace task {

REGISTER(trunk::pnd::task, Task, QpSlPiecewise);

QpSlPiecewiseTask::QpSlPiecewiseTask() : Task("qp sl piecewise task") {}

tpnc::Status QpSlPiecewiseTask::Execute(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  this->reference_line_info_ = reference_line_info;
  if (!boundary_.CalBoundary(frame, reference_line_info_)) {
    TRUNK_LOG_WARN << "boundary calc failure";
    reference_line_info_->set_is_drivable({false, "boundary calc failure"});
    return tpnc::Status::ERROR("boundary calc failure");
  }
  const auto& init_sl_point = boundary_.init_sl_point();
  const auto& stitch_trajectory = boundary_.stitch_trajectory();
  const auto& s_evaluated = boundary_.s_evaluated();
  const size_t optimized_size = s_evaluated.size();

  const double ds_square = boundary_.ds() * boundary_.ds();
  const double hessian_0order_base = config_.weight_norm()[0];
  const double hessian_1order_base = config_.weight_norm()[1];
  const double hessian_2order_base =
      config_.weight_norm()[2] + 2 * config_.weight_norm()[3] / ds_square;
  const double hessian_cross_coefficient =
      -config_.weight_norm()[3] / ds_square;

  // constrains
  const auto& boundary_0order = boundary_.l();
  const auto& boundary_1order = boundary_.dl();
  const auto& boundary_2order = boundary_.ddl();
  const auto& boundary_3order = boundary_.dddl();
  const auto& reference_l = boundary_.reference_l();

  // gradient
  std::vector<double> gradient_0order_base(optimized_size, 0.0);
  for (size_t i = 0; i < optimized_size; i++) {
    gradient_0order_base[i] = -config_.weight_norm()[0] * reference_l[i];
  }
  // QP 定义
  Eigen::MatrixXd hessian =
      Eigen::MatrixXd::Zero(3 * optimized_size, 3 * optimized_size);
  Eigen::VectorXd gradient = Eigen::VectorXd::Zero(3 * optimized_size);
  Eigen::MatrixXd affine =
      Eigen::MatrixXd::Zero(6 * optimized_size, 3 * optimized_size);
  Eigen::VectorXd boundary_lower = Eigen::VectorXd::Zero(6 * optimized_size);
  Eigen::VectorXd boundary_upper = Eigen::VectorXd::Zero(6 * optimized_size);

  // 赋值
  for (size_t i = 0; i < optimized_size; i++) {
    hessian(i, i) = hessian_0order_base;
    hessian(optimized_size + i, optimized_size + i) = hessian_1order_base;
    hessian(2 * optimized_size + i, 2 * optimized_size + i) =
        hessian_2order_base;
    gradient(i) = gradient_0order_base[i];
    // boundary
    affine(i, i) = 1.0;
    affine(optimized_size + i, optimized_size + i) = 1.0;
    affine(2 * optimized_size + i, 2 * optimized_size + i) = 1.0;
    // continuity constrains
    boundary_lower(i) = boundary_0order.first[i];
    boundary_upper(i) = boundary_0order.second[i];
    boundary_lower(optimized_size + i) = boundary_1order.first[i];
    boundary_upper(optimized_size + i) = boundary_1order.second[i];
    boundary_lower(2 * optimized_size + i) = boundary_2order.first[i];
    boundary_upper(2 * optimized_size + i) = boundary_2order.second[i];
  }
  // modified
  // 0order
  hessian(optimized_size - 1, optimized_size - 1) += config_.weight_end()[0];
  // 1order
  hessian(2 * optimized_size - 1, 2 * optimized_size - 1) +=
      config_.weight_end()[1];
  // 2order
  hessian(2 * optimized_size, 2 * optimized_size) -=
      config_.weight_norm()[3] / ds_square;
  hessian(3 * optimized_size - 1, 3 * optimized_size - 1) +=
      config_.weight_end()[2] - config_.weight_norm()[3] / ds_square;

  // gradient(optimized_size - 1) -=
  //     config_.weight_reference_end() * dp_sl_path.back().l();
  // hessian : cross term
  for (size_t i = 2 * optimized_size + 1; i < 3 * optimized_size; i++) {
    hessian(i, i - 1) = hessian_cross_coefficient;
    hessian(i - 1, i) = hessian_cross_coefficient;
  }
  // affine : init state
  affine(3 * optimized_size, 0) = 1.0;
  boundary_lower(3 * optimized_size) = init_sl_point.l();
  boundary_upper(3 * optimized_size) = init_sl_point.l();
  affine(4 * optimized_size, optimized_size) = 1.0;
  boundary_lower(4 * optimized_size) = init_sl_point.dl();
  boundary_upper(4 * optimized_size) = init_sl_point.dl();
  affine(5 * optimized_size, 2 * optimized_size) = 1.0;
  boundary_lower(5 * optimized_size) = init_sl_point.ddl();
  boundary_upper(5 * optimized_size) = init_sl_point.ddl();
  if (frame->replan_flag()) {
    boundary_lower(4 * optimized_size) = -DBL_MAX;
    boundary_upper(4 * optimized_size) = DBL_MAX;
    boundary_lower(5 * optimized_size) = -DBL_MAX;
    boundary_upper(5 * optimized_size) = DBL_MAX;
  }
  // affine :  continuity constrains
  for (int i = 1; i < optimized_size; i++) {
    // acceleration : acc(i) = acc(i-1) + ∫jerk ds = acc(i-1) + jerk * ds
    affine(5 * optimized_size + i, 2 * optimized_size + i) = -1.0;
    affine(5 * optimized_size + i, 2 * optimized_size + i - 1) = 1.0;
    boundary_lower(5 * optimized_size + i) =
        boundary_3order.first[i] * boundary_.ds();
    boundary_upper(5 * optimized_size + i) =
        boundary_3order.second[i] * boundary_.ds();
    // velocity : velo(i) = velo(i-1) + ∫acc ds
    // velocity : = velo(i-1) + acc(i-1) * ds + jerk * ds^2 / 2
    // velocity : = velo(i-1) + ds * (2 acc(i-1) + jerk / ds) / 2
    // velocity : = velo(i-1) + ds * (acc(i-1) + acc(i)) / 2
    affine(4 * optimized_size + i, 2 * optimized_size + i) =
        0.5 * boundary_.ds();
    affine(4 * optimized_size + i, 2 * optimized_size + i - 1) =
        0.5 * boundary_.ds();
    affine(4 * optimized_size + i, optimized_size + i) = -1.0;
    affine(4 * optimized_size + i, optimized_size + i - 1) = 1.0;
    boundary_lower(4 * optimized_size + i) = -0.0;
    boundary_upper(4 * optimized_size + i) = 0.0;
    // position : posi(i) = posi(i-1) + ∫velo ds
    // position : = posi(i-1) + velo(i-1) * ds + acc(i-1) * ds^2 / 2 + jerk *
    // ds^3 / 6
    // position : = posi(i-1) + velo(i-1) * ds + (3 * acc(i-1) + jerk *
    //  ds) * ds^2 / 6
    // position : = posi(i-1) + velo(i-1) * ds + (2 * acc(i-1) +
    //   acc(i)) * ds^2 / 6
    affine(3 * optimized_size + i, 2 * optimized_size + i) = ds_square / 6.0;
    affine(3 * optimized_size + i, 2 * optimized_size + i - 1) =
        ds_square / 3.0;
    affine(3 * optimized_size + i, optimized_size + i - 1) = boundary_.ds();
    affine(3 * optimized_size + i, i) = -1.0;
    affine(3 * optimized_size + i, i - 1) = 1.0;
    boundary_lower(3 * optimized_size + i) = -0.0;
    boundary_upper(3 * optimized_size + i) = 0.0;
  }

  Eigen::SparseMatrix<double> sparse_hessian = hessian.sparseView();
  Eigen::SparseMatrix<double> sparse_affine = affine.sparseView();
  // OSQP
  OsqpEigen::Solver piecewise_jerk_path;
  piecewise_jerk_path.settings()->setVerbosity(false);
  piecewise_jerk_path.settings()->setWarmStart(true);
  // piecewise_jerk_path.settings()->setAlpha(0.88);
  piecewise_jerk_path.settings()->setAbsoluteTolerance(config_.abs_tol());
  piecewise_jerk_path.settings()->setRelativeTolerance(config_.rel_tol());
  piecewise_jerk_path.settings()->setPrimalInfeasibilityTollerance(
      config_.prim_infe_tol());
  piecewise_jerk_path.settings()->setDualInfeasibilityTollerance(
      config_.dual_infe_tol());
  // set the initial data of the QP piecewise_jerk_path
  piecewise_jerk_path.data()->setNumberOfVariables(3 * optimized_size);
  piecewise_jerk_path.data()->setNumberOfConstraints(6 * optimized_size);
  if (!piecewise_jerk_path.data()->setHessianMatrix(sparse_hessian))
    return tpnc::Status::ERROR("path QP error: set hessian failure");
  if (!piecewise_jerk_path.data()->setGradient(gradient))
    return tpnc::Status::ERROR("path QP error: set gradient failure");
  if (!piecewise_jerk_path.data()->setLinearConstraintsMatrix(sparse_affine))
    return tpnc::Status::ERROR("path QP error: set affine failure");
  if (!piecewise_jerk_path.data()->setLowerBound(boundary_lower))
    return tpnc::Status::ERROR("path QP error: set lower_boundary failure");
  if (!piecewise_jerk_path.data()->setUpperBound(boundary_upper))
    return tpnc::Status::ERROR("path QP error: set upper_boundary failure");
  // instantiate the piecewise_jerk_path
  if (!piecewise_jerk_path.initSolver())
    return tpnc::Status::ERROR("path QP error: solver init failure");

  // Solve.
  if (!piecewise_jerk_path.solve())
    return tpnc::Status::ERROR("path QP error: solver failure");

  Eigen::VectorXd qp_solution = piecewise_jerk_path.getSolution();

  math::PiecewiseJerk1d sl_path(qp_solution(0), qp_solution(optimized_size),
                                qp_solution(2 * optimized_size));
  for (size_t i = 2 * optimized_size + 1; i < 3 * optimized_size; i++) {
    const double jerk = (qp_solution(i) - qp_solution(i - 1)) / boundary_.ds();
    sl_path.AppendSegment(jerk, boundary_.ds());
  }
  for (double s = 0; s < sl_path.ParamLength(); s += boundary_.ds()) {
    reference_line_info_->mutable_path_data()
        ->mutable_qp_sl_path()
        ->emplace_back(init_sl_point.s() + s, sl_path.Evaluate(0, s),
                       sl_path.Evaluate(1, s), sl_path.Evaluate(2, s));
  }
  reference_line_info_->QpSlPath2XyPath(frame->path_stitch_trajectory());

  return tpnc::Status::OK();
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
