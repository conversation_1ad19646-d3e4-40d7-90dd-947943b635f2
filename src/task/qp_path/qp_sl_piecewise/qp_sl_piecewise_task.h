// Copyright 2023, trunk Inc. All rights reserved.

#pragma once

#include <OsqpEigen/OsqpEigen.h>

#include <Eigen/Dense>

#include "param/qp_sl_piecewise_task_param.h"
#include "task/qp_path/sl_time_interact_boundary/sl_time_interact_boundary.h"
#include "task/task.h"

namespace trunk {
namespace pnd {
namespace task {

// 所需输入
// PathData-粗规划的轨迹点
// 输出PathData-平滑后的轨迹点
class QpSlPiecewiseTask : public Task {
 public:
  QpSlPiecewiseTask();
  virtual ~QpSlPiecewiseTask() = default;

  virtual tpnc::Status Execute(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info);

 private:
  const QpSlPiecewiseTaskParam& config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()
          ->GetConfig<QpSlPiecewiseTaskParam>("qp_sl_piecewise_task");
  port::ReferenceLineInfo* reference_line_info_ = nullptr;
  SlTimeInteractBoundary boundary_;
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
