// Copyright 2024, trunk Inc. All rights reserved.

#pragma once

#include <Eigen/Dense>
#include <Eigen/Sparse>

#include "math/state/discrete_system.h"
#include "param/mpc_sl_task_param.h"
#include "solver/mpc/mpc_solver.h"
#include "task/qp_path/sl_time_interact_boundary/sl_time_interact_boundary.h"
#include "task/task.h"

namespace trunk {
namespace pnd {
namespace task {

class MpcSlTask : public Task {
 public:
  MpcSlTask();
  virtual ~MpcSlTask() = default;

  virtual tpnc::Status Execute(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info);

 private:
 private:
  const MpcSlTaskParam& config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()->GetConfig<MpcSlTaskParam>(
          "mpc_sl_task");

  port::ReferenceLineInfo* reference_line_info_ = nullptr;

  SlTimeInteractBoundary boundary_;
  size_t step_size_ = 0;

  solver::MpcSolver mpc_;
  std::unique_ptr<math::DiscreteSystem<3>> lat_sys_;
  Eigen::MatrixXd q_tracking_;
  Eigen::MatrixXd r_tracking_;
  Eigen::MatrixXd qT_tracking_;
  Eigen::MatrixXd k_loopshaping_;
  Eigen::MatrixXd w_loopshaping_;
  Eigen::MatrixXd kT_loopshaping_;
  Eigen::MatrixXd wT_loopshaping_;

  std::pair<Eigen::VectorXd, Eigen::VectorXd> ub_;
  std::pair<Eigen::VectorXd, Eigen::VectorXd> xb_;
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
