// Copyright 2024, trunk Inc. All right reserved.

#include "mpc_sl_task.h"

#include <Eigen/src/Core/Matrix.h>
#include <absl/strings/str_format.h>

#include <eigen3/unsupported/Eigen/KroneckerProduct>
#include <string>

#include "log.h"
#include "math/state/diff_integrator.h"
#include "math/state/zoh_integrator.h"
namespace {
const double kInf = 1.0e20;
}
namespace trunk {
namespace pnd {
namespace task {

REGISTER(trunk::pnd::task, Task, MpcSl);

MpcSlTask::MpcSlTask()
    : Task("mpc sl task"),
      lat_sys_(std::make_unique<math::ZohIntegrator<3>>(boundary_.ds())) {
  step_size_ = config_.max_total_s() / boundary_.ds() + 1;
  q_tracking_ = (Eigen::Vector3d() << config_.w_tracking()[0],
                 config_.w_tracking()[1], config_.w_tracking()[2])
                    .finished()
                    .asDiagonal();
  r_tracking_ =
      (Eigen::Matrix<double, 1, 1>() << config_.w_tracking()[3]).finished();
  qT_tracking_ = (Eigen::Vector3d() << config_.wT_tracking()[0],
                  config_.wT_tracking()[1], config_.wT_tracking()[2])
                     .finished()
                     .asDiagonal();
  k_loopshaping_ = (Eigen::Matrix<double, 1, 3>() << config_.w_loopshaping()[0],
                    config_.w_loopshaping()[1], config_.w_loopshaping()[2])
                       .finished();
  w_loopshaping_ =
      (Eigen::Matrix<double, 1, 1>() << config_.w_loopshaping()[3]).finished();
  kT_loopshaping_ =
      (Eigen::Matrix<double, 1, 3>() << config_.wT_loopshaping()[0],
       config_.wT_loopshaping()[1], config_.wT_loopshaping()[2])
          .finished();
  wT_loopshaping_ =
      (Eigen::Matrix<double, 1, 1>() << config_.wT_loopshaping()[3]).finished();
  mpc_.SetModel(lat_sys_->A(), lat_sys_->B(), step_size_ - 1, step_size_ - 1,
                config_.using_adaptive_initial_state(),
                (config_.using_soft_boundary()
                     ? Eigen::MatrixXd(Eigen::Vector3d(1.0, 0.0, 0.0))
                     : Eigen::MatrixXd()));
  mpc_.SetSoftConstraintsWeight(
      Eigen::Matrix<double, 1, 1>(config_.weight_soft_boundary()));
  mpc_.SetTrackingWeight(q_tracking_, r_tracking_);
  mpc_.SetTerminalTrackingWeight(qT_tracking_);
  mpc_.SetLoopShapingWeight(k_loopshaping_, w_loopshaping_);
  mpc_.SetTerminalLoopShapingWeight(kT_loopshaping_, wT_loopshaping_);
  mpc_.SetAdaptiveInitialState(
      (Eigen::Vector3d() << config_.w_adaptive_initial_state()[0],
       config_.w_adaptive_initial_state()[1],
       config_.w_adaptive_initial_state()[2])
          .finished()
          .asDiagonal());
  mpc_.UpdateWeight();

  ub_ = std::make_pair(
      -config_.max_l_soft_boundary() *
          Eigen::VectorXd::Ones(mpc_.Nu() + mpc_.Ns() + mpc_.nais()),
      config_.max_l_soft_boundary() *
          Eigen::VectorXd::Ones(mpc_.Nu() + mpc_.Ns() + mpc_.nais()));
  ub_.first.block(0, 0, mpc_.Nu(), 1) =
      -kInf * Eigen::VectorXd::Ones(mpc_.Nu());
  ub_.second.block(0, 0, mpc_.Nu(), 1) =
      kInf * Eigen::VectorXd::Ones(step_size_ - 1);
  xb_ = std::make_pair(-kInf * Eigen::VectorXd::Ones(mpc_.Nx()),
                       kInf * Eigen::VectorXd::Ones(mpc_.Nx()));
}

tpnc::Status MpcSlTask::Execute(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  this->reference_line_info_ = reference_line_info;
  if (!boundary_.CalBoundary(frame, reference_line_info_)) {
    TRUNK_LOG_WARN << "boundary calc failure";
    reference_line_info_->set_is_drivable({false, "boundary calc failure"});
    return tpnc::Status::ERROR("boundary calc failure");
  }
  ub_.first.block(0, 0, mpc_.Nu(), 1) =
      -kInf * Eigen::VectorXd::Ones(step_size_ - 1);
  ub_.second.block(0, 0, mpc_.Nu(), 1) =
      kInf * Eigen::VectorXd::Ones(step_size_ - 1);
  xb_ = std::make_pair(-kInf * Eigen::VectorXd::Ones(mpc_.Nx()),
                       kInf * Eigen::VectorXd::Ones(mpc_.Nx()));
  const auto& init_sl_point = boundary_.init_sl_point();
  // const auto& stitch_trajectory = boundary_.stitch_trajectory();
  const auto& s_evaluated = boundary_.s_evaluated();
  const size_t pred_step_size = std::min(s_evaluated.size(), step_size_);
  Eigen::Vector3d x0 = Eigen::Vector3d(init_sl_point.l(), init_sl_point.dl(),
                                       init_sl_point.ddl());

  const auto& l_boundary = boundary_.l();
  const auto& dl_boundary = boundary_.dl();
  const auto& ddl_boundary = boundary_.ddl();
  const auto& dddl_boundary = boundary_.dddl();
  const auto& reference_l = boundary_.reference_l();
  const auto& reference_dl = boundary_.reference_dl();
  const auto& reference_ddl = boundary_.reference_ddl();

  Eigen::VectorXd reference = Eigen::VectorXd::Zero(3 * step_size_);
  for (int i = 0; i < pred_step_size; ++i) {
    xb_.first(3 * i) = l_boundary.first[i];
    xb_.second(3 * i) = l_boundary.second[i];
    xb_.first(3 * i + 1) = dl_boundary.first[i];
    xb_.second(3 * i + 1) = dl_boundary.second[i];
    xb_.first(3 * i + 2) = ddl_boundary.first[i];
    xb_.second(3 * i + 2) = ddl_boundary.second[i];
    ub_.first(i) = dddl_boundary.first[i];
    ub_.second(i) = dddl_boundary.second[i];
    reference(3 * i) = reference_l[i];
    reference(3 * i + 1) = reference_dl[i];
    reference(3 * i + 2) = reference_ddl[i];
  }
  if (config_.using_adaptive_initial_state()) {
    ub_.first[mpc_.Nu()] = boundary_.adaptive().first[0];
    ub_.second[mpc_.Nu()] = boundary_.adaptive().second[0];
    ub_.first[mpc_.Nu() + 1] = boundary_.adaptive().first[1];
    ub_.second[mpc_.Nu() + 1] = boundary_.adaptive().second[1];
    ub_.first[mpc_.Nu() + 2] = boundary_.adaptive().first[2];
    ub_.second[mpc_.Nu() + 2] = boundary_.adaptive().second[2];
  }
  int nWSR = config_.max_iter_num();
  double cputime = config_.max_cputime();
  auto result = mpc_.Solve(x0, reference, ub_, xb_, nWSR, &cputime);
  if (result) {
    TRUNK_LOG_WARN << absl::StrFormat(
        "init-- s: %.2f, l: %.5f, dl: %.5f, ddl: %.20f", init_sl_point.s(),
        init_sl_point.l(), init_sl_point.dl(), init_sl_point.ddl());
    TRUNK_LOG_WARN << absl::StrFormat(
        "init_soft_relaxation l: (%.5f, %.5f), dl: (%.5f, %.5f), ddl: (%.20f, "
        "%.20f)",
        ub_.first[mpc_.Nu()], ub_.second[mpc_.Nu()], ub_.first[mpc_.Nu() + 1],
        ub_.second[mpc_.Nu() + 1], ub_.first[mpc_.Nu() + 2],
        ub_.second[mpc_.Nu() + 2]);
    for (int i = 0; i < s_evaluated.size(); ++i) {
      TRUNK_LOG_WARN << absl::StrFormat(
          "s: %.2f, l: (%.5f, %.5f), dl: (%.5f, %.5f), ddl: (%.20f, %.20f), "
          "dddl: (%.20f, %.20f)",
          s_evaluated[i], l_boundary.first[i], l_boundary.second[i],
          dl_boundary.first[i], dl_boundary.second[i], ddl_boundary.first[i],
          ddl_boundary.second[i], dddl_boundary.first[i],
          dddl_boundary.second[i]);
    }
    return tpnc::Status::ERROR("mpc sl error: qpOASES solve failure! reason: " +
                               std::to_string(result));
  } else if (config_.using_adaptive_initial_state()) {
    TRUNK_LOG_INFO << "init_relaxation: " << mpc_.x0_relaxation().transpose();
  }
  TRUNK_LOG_INFO << "nWSR = " << nWSR << ", cputime = " << cputime;

  auto& solution = mpc_.GetSolution();

  auto& qp_sl = *reference_line_info->mutable_path_data()->mutable_qp_sl_path();
  qp_sl.clear();
  qp_sl.reserve(pred_step_size + 1);
  lat_sys_->SetInitState(x0 + mpc_.x0_relaxation(), init_sl_point.s());
  qp_sl.emplace_back(lat_sys_->t(), lat_sys_->x()[0], lat_sys_->x()[1],
                     lat_sys_->x()[2]);
  for (int i = 0; i < pred_step_size; ++i) {
    lat_sys_->Update(solution[i]);
    qp_sl.emplace_back(lat_sys_->t(), lat_sys_->x()[0], lat_sys_->x()[1],
                       lat_sys_->x()[2]);
  }
  reference_line_info_->QpSlPath2XyPath(boundary_.stitch_trajectory());

  return tpnc::Status::OK();
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
