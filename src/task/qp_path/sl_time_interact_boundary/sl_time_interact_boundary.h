// Copyright 2023, trunk Inc. All rights reserved.

#pragma once

#include <trunk/common/common.h>

#include "param/sl_time_interact_boundary_param.h"
#include "port/frame.h"

namespace trunk {
namespace pnd {
namespace task {

// 所需输入
// speedData-粗规划的轨迹点
// 输出speedData-平滑后的轨迹点
class SlTimeInteractBoundary {
 public:
  SlTimeInteractBoundary();
  ~SlTimeInteractBoundary() = default;
  bool CalBoundary(const std::shared_ptr<port::Frame>& frame,
                   port::ReferenceLineInfo* reference_line_info);
  void Show() const;

 private:
  void CalLaneBoundaryBasedOnPathSampleS(
      const std::shared_ptr<port::Frame>& frame);
  void CalObstacleBoundary();
  void CalNudgeRef();
  void SetNudgeRefBasedOnS(const double start_s, const double end_s,
                           const port::Obstacle* obstacle, const double ref_l);
  void CalCartesianCoordinate();

 private:
  // first右边界, second左边界
  using SlBoundarys = std::pair<std::vector<double>, std::vector<double>>;

  const SlTimeInteractBoundaryParam& config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()
          ->GetConfig<SlTimeInteractBoundaryParam>("sl_time_interact_boundary");
  port::ReferenceLineInfo* reference_line_info_;
  std::vector<port::SpeedSTPoint> ego_sample_s_;

  MEMBER_BASIC_TYPE(double, ds, 0.0);
  MEMBER_BASIC_TYPE(double, vehicle_speed, 0.0);
  MEMBER_BASIC_TYPE(bool, lane_change_flag, false);
  MEMBER_BASIC_TYPE(port::PathSLPoint, init_sl_point, {});
  MEMBER_BASIC_TYPE(tport::TrajectoryPoint, init_point, {});
  MEMBER_COMPLEX_TYPE(tport::Trajectory, stitch_trajectory);
  MEMBER_COMPLEX_TYPE(std::vector<double>, s_evaluated);
  MEMBER_COMPLEX_TYPE(std::vector<double>, reference_l);
  MEMBER_COMPLEX_TYPE(std::vector<double>, reference_dl);
  MEMBER_COMPLEX_TYPE(std::vector<double>, reference_ddl);
  MEMBER_COMPLEX_TYPE(SlBoundarys, nudge_reference);
  MEMBER_COMPLEX_TYPE(SlBoundarys, nudge_boundary);
  MEMBER_COMPLEX_TYPE(SlBoundarys, l);
  MEMBER_COMPLEX_TYPE(SlBoundarys, dl);
  MEMBER_COMPLEX_TYPE(SlBoundarys, ddl);
  MEMBER_COMPLEX_TYPE(SlBoundarys, dddl);
  MEMBER_COMPLEX_TYPE(SlBoundarys, adaptive);
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
