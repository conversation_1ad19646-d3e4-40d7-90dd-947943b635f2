// Copyright 2023, trunk Inc. All rights reserved.

#include "sl_time_interact_boundary.h"

#include <absl/strings/str_format.h>

#include "log.h"
#include "math/curve/quartic_polynomial.h"
#include "math/curve/quintic_polynomial.h"
#include "port/frame.h"
#include "port/obstacle.h"
#include "port/sl/path_point.h"
#include "trunk/common/port/base_point.h"
#include "trunk/common/util/tools/tools.h"
#include "util/key.h"
#include "util/util.h"

namespace trunk {
namespace pnd {
namespace task {

SlTimeInteractBoundary::SlTimeInteractBoundary() : ds_(config_.ds()) {}

bool SlTimeInteractBoundary::CalBoundary(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* reference_line_info) {
  reference_line_info_ = reference_line_info;
  vehicle_speed_ = frame->env().vehicle().velocity();

  if (frame->last_drived_reference_line_info_mapping() !=
      reference_line_info_) {
  }
  // 不是取消变道
  if (config_.preview_only_lane_change() && !frame->cancel_lane_change_flag() &&
      // 开始变道或上一帧在变道
      (reference_line_info_->IsLaneChangeLane() ||
       (util::last_frame_ != nullptr &&
        util::last_frame_->turn_flag() != tpnc::TurnSignal::NO_TURN)) &&
      // 非取消变道
      !(util::last_frame_ != nullptr &&
        util::last_frame_->turn_flag() != tpnc::TurnSignal::NO_TURN &&
        frame->last_drived_reference_line_info_mapping() !=
            reference_line_info_)) {
    init_point_ = frame->path_init_point();
    stitch_trajectory_ = frame->path_stitch_trajectory();
    lane_change_flag_ = true;
  } else {
    init_point_ = frame->init_point();
    stitch_trajectory_ = {frame->stitch_trajectory().back()};
    lane_change_flag_ = false;
  }
  init_point_.set_v(frame->env().vehicle().velocity());
  init_sl_point_ = reference_line_info_->CalculateSlPoint(init_point_);
  if (frame->replan_flag()) {
    init_sl_point_.set_dl(0.0);
    init_sl_point_.set_ddl(0.0);
    reference_line_info->set_to_auto_drive(true);
  }
  const auto& reference_line = reference_line_info->reference_line();
  double end_s = std::min(
      config_.max_length(),
      std::min(reference_line.back().s(), reference_line_info->qp_sl_end_s()));
  if (end_s - init_sl_point_.s() < 5.0) {
    TRUNK_LOG_WARN << "reference_line is too short." << reference_line.size()
                   << ", "
                   << tutil::TwoPointsDistance(reference_line.back(),
                                               init_point_);
    return false;
  }
  if (std::isnan(init_sl_point_.s())) {
    TRUNK_LOG_ERROR << "init_sl_point is NaN, path_init_point = "
                    << init_point_;
    return false;
  }
  size_t pred_step_size = (end_s - init_sl_point_.s()) / config_.ds();
  end_s = init_sl_point_.s() + double(pred_step_size) * config_.ds();
  if (pred_step_size < 2) {
    TRUNK_LOG_WARN << "the length is too short";
    return false;
  }

  util::uniform_slice(init_sl_point_.s(), end_s, pred_step_size, s_evaluated_);

  reference_l_.clear();
  reference_dl_.clear();
  reference_ddl_.clear();
  l_.first.clear();
  l_.second.clear();
  nudge_boundary_.first.clear();
  nudge_boundary_.second.clear();
  dl_.first.clear();
  dl_.second.clear();
  ddl_.first.clear();
  ddl_.second.clear();
  dddl_.first.clear();
  dddl_.second.clear();
  nudge_reference_.first.clear();
  nudge_reference_.second.clear();
  nudge_reference_.first.resize(s_evaluated_.size(), 0.0);
  nudge_reference_.second.resize(s_evaluated_.size(), 0.0);
  l_.first.resize(s_evaluated_.size(), -100.0);
  l_.second.resize(s_evaluated_.size(), 100.0);
  nudge_boundary_.first.resize(s_evaluated_.size(), -100.0);
  nudge_boundary_.second.resize(s_evaluated_.size(), 100.0);
  dl_.first.resize(s_evaluated_.size(), -config_.dl_boundary());
  dl_.second.resize(s_evaluated_.size(), config_.dl_boundary());
  ddl_.first.resize(s_evaluated_.size());
  ddl_.second.resize(s_evaluated_.size());
  dddl_.first.resize(s_evaluated_.size());
  dddl_.second.resize(s_evaluated_.size());
  reference_l_.resize(s_evaluated_.size(), reference_line_info->ref_l());
  reference_dl_.resize(s_evaluated_.size(), 0.0);
  reference_ddl_.resize(s_evaluated_.size(), 0.0);

  // const double dkappa_max =
  //     tutil::LookupTableLinear(init_point_.v(), config_.speed_table(),
  //                              config_.max_steering_angular_velocity_table())
  //                              /
  //     util::model_.geometry().wheelbase() / std::max(init_point_.v(), 1.0);
  auto cur_point = reference_line_info_->EvaluateByS(s_evaluated_.front());
  double last_kappa = 0.0;
  double last_dkappa = 0.0;
  const double v_max = std::max(1.0, init_point_.v());
  const double kappa_max = std::max(
      1.1 * std::abs(init_sl_point_.ddl()),
      config_.ratio_max_curvature() *
          tutil::LookupTableLinear(
              v_max, config_.speed_table(),
              lane_change_flag_ ? config_.lane_change_max_lateral_acc_table()
                                : config_.max_lateral_acc_table()) /
          v_max / v_max);
  const double dkappa_max =
      tutil::LookupTableLinear(v_max, config_.speed_table(),
                               config_.max_steering_angular_velocity_table()) /
      util::model_.geometry().wheelbase() / v_max;

  for (size_t i = 0; i < s_evaluated_.size(); i++) {
    const auto next_point = reference_line_info_->EvaluateByS(
        s_evaluated_[std::min(i + 1, pred_step_size - 1)]);
    const double cur_dkappa =
        config_.using_relative_steering_angular_velocity()
            ? 0.0
            : (next_point.kappa() - cur_point.kappa()) / config_.ds();
    ddl_.first[i] =
        -kappa_max -
        (config_.using_relative_curvature() ? 0.0 : cur_point.kappa());
    ddl_.second[i] =
        kappa_max -
        (config_.using_relative_curvature() ? 0.0 : cur_point.kappa());
    dddl_.first[i] = -dkappa_max - cur_dkappa;
    dddl_.second[i] = dkappa_max - cur_dkappa;
    cur_point = next_point;
    // if (config_.using_relative_curvature() && i != 0) {
    //   if (kappa_max < ddl_.second[i - 1] - last_dkappa * config_.ds()) {
    //     ddl_.first[i] = std::min(
    //         ddl_.first[i], ddl_.first[i - 1] + config_.ds() * last_dkappa);
    //     ddl_.second[i] = std::max(
    //         ddl_.second[i], ddl_.second[i - 1] - config_.ds() * last_dkappa);
    //   }
    // }
    // last_dkappa = dkappa_max;
  }

  adaptive_.first.clear();
  adaptive_.second.clear();
  if (reference_line_info_->using_sl_adaptive() ||
      frame->last_drived_reference_line_info_mapping() &&
          reference_line_info_->target_info() &&
          (frame->last_drived_reference_line_info_mapping()
               ->infos()
               .front()
               .IsAuxiliaryLane() ^
           reference_line_info_->target_info()->IsAuxiliaryLane())) {
    adaptive_.first.push_back(
        tutil::clamp(l_.first.front() - init_sl_point_.l(), -0.1, 0.1));
    adaptive_.second.push_back(
        tutil::clamp(l_.second.front() - init_sl_point_.l(), -0.1, 0.1));
    adaptive_.first.push_back(dl_.first.front() - init_sl_point_.dl());
    adaptive_.second.push_back(dl_.second.front() - init_sl_point_.dl());
    // adaptive_.first.push_back(ddl_.first.front() - init_sl_point_.ddl());
    // adaptive_.second.push_back(ddl_.second.front() - init_sl_point_.ddl());
    adaptive_.first.push_back(0.000001);
    adaptive_.second.push_back(0.000001);
    reference_line_info_->set_using_sl_adaptive(true);
  } else {
    adaptive_.first.push_back(-0.01);
    adaptive_.second.push_back(0.01);
    adaptive_.first.push_back(-0.001);
    adaptive_.second.push_back(0.001);
    adaptive_.first.push_back(-0.00001);
    adaptive_.second.push_back(0.00001);
    reference_line_info_->set_using_sl_adaptive(false);
  }
  if (!frame->replan_flag() && reference_line_info->to_auto_drive()) {
    const auto bound =
        reference_line_info_->GetEgoDrivableWidth(init_sl_point_.s());
    if (bound.left_width > init_sl_point_.l() &&
        bound.right_width < init_sl_point_.l()) {
      TRUNK_LOG_INFO << "init_boundary: " << bound.right_width << ", "
                     << bound.left_width << ", init_l: " << init_sl_point_.l();
      reference_line_info->set_to_auto_drive(false);
    }
  }
  if (reference_line_info->to_auto_drive()) {
    TRUNK_LOG_INFO << "to auto drive";
    return true;
  }
  CalLaneBoundaryBasedOnPathSampleS(frame);
  CalNudgeRef();
  /* CheckBoundary(); */
  CalCartesianCoordinate();
  const double nudge_display_dis =
      frame->env().vehicle().velocity() * config_.nudge_disp_time_headway();
  for (int i = 0;
       i < s_evaluated_.size() && s_evaluated_[i] < nudge_display_dis; ++i) {
    if (reference_l_[i] > config_.nudge_disp_lat_dis()) {
      reference_line_info->set_nudge_direction(1);
      break;
    } else if ((reference_l_[i] < -config_.nudge_disp_lat_dis())) {
      reference_line_info->set_nudge_direction(2);
      break;
    }
  }
  if (!lane_change_flag_) {
    return true;
  }
  const double lane_change_dis = std::abs(
      init_sl_point_.l() / util::kHighwayLaneWidth *
      std::max(util::truck_.length(),
               config_.reference_lane_change_time() * init_point_.v()));
  const double lane_change_start =
      init_point_.v() * config_.reference_lane_change_prepare_time();
  const double k = init_sl_point_.l() / lane_change_dis;
  for (int i = 0; i < s_evaluated_.size(); ++i) {
    if (s_evaluated_[i] >
        s_evaluated_.front() + lane_change_dis + lane_change_start) {
      break;
    }
    if (s_evaluated_[i] <= s_evaluated_.front() + lane_change_start) {
      reference_l_[i] += init_sl_point_.l();
    } else {
      reference_l_[i] +=
          init_sl_point_.l() -
          k * (s_evaluated_[i] - s_evaluated_.front() - lane_change_start);
    }
  }
  return true;
}

void SlTimeInteractBoundary::CalLaneBoundaryBasedOnPathSampleS(
    const std::shared_ptr<port::Frame>& frame) {
  for (size_t i = 0; i < s_evaluated_.size(); i++) {
    const auto lane_width =
        reference_line_info_->GetEgoDrivableWidth(s_evaluated_.at(i));
    if (lane_width.left_width - lane_width.right_width <= 0.0) {
      TRUNK_LOG_WARN << "lane_width is too narrow to pass thought at s = "
                     << s_evaluated_.at(i) << ", width = "
                     << lane_width.left_width - lane_width.right_width << " = "
                     << lane_width.left_width << " - "
                     << lane_width.right_width;
      continue;
    }
    l_.first[i] = lane_width.right_width;
    l_.second[i] = lane_width.left_width;
    nudge_boundary_.first[i] = std::min(-0.0, lane_width.right_width);
    nudge_boundary_.second[i] = std::max(0.0, lane_width.left_width);
  }

  if (frame->current_reference_line_info() &&
      frame->current_reference_line_info()->infos().front().IsAuxiliaryLane()) {
    return;
  }
  for (int i = s_evaluated_.size() - 1, end_i = s_evaluated_.size() - 1; i >= 0;
       --i) {
    if (l_.second[end_i] - l_.first[end_i] - (l_.second[i] - l_.first[i]) >
        2.5) {
      l_.second[end_i] = std::max(l_.first[end_i] + 0.1,
                                  std::min(l_.second[i], l_.second[end_i]));
      l_.first[end_i] = std::min(l_.second[end_i] - 0.1,
                                 std::max(l_.first[i], l_.first[end_i]));
    }
    if (s_evaluated_[end_i] - s_evaluated_[i] > util::truck_.base2tail()) {
      --end_i;
    }
  }
}

void SlTimeInteractBoundary::CalNudgeRef() {
  for (auto& obstacle : reference_line_info_->obstacles().Items()) {
    // 速度快的不nudge
    double relative_speed =
        init_point_.v() -
        (obstacle->obj().is_static() ? 0.0 : obstacle->obj().velocity());
    // 本车道的不nudge
    if (obstacle->sl_boundary().min_l() * obstacle->sl_boundary().max_l() <=
        0.0) {
      continue;
    }
    // 离车道中心线太远(隔着一条车道)的不nudge
    if (std::min(std::abs(obstacle->sl_boundary().min_l()),
                 std::abs(obstacle->sl_boundary().max_l())) >
        util::kHighwayLaneWidth) {
      continue;
    }
    // 离车道中心线太近，已经超出nudge能力 不nudge
    // nudge条件：侵入车道距离 > nudge_lateral_safe_distance
    if (util::kHighwayLaneHalfWidth -
            std::min(std::abs(obstacle->sl_boundary().min_l()),
                     std::abs(obstacle->sl_boundary().max_l())) >
        config_.nudge_condition_latertal_distance()) {
      continue;
    }

    // port::ObstacleDecision decision = port::ObstacleDecision::kNotSet;
    if (obstacle->sl_boundary().max_l() > 0.0) {
      obstacle->AddLateralDecision("qp_path/right-nudge",
                                   port::ObstacleDecision::kRightNudge);
    } else {
      obstacle->AddLateralDecision("qp_path/left-nudge",
                                   port::ObstacleDecision::kLeftNudge);
    }
    const double obs_front2ego_tail =
        obstacle->sl_boundary().max_s() + util::truck_.base2tail();
    const double obs_tail2ego_front =
        obstacle->sl_boundary().min_s() - util::truck_.base2front();
    const double obs_length =
        obstacle->sl_boundary().max_s() - obstacle->sl_boundary().min_s();

    if (fabs(relative_speed) < tport::kMathEpsilon) {
      relative_speed = std::copysign(tport::kMathEpsilon, relative_speed);
    }
    // 到障碍物车尾距离d0<到障碍物车头距离d1
    // 同号时，若障碍物在前方，则t0<t1
    // 若障碍物在后方，则t0>t1
    // 若在侧面，则t1>0>t0
    const double t0 = obs_tail2ego_front / relative_speed;
    const double t1 = obs_front2ego_tail / relative_speed;
    const double t_min = std::min(t0, t1);
    const double t_max = std::max(t0, t1);
    // 后车车速快，但没进入自车范围内，不考虑
    if (relative_speed < 0.0 &&
        t_min - config_.start_delay() > tport::kMathEpsilon) {
      continue;
    }
    // 不会有时间交集
    if (t_max + config_.end_delay() < 0.0) {
      continue;
    }

    // 根据并排时间计算nudge范围
    const double start_s =
        std::min((t_min - config_.start_delay()) * init_point_.v(),
                 obstacle->sl_boundary().min_s() - 10.0);
    const double end_s = (t_max + config_.end_delay()) * init_point_.v();
    if (start_s > s_evaluated_.back() || end_s < init_sl_point_.s()) {
      continue;
    }
    const double nudge_dis =
        tutil::LookupTableLinear(
            relative_speed, config_.relative_speed_table(),
            (static_cast<port::ObjectType>(obstacle->obj().type()) ==
                     port::ObjectType::TRAFFIC_CONE
                 ? config_.nudge_cone_lat_dis_table()
                 : config_.nudge_lat_dis_table())) +
        std::max(obstacle->obj().width() - util::kHighwayLaneWidth, 0.0);
    const double ref_l =
        obstacle->lat_decision() == port::ObstacleDecision::kLeftNudge
            ? obstacle->sl_boundary().max_l() +
                  util::truck_.half_expand_width() + nudge_dis
            : obstacle->sl_boundary().min_l() -
                  util::truck_.half_expand_width() - nudge_dis;
    TRUNK_LOG_INFO << "nudge" << static_cast<int>(obstacle->lat_decision())
                   << ": obstacle " << obstacle->obj().id() << ": [" << start_s
                   << ", " << end_s << "], nudge_dis: " << nudge_dis
                   << ", ref_l: " << ref_l;

    SetNudgeRefBasedOnS(start_s, end_s, obstacle, ref_l);
  }
  for (int i = 0; i < s_evaluated_.size(); ++i) {
    reference_l_[i] =
        config_.mean_deviation()
            ? tutil::clamp(
                  0.5 *
                      (nudge_reference_.first[i] + nudge_reference_.second[i] +
                       (std::abs(nudge_reference_.first[i]) < FLT_EPSILON
                            ? nudge_reference_.second[i]
                            : 0.0) +
                       (std::abs(nudge_reference_.second[i]) < FLT_EPSILON
                            ? nudge_reference_.first[i]
                            : 0.0)),
                  nudge_boundary_.first[i], nudge_boundary_.second[i])
            : tutil::clamp(
                  nudge_reference_.first[i] + nudge_reference_.second[i],
                  nudge_boundary_.first[i], nudge_boundary_.second[i]);
  }
}
void SlTimeInteractBoundary::SetNudgeRefBasedOnS(const double start_s,
                                                 const double end_s,
                                                 const port::Obstacle* obstacle,
                                                 const double ref_l) {
  const auto& obstacle_sl_boundary = obstacle->sl_boundary();
  const auto& decision = obstacle->lat_decision();
  const auto start_iter =
      std::upper_bound(s_evaluated_.begin(), s_evaluated_.end(), start_s);
  const auto end_iter = std::upper_bound(start_iter, s_evaluated_.end(), end_s);
  const size_t start_index_next =
      std::distance(s_evaluated_.begin(), start_iter);
  const size_t start_index =
      start_index_next == 0 ? start_index_next : start_index_next + 1;
  const size_t end_index = std::distance(s_evaluated_.begin(), end_iter);
  for (size_t i = start_index; i < end_index; i++) {
    if (decision == port::ObstacleDecision::kLeftNudge) {
      // 左nudge时，下界增大
      nudge_reference_.first[i] = std::max(nudge_reference_.first[i], ref_l);

      nudge_boundary_.first[i] =
          std::min(std::max(nudge_boundary_.first[i],
                            obstacle->sl_boundary().max_l() +
                                util::truck_.half_expand_width() +
                                config_.min_lateral_safe_distance()),
                   nudge_boundary_.second[i] - 0.1);
    } else if (decision == port::ObstacleDecision::kRightNudge) {
      // 右nudge时，上界减小

      nudge_reference_.second[i] = std::min(nudge_reference_.second[i], ref_l);
      nudge_boundary_.second[i] =
          std::max(std::min(nudge_boundary_.second[i],
                            obstacle->sl_boundary().min_l() -
                                util::truck_.half_expand_width() -
                                config_.min_lateral_safe_distance()),
                   nudge_boundary_.first[i] + 0.1);
    }
  }
}

void SlTimeInteractBoundary::CalCartesianCoordinate() {
  const auto& m_interact_boundaries =
      reference_line_info_->mutable_interact_boundaries();
  m_interact_boundaries->first.clear();
  m_interact_boundaries->first.reserve(s_evaluated_.size());
  m_interact_boundaries->second.clear();
  m_interact_boundaries->second.reserve(s_evaluated_.size());
  for (int i = 0; i < s_evaluated_.size(); i++) {
    const auto p = reference_line_info_->EvaluateByS(s_evaluated_.at(i));
    const double& l = l_.first[i];
    const double& r = l_.second[i];
    const double lx = p.x() - l * std::sin(p.theta());
    const double ly = p.y() + l * std::cos(p.theta());
    const double rx = p.x() - r * std::sin(p.theta());
    const double ry = p.y() + r * std::cos(p.theta());
    m_interact_boundaries->first.emplace_back(lx, ly);
    m_interact_boundaries->second.emplace_back(rx, ry);
  }
}

void SlTimeInteractBoundary::Show() const {
  TRUNK_LOG_INFO << "0th-order boundary list as follow:";
  for (size_t i = 0; i < l_.first.size(); i++) {
    TRUNK_LOG_INFO << absl::StrFormat("(%.3f, %.3f)", l_.first.at(i),
                                      l_.second.at(i));
  }
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
