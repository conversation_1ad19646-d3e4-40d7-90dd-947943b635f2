// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <memory>

#include "comparable_cost.h"
#include "param/dp_path_optimizer_param.h"
#include "port/frame.h"
#include "port/reference_line_info.h"
#include "solver/curve1d/quintic_polynomial_curve1d.h"
#include "trajectory_cost.h"
#include "util/util.h"
namespace trunk {
namespace pnd {
namespace task {
class DPRoadGraph {
 public:
  explicit DPRoadGraph(const port::ReferenceLineInfo& reference_line_info);
  explicit DPRoadGraph(const std::shared_ptr<port::Frame>& frame,
                       const port::ReferenceLineInfo& reference_line_info,
                       const port::SpeedData& speed_data);

  void SetSpeedData(const port::SpeedData speed_data) {
    speed_data_ = speed_data;
  }
  port::SpeedData GetSpeedData() const { return speed_data_; }

  ~DPRoadGraph() = default;
  bool FindPathTunnel(const tport::TrajectoryPoint& init_point,
                      const port::IndexedObstacles& obstacles,
                      port::PathSLData& path_data);

 private:
  struct DPRoadGraphNode {
   public:
    DPRoadGraphNode() = default;

    DPRoadGraphNode(const tport::SLPoint& point_sl,
                    const DPRoadGraphNode* node_prev)
        : sl_point(point_sl), min_cost_prev_node(node_prev) {}

    DPRoadGraphNode(const tport::SLPoint& point_sl,
                    const DPRoadGraphNode* node_prev,
                    const ComparableCost& cost)
        : sl_point(point_sl), min_cost_prev_node(node_prev), min_cost(cost) {}

    void UpdateCost(const DPRoadGraphNode* node_prev,
                    const solver::QuinticPolynomialCurve1d& curve,
                    const ComparableCost& cost) {
      if (cost <= min_cost) {
        min_cost = cost;
        min_cost_prev_node = node_prev;
        min_cost_curve = curve;
      }
    }

    tport::SLPoint sl_point;
    const DPRoadGraphNode* min_cost_prev_node = nullptr;
    ComparableCost min_cost = {true, true, true,
                               std::numeric_limits<float>::infinity(),
                               std::numeric_limits<float>::infinity()};
    solver::QuinticPolynomialCurve1d min_cost_curve;
  };
  void CalculateFrenetPoint(const tport::PathPoint& traj_point,
                            port::PathSLPoint& path_sl_point);
  bool GenerateMinCostPath(const port::IndexedObstacles& obstacles,
                           std::vector<DPRoadGraphNode>& min_cost_path);
  bool SamplePathWaypoints(const tport::TrajectoryPoint& init_point,
                           std::vector<std::vector<tport::SLPoint>>& points);

  void UpdateNode(const std::list<DPRoadGraphNode>& prev_nodes,
                  const uint32_t level, const uint32_t total_level,
                  TrajectoryCost& trajectory_cost, DPRoadGraphNode& cur_node,
                  DPRoadGraphNode* front);

  bool IsValidCurve(const solver::QuinticPolynomialCurve1d& curve) const;

  void getSampleBoundary(double& left_boundary, double& right_boundary,
                         const double& s);
  bool IsSafeToChangeLane();

 private:
  const DpPathOptimizerParam* config_ = nullptr;
  const std::shared_ptr<port::Frame> frame_ = nullptr;
  tport::TrajectoryPoint init_point_;
  port::ReferenceLineInfo reference_line_info_;
  tport::Path reference_line_;
  port::SpeedData speed_data_;
  tport::SLPoint init_sl_point_;
  port::PathSLPoint init_path_sl_point_;
  port::ObstacleDecision sidepass_ = port::ObstacleDecision::kNotSet;
};
}  // namespace task
}  // namespace pnd
}  // namespace trunk
