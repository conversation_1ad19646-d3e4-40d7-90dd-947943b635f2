// Copyright 2023, trunk Inc. All rights reserved

#include "trajectory_cost.h"

namespace trunk {
namespace pnd {
namespace task {

TrajectoryCost::TrajectoryCost(
    const port::ReferenceLineInfo& reference_line_info,
    const bool& is_change_lane_path, const port::IndexedObstacles& obstacles,
    const port::SpeedData& heuristic_speed_data,
    const tport::SLPoint& init_sl_point)
    : reference_line_info_(reference_line_info),
      is_change_lane_path_(is_change_lane_path),
      heuristic_speed_data_(heuristic_speed_data),
      init_sl_point_(init_sl_point) {
  config_ = &tpnc::Singleton<tpnc::Config>::GetInstance()
                 ->GetConfig<DpPathOptimizerParam>("dp_path_optimizer");
  tpnc::Singleton<DpPathOptimizerParam>::GetInstance();
  const float total_time = std::min(heuristic_speed_data_.TotalTime(),
                                    config_->prediction_total_time());
  num_of_time_stamps_ = static_cast<uint32_t>(
      std::floor(total_time / config_->eval_time_interval()));
  // 障碍物的处理
  for (const auto pt : obstacles.Items()) {
    if ((pt->lat_decision() == port::ObstacleDecision::kIgnore &&
         pt->lon_decision() == port::ObstacleDecision::kIgnore) ||
        pt->lon_decision() == port::ObstacleDecision::kStop) {
      continue;
    }
    // 不影响自车的障碍物
    const auto sl_boundary = pt->sl_boundary();
    const auto model = tpnc::Singleton<tmodel::Truck>::GetInstance();
    tutil::TruckParam model_param(*model);
    const double ego_left_l =
        init_sl_point_.l() + model_param.head_width() / 2.0;
    const double ego_right_l =
        init_sl_point_.l() - model_param.head_width() / 2.0;
    if (ego_left_l + config_->lateral_ignore_buffer() < sl_boundary.max_l() ||
        ego_right_l - config_->lateral_ignore_buffer() > sl_boundary.min_l()) {
      continue;
    }
    // TODO: 障碍物的分类
    const auto pt_obstacle = pt->obj();
    if (pt_obstacle.xy_contour().size() < 3) {
      continue;
    } else if (pt->obj().is_static()) {
      // 静态障碍物
      static_obstacle_sl_boundaries_.push_back(std::move(sl_boundary));
    } else if (!pt_obstacle.trajectory().empty()) {
      if (!pt->obj().is_static()) {
        // 暂时不考虑动态障碍物
        continue;
      }
      // 存在预测轨迹的动态障碍物
      std::vector<tport::Contour2D> contour_by_time;
      double time_stamp = 0.0;
      for (size_t index = 0; index < num_of_time_stamps_;
           ++index, time_stamp += config_->eval_time_interval()) {
        const tport::TrajectoryPoint trajectory_point =
            pt_obstacle.trajectory().EvaluateByT(time_stamp);
        // TODO: TrajectoryPoint 转换成Contour2D的形式
        // 目前将预测轨迹视为中点 + sl_bourdary 作为障碍物的轮廓
        tport::Contour2D pt_counter;
        const auto width = (sl_boundary.max_l() - sl_boundary.min_l()) / 2.0;
        const auto height = (sl_boundary.max_s() - sl_boundary.min_s()) / 2.0;
        const double dx1 = cos(trajectory_point.theta()) * height;
        const double dy1 = sin(trajectory_point.theta()) * height;
        const double dx2 = sin(trajectory_point.theta()) * width;
        const double dy2 = -cos(trajectory_point.theta()) * width;
        pt_counter.emplace_back(trajectory_point.x() + dx1 + dx2,
                                trajectory_point.y() + dy1 + dy2);
        pt_counter.emplace_back(trajectory_point.x() + dx1 - dx2,
                                trajectory_point.y() + dy1 - dy2);
        pt_counter.emplace_back(trajectory_point.x() - dx1 - dx2,
                                trajectory_point.y() - dy1 - dy2);
        pt_counter.emplace_back(trajectory_point.x() - dx1 + dx2,
                                trajectory_point.y() - dy1 + dy2);
        contour_by_time.emplace_back(std::move(pt_counter));
      }
      dynamic_obstacle_contour_.emplace_back(std::move(contour_by_time));
    }
  }
}

ComparableCost TrajectoryCost::CalculatePathCost(
    const solver::QuinticPolynomialCurve1d& curve, const double start_s,
    const double end_s, const uint32_t curr_level, const uint32_t total_level) {
  ComparableCost cost;
  double path_cost = 0.0;
  // TODO: 函数待调整
  std::function<float(const float)> quasi_softmax = [this](const float x) {
    const float l0 = this->config_->dp_path_cost_param().path_l_cost_param_l0();
    const float b = this->config_->dp_path_cost_param().path_l_cost_param_b();
    const float k = this->config_->dp_path_cost_param().path_l_cost_param_k();
    return (b + std::exp(-k * (x - l0))) / (1.0 + std::exp(-k * (x - l0)));
  };
  const auto model = tpnc::Singleton<tmodel::Truck>::GetInstance();
  tutil::TruckParam model_param(*model);
  const double width = model_param.head_width();
  for (double curve_s = 0.0; curve_s < (end_s - start_s);
       curve_s += config_->path_resolution()) {
    const double l = curve.Evaluate(0, curve_s);
    path_cost += l * l * config_->dp_path_cost_param().path_l_cost() *
                 quasi_softmax(std::fabs(l));
    auto const lane_width =
        reference_line_info_.GetLaneWidth(curve_s + start_s);
    constexpr float kBuff = 0.2;
    // TODO: 待调整 允许nudge
    if (!is_change_lane_path_ &&
        (l + kBuff - config_->nudge_width() > lane_width.left_width ||
         l - kBuff + config_->nudge_width() < lane_width.right_width)) {
      cost.cost_items[ComparableCost::OUT_OF_BOUNDARY] = true;
    }
    // 纵向速度的开销 path_dl_cost = 8000
    const float dl = std::fabs(curve.Evaluate(1, curve_s));
    path_cost += dl * dl * config_->dp_path_cost_param().path_dl_cost();
    // 纵向加速度的开销 path_ddl_cost = 5
    const float ddl = std::fabs(curve.Evaluate(2, curve_s));
    path_cost += ddl * ddl * config_->dp_path_cost_param().path_ddl_cost();
  }
  path_cost *= config_->path_resolution();
  // 结束时的纵向开销设置大点   杜绝不必要的变道
  if (curr_level == total_level) {
    const double end_l = curve.Evaluate(0, end_s - start_s);
    path_cost += std::sqrt(std::abs(end_l)) *
                 config_->dp_path_cost_param().path_end_l_cost();
  }
  cost.smoothness_cost = path_cost;

  return cost;
}

ComparableCost TrajectoryCost::CalculateStaticObstacleCost(
    const solver::QuinticPolynomialCurve1d& curve, const double start_s,
    const double end_s) {
  ComparableCost obstacle_cost;
  for (float curr_s = start_s; curr_s <= end_s;
       curr_s += config_->path_resolution()) {
    // 对于每两个采样点之间离散，判断每个离散点之间的代价
    const float curr_l = curve.Evaluate(0, curr_s - start_s);
    for (const auto& obs_sl_boundary : static_obstacle_sl_boundaries_) {
      obstacle_cost += GetCostFromObsSL(curr_s, curr_l, obs_sl_boundary);
    }
  }
  obstacle_cost.safety_cost *= config_->path_resolution();
  return obstacle_cost;
}

ComparableCost TrajectoryCost::CalculateDynamicObstacleCost(
    const solver::QuinticPolynomialCurve1d& curve, const double start_s,
    const double end_s) {
  ComparableCost obstacle_cost;

  double time_stamp = tpnc::Singleton<tpnc::Config>::GetInstance()
                          ->GetConfig<SysParam>("sys")
                          .stitch_time();

  for (size_t index = 0; index < num_of_time_stamps_;
       ++index, time_stamp += config_->eval_time_interval()) {
    port::SpeedSTPoint speed_point;
    heuristic_speed_data_.EvaluateByTime(time_stamp, &speed_point);
    double ref_s = speed_point.s() + init_sl_point_.s();
    if (ref_s < start_s) {
      continue;
    }
    if (ref_s > end_s) {
      break;
    }
    const double s = ref_s - start_s;  // s on spline curve
    const double l = curve.Evaluate(0, s);
    const double dl = curve.Evaluate(1, s);
    const tport::SLPoint sl(ref_s, l);
    const auto ego_contour = GetEgoFromSLPoint(sl, dl);  // 自车边界
    for (const auto& obstacle_trajectory : dynamic_obstacle_contour_) {
      obstacle_cost +=
          GetCostBetweenObsContours(ego_contour, obstacle_trajectory.at(index));
    }
  }
  constexpr float kDynamicObsWeight = 1e-6;
  obstacle_cost.safety_cost *=
      (config_->eval_time_interval() * kDynamicObsWeight);
  return obstacle_cost;
}

ComparableCost TrajectoryCost::GetCostFromObsSL(
    const double ego_s, const double ego_l,
    const port::SLBoundary& obs_sl_boundary) {
  const auto model = tpnc::Singleton<tmodel::Truck>::GetInstance();
  tutil::TruckParam model_param(*model);
  // TODO: 考虑挂的长度
  if (!util::sys_config_.with_trailer()) {
    model_param.set_trailer_base2tail(0.0);
  }
  ComparableCost obstacle_cost;
  const double ego_left_l = ego_l + model_param.head_width() / 2.0;
  const double ego_right_l = ego_l - model_param.head_width() / 2.0;
  const double ego_front_s = ego_s + model_param.head_base2front();
  const double ego_end_s = ego_s - model_param.trailer_base2tail();

  if (ego_left_l + config_->lateral_ignore_buffer() < obs_sl_boundary.min_l() ||
      ego_right_l - config_->lateral_ignore_buffer() >
          obs_sl_boundary.max_l()) {
    return obstacle_cost;
  }
  // 碰撞判断
  bool no_overlap =
      ((ego_front_s < obs_sl_boundary.min_s() ||
        ego_end_s > obs_sl_boundary.max_s()) ||  // longitudinal
       (ego_left_l +
                config_->dp_path_cost_param().static_decision_nudge_l_buffer() <
            obs_sl_boundary.min_l() ||
        ego_right_l -
                config_->dp_path_cost_param().static_decision_nudge_l_buffer() >
            obs_sl_boundary.max_l()));  // lateral
  if (!no_overlap) {
    obstacle_cost.cost_items[ComparableCost::HAS_COLLISION] = true;
  }
  if (ego_front_s > obs_sl_boundary.max_s()) {
    return obstacle_cost;
  }
  const double delta_l = std::fabs(
      ego_l - (obs_sl_boundary.min_l() + obs_sl_boundary.max_l()) / 2.0);
  obstacle_cost.safety_cost +=
      config_->dp_path_cost_param().obstacle_collision_cost() *
      Sigmoid(config_->dp_path_cost_param().obstacle_collision_distance() -
              delta_l);
  return obstacle_cost;
}
ComparableCost TrajectoryCost::GetCostBetweenObsContours(
    const tport::Contour2D& ego_contour,
    const tport::Contour2D& obstacle_contour) {
  ComparableCost obstacle_cost;
  // 以安全代价的形式计算
  const double distance = DistanceTo(ego_contour, obstacle_contour);
  if (distance > config_->dp_path_cost_param().obstacle_ignore_distance()) {
    return obstacle_cost;
  }

  obstacle_cost.safety_cost +=
      config_->dp_path_cost_param().obstacle_collision_cost() *
      Sigmoid(config_->dp_path_cost_param().obstacle_collision_distance() -
              distance);
  obstacle_cost.safety_cost +=
      20.0 * Sigmoid(config_->dp_path_cost_param().obstacle_risk_distance() -
                     distance);
  return obstacle_cost;
}

ComparableCost TrajectoryCost::Calculate(
    const solver::QuinticPolynomialCurve1d& curve, const double start_s,
    const double end_s, const uint32_t curr_level, const uint32_t total_level) {
  ComparableCost total_cost;

  // path cost
  total_cost +=
      CalculatePathCost(curve, start_s, end_s, curr_level, total_level);

  // static obstacle cost
  total_cost += CalculateStaticObstacleCost(curve, start_s, end_s);

  // dynamic obstacle cost
  // total_cost += CalculateDynamicObstacleCost(curve, start_s, end_s);

  // TRUNK_LOG_DEBUG << "total_cost: " << total_cost.safety_cost;
  return total_cost;
}

tport::Contour2D TrajectoryCost::GetEgoFromSLPoint(const tport::SLPoint sl,
                                                   const double dl) {
  tport::Contour2D ego_contour;
  // 自车相对起点的位置计算
  // TODO: 需要检查返回的值是否是车辆坐标系下的值
  auto ref_point = reference_line_info_.reference_line().EvaluateByS(sl.s());
  tport::Point2D ego_point;
  ego_point.set_x(ref_point.x() - sin(ref_point.theta()) *
                                      sl.l());  // TODO:角度和弧度是否需要转换
  ego_point.set_y(ref_point.y() + cos(ref_point.theta()) * sl.l());
  const double one_minus_kappa_r_d = 1 - ref_point.kappa() * sl.l();
  const double delta_theta = std::atan2(dl, one_minus_kappa_r_d);
  const double theta = tutil::NormalizeAngle(
      delta_theta + ref_point.theta());  // 车辆此时的航向角
  const auto model = tpnc::Singleton<tmodel::Truck>::GetInstance();
  tutil::TruckParam model_param(*model);
  // TODO: 考虑挂的长度
  if (!util::sys_config_.with_trailer()) {
    model_param.set_trailer_base2tail(0.0);
  }
  //
  // 计算车辆包络
  // NOTE:基于几何计算
  const double dx1 = cos(theta) * model_param.head_base2front();
  const double dy1 = sin(theta) * model_param.head_base2front();
  const double dx2 = sin(theta) * model_param.head_width() / 2.0;
  const double dy2 = -cos(theta) * model_param.head_width() / 2.0;
  const double dx3 = cos(theta) * model_param.trailer_base2tail();
  const double dy3 = sin(theta) * model_param.trailer_base2tail();
  ego_contour.emplace_back(ego_point.x() + dx1 + dx2,
                           ego_point.y() + dy1 + dy2);
  ego_contour.emplace_back(ego_point.x() + dx1 - dx2,
                           ego_point.y() + dy1 - dy2);
  ego_contour.emplace_back(ego_point.x() - dx3 - dx2,
                           ego_point.y() - dy3 - dy2);
  ego_contour.emplace_back(ego_point.x() - dx3 + dx2,
                           ego_point.y() - dy3 + dy2);
  return ego_contour;
}
double TrajectoryCost::DistanceTo(const tport::Contour2D& ego_contour,
                                  const tport::Contour2D& obstacle_contour) {
  // TODO: 待更改为更加高效的算法
  //  与障碍物的碰撞距离
  double dis = DBL_MAX;
  if (ego_contour.size() < 3 || obstacle_contour.size() < 3) {
    return dis;
  }

  for (const auto& ego_pt : ego_contour) {
    for (int i = 0; i < obstacle_contour.size(); ++i) {
      const auto start = obstacle_contour[i];
      const auto end = obstacle_contour[(i + 1) % obstacle_contour.size()];
      const auto dis_to_seg = PointToSegmentDistance(ego_pt, start, end);
      dis = std::fmin(dis, dis_to_seg);
    }
  }

  for (const auto& obstacle_pt : obstacle_contour) {
    for (int i = 0; i < ego_contour.size(); ++i) {
      const auto start = ego_contour[i];
      const auto end = ego_contour[(i + 1) % ego_contour.size()];
      const auto dis_to_seg = PointToSegmentDistance(obstacle_pt, start, end);
      dis = std::fmin(dis, dis_to_seg);
    }
  }
  return dis;
}

double TrajectoryCost::PointToSegmentDistance(const tutil::Point& p,
                                              const tutil::Point& start,
                                              const tutil::Point& end) {
  const tutil::Point sp = p - start, se = end - start;
  double se2 = se.InnerProd(se);
  double sp_se = sp.InnerProd(se);
  double t = sp_se / se2;
  if (t < 0.0) {
    t = 0.0;
  } else if (t > 1.0) {
    t = 1.0;
  }
  auto closest = start + se * t;
  closest = p - closest;
  return std::hypot(closest.x(), closest.y());
}

// TODO: Sigmod
double TrajectoryCost::Sigmoid(const double x) {
  return 1.0 / (1.0 + std::exp(-x));
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
