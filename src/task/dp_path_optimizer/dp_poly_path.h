// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include "dp_road_graph.h"
#include "port/frame.h"
#include "task/task.h"

namespace trunk {
namespace pnd {
namespace task {

class DpPolyPathTask : public Task {
 public:
  DpPolyPathTask() : Task("dp_poly_path") {}

  virtual ~DpPolyPathTask() = default;

  tpnc::Status Execute(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info) override;
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
