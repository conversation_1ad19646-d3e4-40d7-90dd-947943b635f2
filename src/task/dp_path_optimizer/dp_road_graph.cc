// Copyright 2023, trunk Inc. All rights reserved

#include "dp_road_graph.h"

namespace trunk {
namespace pnd {
namespace task {
// TODO:构造函数  参数传入
DPRoadGraph::DPRoadGraph(const port::ReferenceLineInfo& reference_line_info)
    : reference_line_info_(reference_line_info),
      reference_line_(reference_line_info.reference_line()) {
  config_ = &tpnc::Singleton<tpnc::Config>::GetInstance()
                 ->GetConfig<DpPathOptimizerParam>("dp_path_optimizer");
}

DPRoadGraph::DPRoadGraph(const std::shared_ptr<port::Frame>& frame,
                         const port::ReferenceLineInfo& reference_line_info,
                         const port::SpeedData& speed_data)
    : frame_(frame),
      reference_line_info_(reference_line_info),
      reference_line_(reference_line_info.reference_line()),
      speed_data_(speed_data) {
  config_ = &tpnc::Singleton<tpnc::Config>::GetInstance()
                 ->GetConfig<DpPathOptimizerParam>("dp_path_optimizer");
}
bool DPRoadGraph::FindPathTunnel(const tport::TrajectoryPoint& init_point,
                                 const port::IndexedObstacles& obstacles,
                                 port::PathSLData& path_data) {
  init_point_ = init_point;
  // TODO: 可能存在初始点不在参考线片段处，待处理
  CalculateFrenetPoint(init_point_, init_path_sl_point_);
  init_sl_point_.set_l(init_path_sl_point_.l());
  init_sl_point_.set_s(init_path_sl_point_.s());
  TRUNK_LOG_DEBUG << "ego_sl: " << "(" << init_sl_point_.s() << ", "
                  << init_sl_point_.l() << ")";
  std::vector<DPRoadGraphNode> min_cost_path;
  if (!GenerateMinCostPath(obstacles, min_cost_path)) {
    TRUNK_LOG_ERROR << "Fail to generate graph!";
    return false;
  }
  // std::vector<port::PathSLPoint> frenet_path;
  // path_data.push_back(init_path_sl_point_);
  double accumulated_s = init_sl_point_.s();
  const double path_resolution = config_->path_resolution();
  for (size_t i = 1; i < min_cost_path.size(); ++i) {
    const auto& prev_node = min_cost_path[i - 1];
    const auto& cur_node = min_cost_path[i];
    const double path_length = cur_node.sl_point.s() - prev_node.sl_point.s();
    float current_s = 0.0;
    const auto& curve = cur_node.min_cost_curve;
    while (current_s + path_resolution / 2.0 < path_length) {
      const double l = curve.Evaluate(0, current_s);
      const double dl = curve.Evaluate(1, current_s);
      const double ddl = curve.Evaluate(2, current_s);
      port::PathSLPoint frenet_frame_point;
      frenet_frame_point.set_s(accumulated_s + current_s);
      frenet_frame_point.set_l(l);
      frenet_frame_point.set_dl(dl);
      frenet_frame_point.set_ddl(ddl);
      path_data.push_back(std::move(frenet_frame_point));
      current_s += path_resolution;
    }
    if (i == min_cost_path.size() - 1) {
      accumulated_s += current_s;
    } else {
      accumulated_s += path_length;
    }
  }
  return true;
}
void DPRoadGraph::CalculateFrenetPoint(const tport::PathPoint& traj_point,
                                       port::PathSLPoint& path_sl_point) {
  const auto sl_point = tutil::TransformFrenet::TransformToFrenet(
      reference_line_,
      traj_point);  // 可能存在初始点不在参考线片段处，待处理
  path_sl_point.set_s(sl_point.s());
  path_sl_point.set_l(sl_point.l());
  path_sl_point.set_x(traj_point.x());
  path_sl_point.set_y(traj_point.y());
  const double theta = traj_point.theta();
  const double kappa = traj_point.kappa();
  auto ref_point = reference_line_.EvaluateByS(path_sl_point.s());
  const double theta_ref = ref_point.theta();  // 车体坐标系
  const double kappa_ref = ref_point.kappa();
  const double dkappa_ref = ref_point.dkappa();
  const double dl = tutil::CartesianFrenetConverter::CalculateLateralDerivative(
      theta_ref, theta, path_sl_point.l(), kappa_ref);
  const double ddl =
      tutil::CartesianFrenetConverter::CalculateSecondOrderLateralDerivative(
          theta_ref, theta, kappa_ref, kappa, dkappa_ref, path_sl_point.l());
  path_sl_point.set_dl(dl);
  path_sl_point.set_ddl(ddl);
}

bool DPRoadGraph::GenerateMinCostPath(
    const port::IndexedObstacles& obstacles,
    std::vector<DPRoadGraphNode>& min_cost_path) {
  std::vector<std::vector<tport::SLPoint>> path_waypoints;
  if (!SamplePathWaypoints(init_point_, path_waypoints) ||
      path_waypoints.size() < 1) {
    TRUNK_LOG_ERROR << "Fail to sample path waypoints! reference_line_length ="
                    << reference_line_.size();
    return false;
  }
  path_waypoints.insert(path_waypoints.begin(),
                        std::vector<tport::SLPoint>{init_sl_point_});
  TrajectoryCost trajectory_cost(
      reference_line_info_,
      reference_line_info_.target_info() &&
          reference_line_info_.target_info()->is_target,
      obstacles, speed_data_, init_sl_point_);
  std::list<std::list<DPRoadGraphNode>> graph_nodes;
  graph_nodes.emplace_back();
  graph_nodes.back().emplace_back(init_sl_point_, nullptr, ComparableCost());
  auto& front = graph_nodes.front().front();
  size_t total_level = path_waypoints.size() - 1;
  for (size_t level = 1; level < path_waypoints.size(); ++level) {
    const auto& prev_dp_nodes = graph_nodes.back();
    const auto& level_points = path_waypoints[level];
    graph_nodes.emplace_back();
    for (size_t i = 0; i < level_points.size(); ++i) {
      const auto& cur_point = level_points[i];
      graph_nodes.back().emplace_back(cur_point, nullptr);
      auto& cur_node = graph_nodes.back().back();
      UpdateNode(prev_dp_nodes, level, total_level, trajectory_cost, cur_node,
                 &front);
    }
  }
  // find best path
  DPRoadGraphNode fake_head;
  for (const auto& cur_dp_node : graph_nodes.back()) {
    fake_head.UpdateCost(&cur_dp_node, cur_dp_node.min_cost_curve,
                         cur_dp_node.min_cost);
    TRUNK_LOG_DEBUG << "node_cost: " << cur_dp_node.min_cost.safety_cost << ", "
                    << cur_dp_node.min_cost.smoothness_cost
                    << ", has_collision: "
                    << cur_dp_node.min_cost.cost_items[0];
  }
  const auto* min_cost_node = &fake_head;
  while (min_cost_node->min_cost_prev_node) {
    min_cost_node = min_cost_node->min_cost_prev_node;
    min_cost_path.emplace_back(*min_cost_node);
  }
  // TODO: check
  if (min_cost_node != &graph_nodes.front().front()) {
    return false;
  }
  std::reverse(min_cost_path.begin(), min_cost_path.end());
  return true;
}

void DPRoadGraph::UpdateNode(const std::list<DPRoadGraphNode>& prev_nodes,
                             const uint32_t level, const uint32_t total_level,
                             TrajectoryCost& trajectory_cost,
                             DPRoadGraphNode& cur_node,
                             DPRoadGraphNode* front) {
  // TODO: 需要检查逻辑
  if (front == nullptr) {
    return;
  }
  for (const auto& prev_dp_node : prev_nodes) {
    const auto& prev_sl_point = prev_dp_node.sl_point;
    const auto& cur_point = cur_node.sl_point;
    double init_dl = 0.0;
    double init_ddl = 0.0;
    if (level == 1) {
      init_dl = init_path_sl_point_.dl();
      init_ddl = init_path_sl_point_.ddl();
    }
    solver::QuinticPolynomialCurve1d curve(prev_sl_point.l(), init_dl, init_ddl,
                                           cur_point.l(), 0.0, 0.0,
                                           cur_point.s() - prev_sl_point.s());
    if (!IsValidCurve(curve)) {
      continue;
    }
    const auto cost =
        trajectory_cost.Calculate(curve, prev_sl_point.s(), cur_point.s(),
                                  level, total_level) +
        prev_dp_node.min_cost;
    cur_node.UpdateCost(&prev_dp_node, curve, cost);
  }

  // 尝试是否可以与初始位置直接连接
  if (level >= 2 && front != nullptr) {
    const double init_dl = init_path_sl_point_.dl();
    const double init_ddl = init_path_sl_point_.ddl();
    solver::QuinticPolynomialCurve1d curve(
        init_sl_point_.l(), init_dl, init_ddl, cur_node.sl_point.l(), 0.0, 0.0,
        cur_node.sl_point.s() - init_sl_point_.s());
    if (!IsValidCurve(curve)) {
      return;
    }
    const auto cost = trajectory_cost.Calculate(
        curve, init_sl_point_.s(), cur_node.sl_point.s(), level, total_level);
    cur_node.UpdateCost(front, curve, cost);
  }
}

bool DPRoadGraph::SamplePathWaypoints(
    const tport::TrajectoryPoint& init_point,
    std::vector<std::vector<tport::SLPoint>>& points) {
  // TODO: 是否检查变道是否安全？？？
  const double kMinSampleDistance = config_->sample_distance();
  const double total_length = std::fmin(
      init_sl_point_.s() + std::fmax(init_point.v() * config_->sample_time(),
                                     kMinSampleDistance),
      reference_line_.back().s());
  const size_t num_sample_per_level = config_->sample_points_num_each_level();
  constexpr float kSamplePointLookForwardTime = 4.0;  // 前向采样时长
  const float step_length = tutil::clamp(
      init_point.v() * kSamplePointLookForwardTime, config_->step_length_min(),
      config_->step_length_max());  // 单步长   限制长度不超出上下界
  const float level_distance = (init_point.v() > config_->max_stop_speed())
                                   ? step_length
                                   : step_length / 2.0;

  if (level_distance == 0) {
    TRUNK_LOG_ERROR << "level_distance: " << level_distance;
    return false;
  }
  auto base_ref = reference_line_;
  if (frame_->current_reference_line_info() != nullptr) {
    base_ref = frame_->current_reference_line_info()->reference_line();
  }
  double accumulated_s = init_sl_point_.s();
  double init_delta_l =
      tutil::TransformFrenet::TransformToFrenet(base_ref, init_path_sl_point_)
          .l();
  double prev_s = accumulated_s;
  int index = 0;
  for (std::size_t i = 0; accumulated_s < total_length; ++i) {
    accumulated_s += level_distance;
    // 越界时的处理
    if (accumulated_s + level_distance / 2.0 > total_length) {
      accumulated_s = total_length;
    }
    double s = std::fmin(accumulated_s, total_length);
    // TODO: 最小的采样横向距离间隔 待调参
    constexpr double kMinAllowedSampleStep = 1.0;
    if (std::fabs(s - prev_s) < kMinAllowedSampleStep) {
      continue;
    }
    // 横向起始的l值
    for (int j = index; j < base_ref.size(); ++j) {
      if (base_ref[j].s() >= s) {
        index = j;
        break;
      }
    }

    double init_l = init_delta_l + tutil::TransformFrenet::TransformToFrenet(
                                       reference_line_, base_ref[index])
                                       .l();
    TRUNK_LOG_DEBUG << "init_l: " << init_l
                    << ", init_delta_l: " << init_delta_l
                    << ", base_ref: " << base_ref[index];
    prev_s = s;
    double left_width = 0.0;
    double right_width = 0.0;
    getSampleBoundary(left_width, right_width, s);

    const auto model = tpnc::Singleton<tmodel::Truck>::GetInstance();
    tutil::TruckParam model_param(*model);
    const double half_ego_width = model_param.head_width() / 2.0;

    auto width = reference_line_info_.GetLaneWidth(s);
    // 缓冲距离
    constexpr double kBoundaryBuff = 0.25;
    const double eff_right_width =
        width.right_width + half_ego_width + kBoundaryBuff;
    const double eff_left_width =
        width.left_width - half_ego_width - kBoundaryBuff;

    const double delta_dl = 1.2 / 20.0;
    const double kChangeLaneDeltaL = tutil::clamp(
        level_distance * (std::fabs(init_path_sl_point_.dl()) + delta_dl), 1.2,
        3.5);

    double kDefaultUnitL = kChangeLaneDeltaL / (num_sample_per_level - 1);
    if (reference_line_info_.IsLaneChangeLane() && !IsSafeToChangeLane()) {
      kDefaultUnitL = 1.0;
    }
    const double sample_l_range =
        1.5 * kDefaultUnitL * (num_sample_per_level - 1);
    double sample_right_boundary = eff_right_width;
    double sample_left_boundary = eff_left_width;

    const double kLargeDeviationL = 1.75;
    if (reference_line_info_.IsLaneChangeLane() ||
        std::fabs(init_l) > kLargeDeviationL) {
      TRUNK_LOG_DEBUG << "l: " << init_l
                      << ", eff_right_width: " << eff_right_width
                      << ", eff_left_width: " << eff_left_width;
      sample_right_boundary = std::fmin(eff_right_width, init_l);
      sample_left_boundary = std::fmax(eff_left_width, init_l);

      if (init_l > eff_left_width) {
        sample_right_boundary =
            std::fmax(sample_right_boundary, init_l - sample_l_range);
      }
      if (init_l < eff_right_width) {
        sample_left_boundary =
            std::fmin(sample_left_boundary, init_l + sample_l_range);
      }
    }

    sample_left_boundary = std::fmin(sample_left_boundary, left_width);
    sample_right_boundary = std::fmax(sample_right_boundary, right_width);

    std::vector<double> sample_l;
    // if (reference_line_info_.IsLaneChangeLane() && !IsSafeToChangeLane()) {
    //   sample_l.push_back(0.0);
    // } else {
    //   util::uniform_slice(sample_left_boundary, sample_right_boundary,
    //                       num_sample_per_level - 1, sample_l);
    // }
    util::uniform_slice(sample_left_boundary, sample_right_boundary,
                        num_sample_per_level - 1, sample_l);
    // } else if () {
    //   switch(){
    //     case 1: {
    //       sample_l.push_back(eff_left_width + 2.8);
    //       break;
    //     }
    //     case 2: {
    //       sample_l.push_back(-eff_left_width - 2.8);
    //       break;
    //     }
    //     default:
    //       break;
    // }else {
    //   util::uniform_slice(sample_left_boundary, sample_right_boundary,
    //                       num_sample_per_level - 1, sample_l);
    // }

    // util::uniform_slice(sample_left_boundary, sample_right_boundary,
    //                       num_sample_per_level - 1, sample_l);
    TRUNK_LOG_DEBUG << "init_sl_point: " << init_sl_point_.s() << ", "
                    << init_sl_point_.l();
    TRUNK_LOG_DEBUG << "sample_left_boundary: " << sample_left_boundary
                    << ", sample_right_boundary: " << sample_right_boundary;
    TRUNK_LOG_DEBUG << "accumulated_s: " << accumulated_s
                    << ", sample_l: " << sample_l;

    // 采样

    // 采样点转换
    std::vector<tport::SLPoint> level_points;
    // if (left_width > 0 && right_width < 0) {
    //   level_points.emplace_back(port::SLPoint(s, 0));  // 添加参考线上的采样
    // }
    for (size_t j = 0; j < sample_l.size(); ++j) {
      if (!reference_line_info_.IsLaneChangeLane() &&
          std::fabs(sample_l[j]) < 0.1) {
        // 过滤掉过与参考线接近的采样点，防止与后续的参考线上的采样点过近
        continue;
      }
      tport::SLPoint sl(s, sample_l[j]);
      level_points.emplace_back(std::move(sl));
    }
    if (!reference_line_info_.IsLaneChangeLane()) {
      level_points.push_back(tport::SLPoint(s, 0));
    }

    if (!level_points.empty()) {
      points.emplace_back(std::move(level_points));
    }
  }
  if (points.empty()) {
    return false;
  }
  // TRUNK_LOG_DEBUG << "points: " << points.size();
  return true;
}

bool DPRoadGraph::IsValidCurve(
    const solver::QuinticPolynomialCurve1d& curve) const {
  for (double s = 0.0; s < curve.ParamLength(); s += 2.0) {
    const double l = curve.Evaluate(0, s);
    if (std::fabs(l) > config_->max_lateral_distance()) {
      return false;
    }
  }
  return true;
}

// TODO:  待优化
void DPRoadGraph::getSampleBoundary(double& left_boundary,
                                    double& right_boundary, const double& s) {
  // 1. 车辆位置判断
  auto width = reference_line_info_.GetLaneWidth(s);
  auto road_width = reference_line_info_.GetRoadWidth(s);

  TRUNK_LOG_DEBUG << "width: (" << width.left_width << ", " << width.right_width
                  << ")";
  TRUNK_LOG_DEBUG << "road_width: (" << road_width.left_width << ", "
                  << road_width.right_width << ")";

  bool left_is_solid_line = false, right_is_solid_line = false;
  // 如果道路边界和可行驶边界相同，则是实线
  if (abs(width.right_width - road_width.right_width) < 0.1) {
    // 左边界是实线
    right_is_solid_line = true;
  }
  if (abs(width.left_width - road_width.left_width) < 0.1) {
    // 右边界是实线
    left_is_solid_line = true;
  }

  // 是否在本车道
  bool left_side_of_ref = false, right_side_of_ref = false;

  if (init_sl_point_.l() < width.right_width) {
    right_side_of_ref = true;
  } else if (init_sl_point_.l() > width.left_width) {
    left_side_of_ref = true;
  }
  TRUNK_LOG_DEBUG << "left_side_of_ref: " << left_side_of_ref
                  << ", right_side_of_ref: " << right_side_of_ref;

  // 2. 边界生成
  const auto model = tpnc::Singleton<tmodel::Truck>::GetInstance();
  tutil::TruckParam model_param(*model);
  const double half_ego_width = model_param.head_width() / 2.0;
  // TODO:缓冲距离
  // 如果更改缓冲距离，需要同时修改trajectory_cost.cc里CalculatePathCost的值
  constexpr double kBoundaryBuff = 0.20;

  if (right_side_of_ref) {
    left_boundary = width.left_width - half_ego_width - kBoundaryBuff;
    if (right_is_solid_line) {
      left_boundary = width.right_width - half_ego_width - kBoundaryBuff;
    }
    right_boundary =
        std::min(init_sl_point_.l(), left_boundary) - config_->solid_dp_width();
  } else if (left_side_of_ref) {
    right_boundary = width.right_width + half_ego_width + kBoundaryBuff;
    if (left_side_of_ref) {
      right_boundary = width.left_width + half_ego_width + kBoundaryBuff;
    }
    left_boundary = std::max(init_sl_point_.l(), right_boundary) +
                    config_->solid_dp_width();
  } else {
    // 在本车道时，采样可以超出本车道
    left_boundary = width.left_width - half_ego_width - kBoundaryBuff;
    right_boundary = width.right_width + kBoundaryBuff + half_ego_width;
    if (!left_is_solid_line) {
      left_boundary = left_boundary + config_->nudge_width();
    }
    if (!right_is_solid_line) {
      right_boundary = width.right_width - config_->nudge_width();
    }
  }
}

bool DPRoadGraph::IsSafeToChangeLane() {
  const auto obstacles = reference_line_info_.obstacles();
  for (const auto pt : obstacles.Items()) {
    const auto& sl_boundary = pt->sl_boundary();

    constexpr float kLateralShift = 2.5;
    if (sl_boundary.min_l() < -kLateralShift ||
        sl_boundary.max_l() > kLateralShift) {
      continue;
    }

    constexpr float kSafeTime = 3.0;
    constexpr float kForwardMinSafeDistance = 6.0;
    constexpr float kBackwardMinSafeDistance = 8.0;

    const float kForwardSafeDistance =
        std::max(kForwardMinSafeDistance,
                 static_cast<float>((init_point_.v() - pt->obj().velocity()) *
                                    kSafeTime));
    const float kBackwardSafeDistance =
        std::max(kBackwardMinSafeDistance,
                 static_cast<float>((pt->obj().velocity() - init_point_.v()) *
                                    kSafeTime));
    if (sl_boundary.max_s() > reference_line_info_.AdcSlBoundary().min_s() -
                                  kBackwardSafeDistance &&
        sl_boundary.min_s() < reference_line_info_.AdcSlBoundary().max_s() +
                                  kForwardSafeDistance) {
      return false;
    }
  }
  return true;
}
}  // namespace task
}  // namespace pnd
}  // namespace trunk
