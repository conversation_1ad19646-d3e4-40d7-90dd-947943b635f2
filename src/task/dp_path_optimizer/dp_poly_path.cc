// Copyright 2023, trunk Inc. All rights reserved

#include "dp_poly_path.h"

namespace trunk {
namespace pnd {
namespace task {

REGISTER(trunk::pnd::task, Task, DpPolyPath);

tpnc::Status DpPolyPathTask::Execute(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  if (frame == nullptr || reference_line_info == nullptr) {
    TRUNK_LOG_ERROR << "frame or reference_line_info is null";
    return tpnc::Status::ERROR();
  }
  DPRoadGraph dp_road_graph(frame, *reference_line_info,
                            reference_line_info->speed_data());

  if (!reference_line_info->is_drivable().first) {
    TRUNK_LOG_DEBUG << "Reference line is not drivable";
    return tpnc::Status::OK();
  }
  port::PathSLData path_data;
  if (!dp_road_graph.FindPathTunnel(frame->path_init_point(),
                                    reference_line_info->obstacles(),
                                    path_data)) {
    TRUNK_LOG_ERROR << "Failed to find tunnel in road graph";
    return tpnc::Status::ERROR();  // TODO: 当前的输出等级是什么
  }
  reference_line_info->mutable_path_data()->set_dp_sl_path(path_data);
  reference_line_info->DpSlPath2XyPath();

  return tpnc::Status::OK();
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
