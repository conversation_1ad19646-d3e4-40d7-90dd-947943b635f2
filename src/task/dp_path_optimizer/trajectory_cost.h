// Copyright 2023, trunk Inc. All rights reserved.

#pragma once

#include <vector>

#include "comparable_cost.h"
#include "param/dp_path_optimizer_param.h"
#include "param/sys_param.h"
#include "port/frame.h"
#include "port/reference_line_info.h"
#include "port/sl/sl_boundary.h"
#include "port/st/speed_data.h"
#include "solver/curve1d/quintic_polynomial_curve1d.h"
#include "util/util.h"

namespace trunk {
namespace pnd {
namespace task {

class TrajectoryCost {
 public:
  TrajectoryCost() = default;
  ~TrajectoryCost() = default;
  explicit TrajectoryCost(const port::ReferenceLineInfo& reference_line_info,
                          const bool& is_change_lane_path,
                          const port::IndexedObstacles& obstacles,
                          const port::SpeedData& heuristic_speed_data,
                          const tport::SLPoint& init_sl_point);
  ComparableCost Calculate(const solver::QuinticPolynomialCurve1d& curve,
                           const double start_s, const double end_s,
                           const uint32_t curr_level,
                           const uint32_t total_level);
  //   void SetHeuristicSpeedData(const port::SpeedData* heuristic_speed_data) {
  //     heuristic_speed_data_ = heuristic_speed_data;
  //   }

  //   const port::SpeedData* GetHeuristicSpeedData() const {
  //     return heuristic_speed_data_;
  //   }

 private:
  ComparableCost GetCostFromObsSL(const double adc_s, const double adc_l,
                                  const port::SLBoundary& obs_sl_boundary);
  /**
   * Calculates the cost of a trajectory based on the obstacle's position in the
   * SL coordinate system.
   *
   * @param adc_s The longitudinal position of the ego vehicle in the SL
   * coordinate system.
   * @param adc_l The lateral position of the ego vehicle in the SL coordinate
   * system.
   * @param obs_sl_boundary The SL boundary of the obstacle.
   *
   * @return The cost of the trajectory based on the obstacle's position.
   *
   * @throws None
   */

  ComparableCost CalculatePathCost(
      const solver::QuinticPolynomialCurve1d& curve, const double start_s,
      const double end_s, const uint32_t curr_level,
      const uint32_t total_level);
  /**
   * Calculate the cost of a path.
   *
   * @param curve the quintic polynomial curve representing
   * the path
   * @param start_s the starting position along the path
   * @param end_s the ending position along the path
   * @param curr_level the current level of the path
   * @param total_level the total number of levels in the
   * path
   *
   * @return the cost of the path
   *
   * @throws None
   */

  ComparableCost CalculateStaticObstacleCost(
      const solver::QuinticPolynomialCurve1d& curve, const double start_s,
      const double end_s);
  /**
   * Calculates the static obstacle cost for a given trajectory.
   *
   * @param curve the QuinticPolynomialCurve1d representing the trajectory
   * @param start_s the starting s-coordinate of the trajectory
   * @param end_s the ending s-coordinate of the trajectory
   *
   * @return a ComparableCost object representing the obstacle cost
   *
   * @throws None
   */

  ComparableCost CalculateDynamicObstacleCost(
      const solver::QuinticPolynomialCurve1d& curve, const double start_s,
      const double end_s);

  ComparableCost GetCostBetweenObsContours(
      const tport::Contour2D& ego_contour,
      const tport::Contour2D& obstacle_contour);

  double DistanceTo(const tport::Contour2D& ego_contour,
                    const tport::Contour2D& obstacle_contour);

  tport::Contour2D GetEgoFromSLPoint(const tport::SLPoint sl, const double dl);

  double Sigmoid(const double x);  // TODO: sigmoid  等待替换

  double PointToSegmentDistance(const tutil::Point& p,
                                const tutil::Point& start,
                                const tutil::Point& end);

  const DpPathOptimizerParam* config_;
  port::ReferenceLineInfo reference_line_info_;
  const port::SpeedData heuristic_speed_data_;
  tport::SLPoint init_sl_point_;
  bool is_change_lane_path_ = false;
  uint32_t num_of_time_stamps_ = 0;
  // TODO: 以下还未确定是否使用
  std::vector<port::SLBoundary> static_obstacle_sl_boundaries_;  // 静态障碍物
  std::vector<double> obstacle_probabilities_;
  std::vector<std::vector<tport::Contour2D>> dynamic_obstacle_contour_;
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
