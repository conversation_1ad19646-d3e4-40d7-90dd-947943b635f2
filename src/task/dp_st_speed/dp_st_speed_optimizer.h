// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <string>

// #include "modules/planning/proto/planning_internal.pb.h"
#include "param/dp_st_speed_param.h"
#include "param/st_graph_param.h"

// #include "modules/planning/tasks/optimizers/speed_optimizer.h"
#include <trunk/common/common.h>

#include "config.h"
#include "port/frame.h"
#include "task/st_graph/speed_limit_decider.h"
#include "task/st_graph/st_boundary_mapper.h"
#include "task/task.h"
#include "util/util.h"

namespace trunk {
namespace pnd {
namespace task {

/**
 * @class DpStSpeedOptimizer
 * @brief DpStSpeedOptimizer does ST graph speed planning with dynamic
 * programming algorithm.
 */
class DpStSpeedOptimizerTask : public Task {
 public:
  DpStSpeedOptimizerTask();

  virtual ~DpStSpeedOptimizerTask() = default;

  virtual tpnc::Status Execute(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info);

  // explicit DpStSpeedOptimizer(const TaskConfig& config);

 private:
  //  tpnc::Status Process(const SLBoundary& adc_sl_boundary,
  //                       const PathData& path_data,
  //                       const common::TrajectoryPoint& init_point,
  //                       const ReferenceLine& reference_line,
  //                       const SpeedData& reference_speed_data,
  //                       PathDecision* const path_decision,
  //                       SpeedData* const speed_data) override;

  bool SearchStGraph(const StBoundaryMapper& boundary_mapper,
                     const SpeedLimitDecider& speed_limit_decider,
                     port::ReferenceLineInfo* reference_line_info) const;

 private:
  tport::TrajectoryPoint init_point_;
  port::ReferenceLineInfo* reference_line_info_ = nullptr;
  port::SLBoundary adc_sl_boundary_;
  DpStSpeedOptimizerParam dp_st_speed_config_;
  StGraphParam st_boundary_config_;
};
}  // namespace task
}  // namespace pnd
}  // namespace trunk
