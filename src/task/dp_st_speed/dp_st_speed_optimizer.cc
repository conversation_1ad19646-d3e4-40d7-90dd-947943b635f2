// Copyright 2023, trunk Inc. All rights reserved

#include "dp_st_speed_optimizer.h"

#include <absl/time/clock.h>
#include <absl/time/time.h>

#include "dp_st_graph.h"
#include "task/st_graph/st_graph_data.h"

namespace trunk {
namespace pnd {
namespace task {

using tpnc::ErrorCode;
using tpnc::Status;

REGISTER(trunk::pnd::task, Task, DpStSpeedOptimizer);

DpStSpeedOptimizerTask::DpStSpeedOptimizerTask()
    : Task("dp st speed optimizer task") {
  dp_st_speed_config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()
          ->GetConfig<DpStSpeedOptimizerParam>("dp_st_speed_optimizer");
  st_boundary_config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()->GetConfig<StGraphParam>(
          "st_graph");
}

Status DpStSpeedOptimizerTask::Execute(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  auto start_run_time = absl::Now();
  init_point_ = frame->init_point();
  adc_sl_boundary_ = reference_line_info->AdcSlBoundary();
  reference_line_info_ = reference_line_info;
  //  for (auto obstacles : reference_line_info->obstacles().Items()) {
  //    TRUNK_LOG_WARN << "ID:" << obstacles->obj().id()
  //                   << " center:" << obstacles->obj().xy_center();
  //  }

  if (reference_line_info_->path_data().qp_norm_path().empty()) {
    std::string msg("Empty path data");
    TRUNK_LOG_ERROR << msg;
    return Status(ErrorCode::PLANNING_OTHERS, msg);
  }

  // create st_boundary_mapper
  StBoundaryMapper boundary_mapper(adc_sl_boundary_, *reference_line_info_,
                                   reference_line_info_->path_data(),
                                   dp_st_speed_config_.total_path_length(),
                                   dp_st_speed_config_.total_time());
  //
  //  auto* debug = reference_line_info_->mutable_debug();
  //  // STGraphDebug* st_graph_debug =
  //  // debug->mutable_planning_data()->add_st_graph();
  //
  // path_decision->EraseStBoundaries();

  // reset obstacle.st_boundary
  for (auto obstacle : reference_line_info_->obstacles().Items()) {
    obstacle->set_st_boundary(port::STBoundary());
  }
  // create st_boundary
  if (!boundary_mapper.CreateStBoundary()) {
    const std::string msg =
        "Mapping obstacle for dp st speed optimizer failed.";
    TRUNK_LOG_ERROR << msg;
    return Status(ErrorCode::PLANNING_OTHERS, msg);
  }
  // set speed limit
  SpeedLimitDecider speed_limit_decider(
      adc_sl_boundary_, st_boundary_config_, *reference_line_info_,
      frame->current_reference_line_info(), frame->tnp_speed_limit());
  double run_time = absl::ToDoubleSeconds(absl::Now() - start_run_time);
  TRUNK_LOG_DEBUG << "map using:" << run_time;
  start_run_time = absl::Now();
  // start dp st
  if (!SearchStGraph(boundary_mapper, speed_limit_decider,
                     reference_line_info_)) {
    const std::string msg(":Failed to search graph with dynamicprogramming.");
    reference_line_info_->set_dp_speed_data(reference_line_info->speed_data());
    return Status(ErrorCode::PLANNING_OTHERS, msg);
  }
  reference_line_info_->set_dp_speed_data(reference_line_info->speed_data());
  run_time = absl::ToDoubleSeconds(absl::Now() - start_run_time);
  TRUNK_LOG_DEBUG << "dp using:" << run_time;
  return Status::OK();
}

bool DpStSpeedOptimizerTask::SearchStGraph(
    const StBoundaryMapper& boundary_mapper,
    const SpeedLimitDecider& speed_limit_decider,
    port::ReferenceLineInfo* reference_line_info) const {
  // step1 fill boundaries
  std::vector<const port::STBoundary*> boundaries;
  for (auto* obstacle : reference_line_info->obstacles().Items()) {
    if (!obstacle->st_boundary().lower_points().empty()) {
      if (obstacle->st_boundary().boundary_type() ==
          port::STBoundary::BoundaryType::KEEP_CLEAR) {
        obstacle->set_is_blocking(false);
        TRUNK_LOG_INFO << "obj id:" << obstacle->obj().id() << "KEEP CLEAR";
      } else {
        obstacle->set_is_blocking(true);
        // TRUNK_LOG_INFO << "obj id:" << obstacle->obj().id() << "BLOCK";
      }
      // TRUNK_LOG_INFO << "obj id:" << obstacle->obj().id() << "add boundary";
      boundaries.push_back(&obstacle->st_boundary());
    }
  }

  // step 2 perform graph search
  port::SpeedLimit speed_limit;
  if (!speed_limit_decider
           .GetSpeedLimits(reference_line_info->obstacles(), &speed_limit)
           .ok()) {
    TRUNK_LOG_ERROR << "Getting speed limits for dp st speed optimizer failed!";
    return false;
  }

  const double path_length =
      reference_line_info->path_data().qp_norm_path().back().s();
  StGraphData st_graph_data(boundaries, init_point_, speed_limit, path_length);
  // TRUNK_LOG_INFO<<"path s:"<<path_length;

  DpStGraph st_graph(st_graph_data, dp_st_speed_config_,
                     reference_line_info_->obstacles(), init_point_,
                     adc_sl_boundary_);

  if (!st_graph.Search(reference_line_info->mutable_speed_data()).ok()) {
    TRUNK_LOG_ERROR << "failed to search graph with dynamic programming.";
    if (reference_line_info_ != nullptr) {
      reference_line_info_->set_planning_failure_cause(this->GetName() + ": " +
                                                       "faliure");
    }
    // RecordSTGraphDebug(st_graph_data, st_graph_debug);
    // return false;
  }
  // RecordSTGraphDebug(st_graph_data, st_graph_debug);
  return true;
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
