// Copyright 2023, trunk Inc. All rights reserved

#include "dp_st_graph.h"

#include <absl/time/clock.h>
#include <absl/time/time.h>

#include "future"
#include "param/sys_param.h"

// #include "cyber/common/log.h"

namespace trunk {
namespace pnd {
namespace task {

using port::SpeedSTPoint;
using tpnc::ErrorCode;
using tpnc::Status;

namespace {

bool CheckOverlapOnDpStGraph(
    const std::vector<const port::STBoundary*>& boundaries,
    const StGraphPoint& p1, const StGraphPoint& p2) {
  tport::Point2D pt1(p1.point().t(), p1.point().s());
  tport::Point2D pt2(p2.point().t(), p2.point().s());
  // const tutil::LineSegment seg(pt1, pt2);
  tport::Contour2D dp_line;
  dp_line.emplace_back(pt1);
  dp_line.emplace_back(pt2);
  for (const auto* boundary : boundaries) {
    if (boundary->boundary_type() ==
        port::STBoundary::BoundaryType::KEEP_CLEAR) {
      continue;
    }
    tport::Contour2D boundary_polygen;
    boundary_polygen.reserve(boundary->lower_points().size() +
                             boundary->upper_points().size());
    std::for_each(boundary->lower_points().begin(),
                  boundary->lower_points().end(),
                  [&boundary_polygen](const auto& pt) {
                    boundary_polygen.emplace_back(pt.t(), pt.s());
                  });
    std::for_each(boundary->upper_points().rbegin(),
                  boundary->upper_points().rend(),
                  [&boundary_polygen](const auto& pt) {
                    boundary_polygen.emplace_back(pt.t(), pt.s());
                  });
    if (tutil::ObstacleDetect{}.GjkCheck(dp_line, boundary_polygen)) {
      return true;
    }
  }
  return false;
}

}  // namespace

DpStGraph::DpStGraph(const StGraphData& st_graph_data,
                     const DpStSpeedOptimizerParam& dp_config,
                     const port::IndexedObstacles& obstacles,
                     const tport::TrajectoryPoint& init_point,
                     const port::SLBoundary& adc_sl_boundary)
    : st_graph_data_(st_graph_data),
      dp_st_speed_config_(dp_config),
      obstacles_(obstacles),
      init_point_(init_point),
      dp_st_cost_(dp_config, obstacles, init_point_),
      adc_sl_boundary_(adc_sl_boundary) {
  dp_st_speed_config_.set_total_path_length(
      std::fmin(dp_st_speed_config_.total_path_length(),
                st_graph_data_.path_data_length()));
  unit_s_ = dp_st_speed_config_.total_path_length() /
            (dp_st_speed_config_.matrix_dimension_s() - 1);
  unit_t_ = dp_st_speed_config_.total_time() /
            (dp_st_speed_config_.matrix_dimension_t() - 1);
  sys_config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()->GetConfig<SysParam>("sys");
}

Status DpStGraph::Search(port::SpeedData* const speed_data) {
  constexpr double kBounadryEpsilon = 1e-2;
  for (const auto& boundary : st_graph_data_.st_boundaries()) {
    if (boundary->boundary_type() ==
        port::STBoundary::BoundaryType::KEEP_CLEAR) {
      continue;
    }
    if (boundary->IsPointInBoundary(port::STPoint(0.0, 0.0)) ||
        (std::fabs(boundary->min_t()) < kBounadryEpsilon &&
         std::fabs(boundary->min_s()) < kBounadryEpsilon)) {
      std::vector<SpeedSTPoint> speed_profile;
      double t = 0.0;
      for (int i = 0; i <= dp_st_speed_config_.matrix_dimension_t();
           ++i, t += unit_t_) {
        SpeedSTPoint speed_point;
        speed_point.set_s(0.0);
        speed_point.set_t(t);
        speed_point.set_v(0.0);
        speed_point.set_a(0.0);
        speed_profile.emplace_back(speed_point);
      }
      *speed_data = port::SpeedData(speed_profile);
      return Status::OK();
    }
  }

  //  if (st_graph_data_.st_boundaries().empty()) {
  //    TRUNK_LOG_WARN
  //        << "No path obstacles, dp_st_graph output default speed profile.";
  //    std::vector<SpeedSTPoint> speed_profile;
  //    double s = 0.0;
  //    double t = 0.0;
  //    for (int i = 0; i <= dp_st_speed_config_.matrix_dimension_t() &&
  //                    i <= dp_st_speed_config_.matrix_dimension_s();
  //         ++i, t += unit_t_, s += unit_s_) {
  //      SpeedSTPoint speed_point;
  //      speed_point.set_s(s);
  //      speed_point.set_t(t);
  //      const double v_default = unit_s_ / unit_t_;
  //      speed_point.set_v(v_default);
  //      speed_point.set_a(0.0);
  //      speed_profile.emplace_back(std::move(speed_point));
  //    }
  //    *speed_data = port::SpeedData(std::move(speed_profile));
  //    TRUNK_LOG_WARN << speed_profile.size() << " dimension_t:"
  //                   << dp_st_speed_config_.matrix_dimension_t()
  //                   << " dimension_s:"
  //                   << dp_st_speed_config_.matrix_dimension_s()
  //                   << " u_t:" << unit_t_ << " u_s:" << unit_s_;
  //    return Status::OK();
  //  }

  if (!InitCostTable().ok()) {
    const std::string msg = "Initialize cost table failed.";
    TRUNK_LOG_ERROR << msg;
    *speed_data = util::SpeedProfileGenerator::GenerateStopProfile(
        init_point_.v(), dp_st_speed_config_.total_time(), max_dec_);
    return Status(ErrorCode::PLANNING_OTHERS, msg);
  }
  auto start_time = absl::Now();
  if (!CalculateTotalCost().ok()) {
    const std::string msg = "Calculate total cost failed.";
    TRUNK_LOG_ERROR << msg;
    *speed_data = util::SpeedProfileGenerator::GenerateStopProfile(
        init_point_.v(), dp_st_speed_config_.total_time(), max_dec_);
    return Status(ErrorCode::PLANNING_OTHERS, msg);
  }
  double run_time = absl::ToDoubleSeconds(absl::Now() - start_time);
  TRUNK_LOG_DEBUG << "true:" << run_time;
  //  InitCostTable();
  //  start_time = absl::Now();
  //  CalculateTotalCost();
  //  run_time = absl::ToDoubleSeconds(absl::Now() - start_time);
  //  TRUNK_LOG_ERROR << "false:" << run_time;

  if (!RetrieveSpeedProfile(speed_data).ok()) {
    const std::string msg = "Retrieve best speed profile failed.";
    TRUNK_LOG_ERROR << msg;
    *speed_data = util::SpeedProfileGenerator::GenerateStopProfile(
        init_point_.v(), dp_st_speed_config_.total_time(), max_dec_);
    // 需要传出错误信息，计算cost 代价
    return Status(ErrorCode::PLANNING_OTHERS, msg);
  }
  // for (auto sp : *speed_data) {
  //   TRUNK_LOG_ERROR << "s: " << sp.s() << ", t: " << sp.t();
  // }
  return Status::OK();
}

Status DpStGraph::InitCostTable() {
  // 初始化cost table 8列, 1列1s, 150行, 1行1m
  uint32_t dim_s = dp_st_speed_config_.matrix_dimension_s();
  uint32_t dim_t = dp_st_speed_config_.matrix_dimension_t();
  cost_table_ = std::vector<std::vector<StGraphPoint>>(
      dim_t, std::vector<StGraphPoint>(dim_s, StGraphPoint()));

  double curr_t = 0.0;
  for (uint32_t i = 0; i < cost_table_.size(); ++i, curr_t += unit_t_) {
    auto& cost_table_i = cost_table_[i];
    double curr_s = 0.0;
    for (uint32_t j = 0; j < cost_table_i.size(); ++j, curr_s += unit_s_) {
      cost_table_i[j].Init(i, j, port::STPoint(curr_s, curr_t));
    }
  }
  // Eigen::MatrixXd debug_cost_table;
  // debug_cost_table.resize(cost_table_.front().size(), cost_table_.size());
  // int i = 0;
  // for (auto col : cost_table_) {
  //   int j = 0;
  //   for (auto pt : col) {
  //     debug_cost_table(j, i) = pt.total_cost();
  //     j++;
  //   }
  //   i++;
  // }
  // TRUNK_LOG_WARN << "TABLEBLE:\n" << debug_cost_table;
  return Status::OK();
}

Status DpStGraph::CalculateTotalCost() {
  // 动态规划问题构建：花费cost_at_cr可以到达下一列(next_t),
  // 且每一列t上可以选择的行(s)有加速度限制，计算到达最后一列t时花费最小的路径
  //  col and row are for STGraph
  //  t corresponding to col
  //  s corresponding to row
  size_t next_highest_row = 0;
  size_t next_lowest_row = 0;
  for (size_t c = 0; c < cost_table_.size(); ++c) {
    size_t highest_row = 0;
    auto lowest_row = cost_table_.back().size() - 1;

    int count = static_cast<int>(next_highest_row) -
                static_cast<int>(next_lowest_row) + 1;
    if (count > 0) {
      if (sys_config_.enable_multithreading()) {
        std::vector<std::future<void>> results;
        for (size_t r = next_lowest_row; r <= next_highest_row; ++r) {
          auto msg = std::make_shared<StGraphMessage>(c, r);
          results.push_back(std::async(std::launch::async,
                                       &DpStGraph::CalculateCostAt, this, msg));
        }
        for (auto& result : results) {
          result.get();
        }
      } else {
        for (size_t r = next_lowest_row; r <= next_highest_row; ++r) {
          auto msg = std::make_shared<StGraphMessage>(c, r);
          CalculateCostAt(msg);
          TRUNK_LOG_DEBUG << "c:" << c << " r:" << r
                          << " cost:" << cost_table_[c][r].total_cost()
                          << " l:" << next_lowest_row
                          << " h:" << next_highest_row;
        }
      }
    }
    for (size_t r = next_lowest_row; r <= next_highest_row; ++r) {
      const auto& cost_cr = cost_table_[c][r];
      if (cost_cr.total_cost() < std::numeric_limits<double>::infinity()) {
        size_t h_r = 0;
        size_t l_r = 0;
        GetRowRange(cost_cr, &h_r, &l_r);
        highest_row = std::max(highest_row, h_r);
        lowest_row = std::min(lowest_row, l_r);
      }
    }
    next_highest_row = highest_row;
    next_lowest_row = lowest_row;
  }  // end of for

  // const uint32_t dim_s = dp_st_speed_config_.matrix_dimension_s();
  // const uint32_t dim_t = dp_st_speed_config_.matrix_dimension_t();
  // Eigen::Matrix<std::string, -1, -1> debug_cost_table;
  // debug_cost_table.resize(dim_s + 1, dim_t + 1);

  // double curr_t = 0.0;
  // debug_cost_table(0, 0) = "s-t";
  // for (uint32_t i = 1; i < dim_t + 1; ++i, curr_t += unit_t_) {
  //   double curr_s = 0.0;
  //   for (uint32_t j = 1; j < dim_s + 1; ++j, curr_s += unit_s_) {
  //     const auto& cost_cr = cost_table_.at(i - 1).at(j - 1).total_cost();
  //     debug_cost_table(j, i) = std::to_string(cost_cr);
  //     debug_cost_table(j, 0) = "s[" + std::to_string(curr_s) + "]";
  //   }
  //   debug_cost_table(0, i) = "t[" + std::to_string(curr_t) + "]";
  // }

  // TRUNK_LOG_WARN << "v: " << init_point_.v() << " ,TABLE:\n"
  //                << debug_cost_table;
  return Status::OK();
}

void DpStGraph::GetRowRange(const StGraphPoint& point, size_t* next_highest_row,
                            size_t* next_lowest_row) {
  // 根据初始速度和加速度限制选择采样行数(s)
  double v0 = 0.0;
  if (!point.pre_point()) {
    v0 = init_point_.v();
  } else {
    v0 = (point.index_s() - point.pre_point()->index_s()) * unit_s_ / unit_t_;
  }

  const auto max_s_size = cost_table_.back().size() - 1;

  const double speed_coeff = 1.0;  // appollo here use 2.0

  const double delta_s_upper_bound =
      v0 * unit_t_ + speed_coeff * 0.5 * max_acc_ * unit_t_ * unit_t_;
  *next_highest_row =
      point.index_s() + static_cast<int>(delta_s_upper_bound / unit_s_);
  if (*next_highest_row >= max_s_size) {
    *next_highest_row = max_s_size;
  }

  const double delta_s_lower_bound = std::fmax(
      0.0, v0 * unit_t_ + speed_coeff * 0.5 * max_dec_ * unit_t_ * unit_t_);
  *next_lowest_row =
      point.index_s() + static_cast<int>(delta_s_lower_bound / unit_s_);
  if (*next_lowest_row > max_s_size) {
    *next_lowest_row = max_s_size;
  } else if (*next_lowest_row < 0) {
    *next_lowest_row = 0;
  }
}

void DpStGraph::CalculateCostAt(const std::shared_ptr<StGraphMessage>& msg) {
  const uint32_t c = msg->c;
  const uint32_t r = msg->r;
  auto& cost_cr = cost_table_[c][r];
  cost_cr.set_obstacle_cost(dp_st_cost_.GetObstacleCost(cost_cr));
  if (cost_cr.obstacle_cost() > std::numeric_limits<double>::max()) {
    return;
  }

  const auto& cost_init = cost_table_[0][0];

  // table[0，0] cost = 0.0, 且第一列只有一个点即初始位置点 s=0
  if (c == 0) {
    cost_cr.set_total_cost(0.0);
    return;
  }

  // unit_s_ * r 代表这一行的s距离，根据加速度限制每一列的最大delta_s
  double speed_limit =
      st_graph_data_.speed_limit().GetSpeedLimitByS(unit_s_ * r);

  // 第一列采样acc限制，dp点graph重叠限制
  if (c == 1) {
    const double acc = (r * unit_s_ / unit_t_ - init_point_.v()) / unit_t_;
    if (acc < dp_st_speed_config_.max_deceleration() ||
        acc > dp_st_speed_config_.max_acceleration()) {
      TRUNK_LOG_ERROR << "acc too large:" << acc;
      return;
    }

    if (CheckOverlapOnDpStGraph(st_graph_data_.st_boundaries(), cost_cr,
                                cost_init)) {
      TRUNK_LOG_INFO << "over lap! \npt1:" << cost_cr.point().t() << " "
                     << cost_cr.point().s() << "\npt2:" << cost_init.point().t()
                     << " " << cost_init.point().s();
      return;
    }
    cost_cr.set_total_cost(cost_cr.obstacle_cost() + cost_init.total_cost() +
                           CalculateEdgeCostForSecondCol(r, speed_limit));
    // 第一行设为初始点
    cost_cr.SetPrePoint(cost_init);
    //    TRUNK_LOG_INFO << "first dp:" << cost_cr.total_cost() << " r:" << r
    //                   << " init_cost:" << cost_init.total_cost();
    return;
  }

  constexpr double kSpeedRangeBuffer = 0.20;
  const uint32_t max_s_diff =
      static_cast<uint32_t>(tutil::Kmh2Ms(sys_config_.config_speed_limit()) *
                            (1 + kSpeedRangeBuffer) * unit_t_ / unit_s_);
  const uint32_t r_low = (max_s_diff < r ? r - max_s_diff : 0);

  const auto& pre_col = cost_table_[c - 1];

  if (c == 2) {
    for (uint32_t r_pre = r_low; r_pre <= r; ++r_pre) {
      const double acc =
          (r * unit_s_ - 2 * r_pre * unit_s_) / (unit_t_ * unit_t_);
      if (acc < dp_st_speed_config_.max_deceleration() ||
          acc > dp_st_speed_config_.max_acceleration()) {
        // TRUNK_LOG_ERROR << "acc too large:" << acc;
        continue;
      }

      if (CheckOverlapOnDpStGraph(st_graph_data_.st_boundaries(), cost_cr,
                                  pre_col[r_pre])) {
        TRUNK_LOG_ERROR << "over lap! \npt1:" << cost_cr.point().t() << " "
                        << cost_cr.point().s()
                        << "\npt2:" << pre_col[r_pre].point().t() << " "
                        << pre_col[r_pre].point().s();
        continue;
      }

      const double cost = cost_cr.obstacle_cost() +
                          pre_col[r_pre].total_cost() +
                          CalculateEdgeCostForThirdCol(r, r_pre, speed_limit);
      //      TRUNK_LOG_ERROR << "1:" << cost_cr.obstacle_cost()
      //                      << " 2:" << pre_col[r_pre].total_cost() << " 3:"
      //                      << CalculateEdgeCostForThirdCol(r, r_pre,
      //                      speed_limit);

      if (cost < cost_cr.total_cost()) {
        cost_cr.set_total_cost(cost);
        cost_cr.SetPrePoint(pre_col[r_pre]);
      }
    }
    return;
  }
  for (uint32_t r_pre = r_low; r_pre <= r; ++r_pre) {
    if (std::isinf(pre_col[r_pre].total_cost()) ||
        pre_col[r_pre].pre_point() == nullptr) {
      continue;
    }

    const double curr_a = (cost_cr.index_s() * unit_s_ +
                           pre_col[r_pre].pre_point()->index_s() * unit_s_ -
                           2 * pre_col[r_pre].index_s() * unit_s_) /
                          (unit_t_ * unit_t_);
    if (curr_a > max_acc_ || curr_a < max_dec_) {
      continue;
    }
    if (CheckOverlapOnDpStGraph(st_graph_data_.st_boundaries(), cost_cr,
                                pre_col[r_pre])) {
      // TRUNK_LOG_ERROR << "over lap! \npt1:" << cost_cr.point().t() << " "
      //                 << cost_cr.point().s()
      //                 << "\npt2:" << pre_col[r_pre].point().t() << " "
      //                 << pre_col[r_pre].point().s();
      continue;
    }

    uint32_t r_prepre = pre_col[r_pre].pre_point()->index_s();
    const StGraphPoint& prepre_graph_point = cost_table_[c - 2][r_prepre];
    if (std::isinf(prepre_graph_point.total_cost())) {
      continue;
    }

    if (!prepre_graph_point.pre_point()) {
      continue;
    }
    const port::STPoint& triple_pre_point =
        prepre_graph_point.pre_point()->point();
    const port::STPoint& prepre_point = prepre_graph_point.point();
    const port::STPoint& pre_point = pre_col[r_pre].point();
    const port::STPoint& curr_point = cost_cr.point();
    double cost = cost_cr.obstacle_cost() + pre_col[r_pre].total_cost() +
                  CalculateEdgeCost(triple_pre_point, prepre_point, pre_point,
                                    curr_point, speed_limit);

    if (cost < cost_cr.total_cost()) {
      cost_cr.set_total_cost(cost);
      cost_cr.SetPrePoint(pre_col[r_pre]);
    }
  }
}

Status DpStGraph::RetrieveSpeedProfile(port::SpeedData* const speed_data) {
  double min_cost = std::numeric_limits<double>::infinity();
  const StGraphPoint* best_end_point = nullptr;
  for (const StGraphPoint& cur_point : cost_table_.back()) {
    if (!std::isinf(cur_point.total_cost()) &&
        cur_point.total_cost() < min_cost) {
      best_end_point = &cur_point;
      min_cost = cur_point.total_cost();
    }
  }

  // for (const auto& row : cost_table_) {
  //   const StGraphPoint& cur_point = row.back();
  //   if (!std::isinf(cur_point.total_cost()) &&
  //       cur_point.total_cost() < min_cost) {
  //     best_end_point = &cur_point;
  //     min_cost = cur_point.total_cost();
  //   }
  // }

  if (best_end_point == nullptr) {
    const std::string msg = "Fail to find the best feasible trajectory.";
    TRUNK_LOG_ERROR << msg << min_cost;
    // for (auto bound : st_graph_data_.st_boundaries()) {
    // for (auto pt : bound->lower_points()) {
    // TRUNK_LOG_INFO << "l bound:" << pt.t() << " " << pt.s();
    // }
    // for (auto pt : bound->upper_points()) {
    // TRUNK_LOG_INFO << "u bound:" << pt.t() << " " << pt.s();
    // }
    // }
    return Status(ErrorCode::PLANNING_OTHERS, msg);
  }

  std::vector<port::SpeedSTPoint> speed_profile;
  const StGraphPoint* cur_point = best_end_point;
  while (cur_point != nullptr) {
    SpeedSTPoint speed_point;
    speed_point.set_s(cur_point->point().s());
    speed_point.set_t(cur_point->point().t());
    speed_profile.emplace_back(speed_point);
    cur_point = cur_point->pre_point();
  }
  std::reverse(speed_profile.begin(), speed_profile.end());

  constexpr double kEpsilon = std::numeric_limits<double>::epsilon();
  if (speed_profile.front().t() > kEpsilon ||
      speed_profile.front().s() > kEpsilon) {
    const std::string msg = "Fail to retrieve speed profile.";
    TRUNK_LOG_ERROR << msg;
    return Status(ErrorCode::PLANNING_OTHERS, msg);
  }

  for (size_t i = 0; i + 1 < speed_profile.size(); ++i) {
    const double v = (speed_profile[i + 1].s() - speed_profile[i].s()) /
                     (speed_profile[i + 1].t() - speed_profile[i].t() + 1e-3);
    speed_profile[i].set_v(v);
  }

  // for (const auto& pt : speed_profile) {
  // TRUNK_LOG_INFO << "s: " << pt.s() << " t: " << pt.t() << " v: " << pt.v();
  // }

  *speed_data = port::SpeedData(speed_profile);
  return Status::OK();
}

double DpStGraph::CalculateEdgeCost(const port::STPoint& first,
                                    const port::STPoint& second,
                                    const port::STPoint& third,
                                    const port::STPoint& forth,
                                    const double speed_limit) {
  return dp_st_cost_.GetSpeedCost(third, forth, speed_limit) +
         dp_st_cost_.GetAccelCostByThreePoints(second, third, forth) +
         dp_st_cost_.GetJerkCostByFourPoints(first, second, third, forth);
}

double DpStGraph::CalculateEdgeCostForSecondCol(const uint32_t row,
                                                const double speed_limit) {
  double init_speed = init_point_.v();
  double init_acc = init_point_.a();
  const port::STPoint& pre_point = cost_table_[0][0].point();
  const port::STPoint& curr_point = cost_table_[1][row].point();
  return dp_st_cost_.GetSpeedCost(pre_point, curr_point, speed_limit) +
         dp_st_cost_.GetAccelCostByTwoPoints(init_speed, pre_point,
                                             curr_point) +
         dp_st_cost_.GetJerkCostByTwoPoints(init_speed, init_acc, pre_point,
                                            curr_point);
}

double DpStGraph::CalculateEdgeCostForThirdCol(const uint32_t curr_row,
                                               const uint32_t pre_row,
                                               const double speed_limit) {
  double init_speed = init_point_.v();
  const port::STPoint& first = cost_table_[0][0].point();
  const port::STPoint& second = cost_table_[1][pre_row].point();
  const port::STPoint& third = cost_table_[2][curr_row].point();
  return dp_st_cost_.GetSpeedCost(second, third, speed_limit) +
         dp_st_cost_.GetAccelCostByThreePoints(first, second, third) +
         dp_st_cost_.GetJerkCostByThreePoints(init_speed, first, second, third);
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
