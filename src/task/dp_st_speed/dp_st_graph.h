// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/common.h>
//#include "modules/common/configs/proto/vehicle_config.pb.h"
//#include "modules/planning/proto/dp_st_speed_config.pb.h"
//#include "modules/planning/proto/planning_config.pb.h"
#include "dp_st_cost.h"
#include "port/frame.h"
#include "port/st/speed_data.h"
#include "port/st/st_graph_point.h"
#include "task/st_graph/st_graph_data.h"
#include "util/speed_profile_generator.h"
#include "util/util.h"

//#include "modules/common/configs/vehicle_config_helper.h"

namespace trunk {
namespace pnd {
namespace task {

class DpStGraph {
 public:
  DpStGraph(const StGraphData& st_graph_data,
            const DpStSpeedOptimizerParam& dp_config,
            const port::IndexedObstacles& obstacles,
            const tport::TrajectoryPoint& init_point,
            const port::SLBoundary& adc_sl_boundary);

  tpnc::Status Search(port::SpeedData* const speed_data);

 private:
  tpnc::Status InitCostTable();

  tpnc::Status RetrieveSpeedProfile(port::SpeedData* const speed_data);

  tpnc::Status CalculateTotalCost();

  // defined for cyber task
  struct StGraphMessage {
    StGraphMessage(const uint32_t c_, const int32_t r_) : c(c_), r(r_) {}

    uint32_t c;
    uint32_t r;
  };

  void CalculateCostAt(const std::shared_ptr<StGraphMessage>& msg);

  double CalculateEdgeCost(const port::STPoint& first,
                           const port::STPoint& second,
                           const port::STPoint& third,
                           const port::STPoint& forth,
                           const double speed_limit);

  double CalculateEdgeCostForSecondCol(const uint32_t row,
                                       const double speed_limit);

  double CalculateEdgeCostForThirdCol(const uint32_t curr_r,
                                      const uint32_t pre_r,
                                      const double speed_limit);

  void GetRowRange(const StGraphPoint& point, size_t* highest_row,
                   size_t* lowest_row);

 private:
  const StGraphData& st_graph_data_;

  // dp st configuration
  DpStSpeedOptimizerParam dp_st_speed_config_;

  SysParam sys_config_;

  // obstacles based on the current reference line
  const port::IndexedObstacles& obstacles_;

  // 车辆参数
  double max_acc_ = 1.0;
  double max_dec_ = -3.0;

  // initial status
  tport::TrajectoryPoint init_point_;

  // cost utility with configuration;
  DpStCost dp_st_cost_;

  const port::SLBoundary& adc_sl_boundary_;

  double unit_s_ = 0.0;
  double unit_t_ = 0.0;
  bool load_ = false;
  bool load2_ = false;

  // cost_table_[t][s]
  // row: s, col: t --- NOTICE: Please do NOT change.
  std::vector<std::vector<StGraphPoint>> cost_table_;

};
}  // namespace task
}  // namespace pnd
}  // namespace trunk
