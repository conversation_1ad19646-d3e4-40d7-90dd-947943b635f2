// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <param/dp_st_speed_param.h>
#include <trunk/common/common.h>

#include "port/frame.h"
#include "port/st/st_graph_point.h"
#include "util/util.h"

namespace trunk {
namespace pnd {
namespace task {

class DpStCost {
 public:
  explicit DpStCost(const DpStSpeedOptimizerParam& config_,
                    const port::IndexedObstacles& obstacles,
                    const tport::TrajectoryPoint& init_point);

  double GetObstacleCost(const StGraphPoint& point);

  double GetReferenceCost(const port::STPoint& point,
                          const port::STPoint& reference_point) const;

  double GetSpeedCost(const port::STPoint& first, const port::STPoint& second,
                      const double speed_limit) const;

  double GetAccelCostByTwoPoints(const double pre_speed,
                                 const port::STPoint& first,
                                 const port::STPoint& second);

  double GetAccelCostByThreePoints(const port::STPoint& first,
                                   const port::STPoint& second,
                                   const port::STPoint& third);

  double GetJerkCostByTwoPoints(const double pre_speed, const double pre_acc,
                                const port::STPoint& pre_point,
                                const port::STPoint& curr_point);

  double GetJerkCostByThreePoints(const double first_speed,
                                  const port::STPoint& first_point,
                                  const port::STPoint& second_point,
                                  const port::STPoint& third_point);

  double GetJerkCostByFourPoints(const port::STPoint& first,
                                 const port::STPoint& second,
                                 const port::STPoint& third,
                                 const port::STPoint& fourth);

 private:
  double GetAccelCost(const double accel);

  double JerkCost(const double jerk);

  void AddToKeepClearRange(const port::IndexedObstacles& obstacles);

  static void SortAndMergeRange(
      std::vector<std::pair<double, double>>* keep_clear_range_);

  bool InKeepClearRange(double s) const;

  const DpStSpeedOptimizerParam& config_;
  const port::IndexedObstacles& obstacles_;
  const tport::TrajectoryPoint& init_point_;

  double unit_t_ = 0.0;

  std::unordered_map<int, int> boundary_map_;

  // first: s上界, second: s下界
  std::vector<std::vector<std::pair<double, double>>> boundary_cost_;

  std::vector<std::pair<double, double>> keep_clear_range_;

  std::array<double, 200> accel_cost_;
  std::array<double, 400> jerk_cost_;
};
}  // namespace task
}  // namespace pnd
}  // namespace trunk
