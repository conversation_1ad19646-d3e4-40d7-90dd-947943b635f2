// Copyright 2024, trunk Inc. All rights reserved
#include "tsl_trajectory_generate_task.h"

#include "math/curve/quartic_polynomial.h"
#include "math/curve/quintic_polynomial.h"
#include "port/tsl/tsl_point.h"
#include "trunk/common/util/tools/finder.h"

namespace trunk {
namespace pnd {
namespace task {

REGISTER(trunk::pnd::task, Task, TslTrajectoryGenerate);

TslTrajectoryGenerateTask::TslTrajectoryGenerateTask()
    : Task("tsl trajectory generate task") {}
tpnc::Status TslTrajectoryGenerateTask::Execute(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  port::TslTrajectoryPoint init_tsl;
  const auto inter_p = tutil::FindInterpPointByDistance(
      reference_line_info->reference_line(),
      reference_line_info->sl_init_point().s());
  const double delta_theta = frame->init_point().theta() - inter_p.theta();
  init_tsl.set_s(reference_line_info->sl_init_point().s());
  init_tsl.set_v(frame->init_point().v());
  init_tsl.set_a(frame->init_point().a());
  init_tsl.set_l(reference_line_info->sl_init_point().l());
  init_tsl.set_dl(reference_line_info->sl_init_point().dl() * init_tsl.v());
  init_tsl.set_ddl(reference_line_info->sl_init_point().dl() * init_tsl.a() +
                   std::pow(init_tsl.v(), 2) *
                       reference_line_info->sl_init_point().ddl());
  // init_tsl.set_lat_v(frame->init_point().v() * std::sin(delta_theta));
  // init_tsl.set_lat_a(frame->init_point().a() * std::sin(delta_theta));
  if (frame->replan_flag() || std::abs(frame->init_point().kappa()) > 0.01 &&
                                  std::abs(init_tsl.l()) < 0.1) {
    init_tsl.set_dl(0.0);
    init_tsl.set_ddl(0.0);
  }
  const double target_speed =
      reference_line_info->GetInfoByS(reference_line_info->sl_init_point().s())
          ->speed_limit;
  const double step_v = target_speed / double(config_.v_sample_num() - 1);

  auto& tsl_trajectorys = *reference_line_info->mutable_tsl_trajectorys();
  tsl_trajectorys.reserve(
      (size_t((config_.max_t() - config_.min_t()) / config_.step_t()) + 1) *
      config_.v_sample_num() *
      (size_t((config_.max_l() - config_.min_l()) / config_.step_l()) + 1));
  for (double t_end = config_.min_t(); t_end <= config_.max_t();
       t_end += config_.step_t()) {
    std::vector<math::QuarticPolynomial> tss;
    std::vector<math::QuinticPolynomial> tls;
    tss.reserve(config_.v_sample_num());
    tls.reserve(size_t(config_.max_l() - config_.min_l()) + 1);
    for (double v = 0.0; v <= target_speed; v += step_v) {
      tss.emplace_back(t_end, init_tsl.s(), init_tsl.v(), init_tsl.a(), v, 0.0);
    }
    for (double l = config_.min_l(); l <= config_.max_l();
         l += config_.step_l()) {
      tls.emplace_back(t_end, init_tsl.l(), init_tsl.dl(), init_tsl.ddl(), l,
                       0.0, 0.0);
    }

    for (const auto& tl : tls) {
      for (const auto& ts : tss) {
        tsl_trajectorys.emplace_back();
        auto& tsl_trajectory = tsl_trajectorys.back();
        auto& trajectory = *tsl_trajectory.mutable_trajectory();
        tsl_trajectory.reserve(size_t(t_end / config_.dt()) + 1);
        trajectory.reserve(size_t(t_end / config_.dt()) + 1);
        double lon_v = 0.0;
        double lon_a = 0.0;
        double lon_j = 0.0;
        double lat_v = 0.0;
        double lat_a = 0.0;
        double lat_j = 0.0;
        tport::Path ref_path;
        for (double t = 0.0; t <= t_end; t += config_.dt()) {
          tsl_trajectory.emplace_back(t, ts.Evaluate(0, t), ts.Evaluate(1, t),
                                      ts.Evaluate(2, t), ts.Evaluate(3, t),
                                      tl.Evaluate(0, t), tl.Evaluate(1, t),
                                      tl.Evaluate(2, t), tl.Evaluate(3, t));
          auto& p = tsl_trajectory.back();
          ref_path.push_back(reference_line_info->EvaluateByS(p.s()));
          lon_v += std::abs(p.v() - target_speed);
          lon_a += std::abs(p.a());
          lon_j += std::abs(p.da());
          lat_v += std::abs(p.dl());
          lat_a += std::abs(p.ddl());
          lat_j += std::abs(p.dddl());
        }
        tsl_trajectory.ToTrajectory(ref_path);
        tsl_trajectory.set_cost_lon_velocity(config_.dt() * lon_v / t_end);
        tsl_trajectory.set_cost_lon_acceleration(config_.dt() * lon_a / t_end);
        tsl_trajectory.set_cost_lon_jerk(config_.dt() * lon_j / t_end);
        tsl_trajectory.set_cost_lat_terminal(
            std::abs(tsl_trajectory.back().l()));
        tsl_trajectory.set_cost_lat_velocity(config_.dt() * lat_v / t_end);
        tsl_trajectory.set_cost_lat_acceleration(config_.dt() * lat_a / t_end);
        tsl_trajectory.set_cost_lat_jerk(config_.dt() * lat_j / t_end);
        tsl_trajectory.set_cost_obstacle_front_distance(config_.dt() / t_end);
        tsl_trajectory.set_cost_obstacle_back_distance(config_.dt() / t_end);
        tsl_trajectory.set_cost_obstacle_lateral_distance(config_.dt() / t_end);
        tsl_trajectory.set_cost_obstacle_oblique_distance(config_.dt() / t_end);
        tsl_trajectory.set_cost_obstacle_follow(config_.dt() / t_end);
      }
    }
  }

  return tpnc::Status::OK();
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
