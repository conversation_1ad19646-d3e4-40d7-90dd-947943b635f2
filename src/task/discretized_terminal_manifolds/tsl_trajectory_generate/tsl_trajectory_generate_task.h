// Copyright 2024, trunk Inc. All rights reserved
#pragma once

#include <trunk/common/config.h>

#include "param/tsl_trajectory_generate_task_param.h"
#include "task/task.h"

namespace trunk {
namespace pnd {
namespace task {

class TslTrajectoryGenerateTask : public Task {
 public:
  TslTrajectoryGenerateTask();
  virtual ~TslTrajectoryGenerateTask() = default;
  virtual tpnc::Status Execute(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info);

 private:
  const TslTrajectoryGenerateTaskParam& config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()
          ->GetConfig<TslTrajectoryGenerateTaskParam>(
              "tsl_trajectory_generate_task");
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
