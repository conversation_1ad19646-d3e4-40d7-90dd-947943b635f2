// Copyright 2024, trunk Inc. All rights reserved
#include "tsl_trajectory_check_task.h"

#include "log.h"
#include "port/sl/sl_boundary.h"
#include "port/tsl/tsl_point.h"
#include "trunk/common/util/tools/tools.h"
#include "util/key.h"

namespace trunk {
namespace pnd {
namespace task {

REGISTER(trunk::pnd::task, Task, TslTrajectoryCheck);

TslTrajectoryCheckTask::TslTrajectoryCheckTask()
    : Task("tsl trajectory check task") {}
tpnc::Status TslTrajectoryCheckTask::Execute(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  const port::SLBoundary ego_boundary(
      -util::truck_.base2tail(), util::truck_.base2front(),
      -util::truck_.half_expand_width(), util::truck_.half_expand_width());
  for (auto& traj : *reference_line_info->mutable_tsl_trajectorys()) {
    // lateral check
    if (any_of(traj.begin(), traj.end(),
               [reference_line_info](const port::TslPoint& p) {
                 auto center_with =
                     reference_line_info->GetEgoDrivableWidth(p.s());
                 return (p.l() > center_with.left_width ||
                         p.l() < center_with.right_width);
               })) {
      TRUNK_LOG_DEBUG << "invalid lateral";
      traj.set_invalid(true);
      continue;
    }
    // curvature rate of change check
    // if (any_of(traj.trajectory().begin(), traj.trajectory().end(),
    //            [this](const tport::TrajectoryPoint& p) {
    //              return std::abs(p.dkappa()) * p.v() *
    //                         util::model_.geometry().wheelbase() >
    //                     config_.max_steering_angular_velocity();
    //            })) {
    //   TRUNK_LOG_DEBUG << "invalid dkappa";
    //   traj.set_invalid(true);
    //   continue;
    // }
    // curvature check
    // if (any_of(traj.trajectory().begin(), traj.trajectory().end(),
    //            [](const tport::TrajectoryPoint& p) {
    //              return std::abs(p.kappa()) > util::GetMaxKappa(p.v());
    //            })) {
    //   TRUNK_LOG_DEBUG << "invalid kappa";
    //   traj.set_invalid(true);
    //   continue;
    // }
    // speed check
    if (any_of(traj.trajectory().begin(), traj.trajectory().end(),
               [reference_line_info](const tport::TrajectoryPoint& p) {
                 // FIX: 超速时要规划，所以要加松弛因子
                 return p.v() < 0.0 ||
                        p.v() > reference_line_info->GetSpeedLimitFromS(p.s()) *
                                    1.2;
               })) {
      TRUNK_LOG_DEBUG << "invalid speed";
      traj.set_invalid(true);
      continue;
    }
    // acceleration check
    if (any_of(traj.trajectory().begin(), traj.trajectory().end(),
               [this](const tport::TrajectoryPoint& p) {
                 return p.a() > config_.max_acceleration() ||
                        p.a() < config_.min_acceleration();
               })) {
      TRUNK_LOG_DEBUG << "invalid acc";
      traj.set_invalid(true);
      continue;
    }
    // jerk check
    if (any_of(traj.trajectory().begin(), traj.trajectory().end(),
               [this](const tport::TrajectoryPoint& p) {
                 return p.da() > config_.max_jerk() ||
                        p.da() < config_.min_jerk();
               })) {
      TRUNK_LOG_DEBUG << "invalid jerk";
      traj.set_invalid(true);
      continue;
    }

    // collision and follow check
    // TODO: 节省时间，高速场景用SLBoundary的碰撞检测
    double cost_front_dis = 0.0;
    double cost_back_dis = 0.0;
    double cost_lat_dis = 0.0;
    double cost_obli_dis = 0.0;
    double cost_follow_dis = 0.0;
    double cost_nudge = 0.0;
    for (auto& p : traj) {
      auto cur_boundary = ego_boundary + p;
      for (auto& obs : reference_line_info->obstacles().Items()) {
        if (obs->sl_boundary().min_s() < util::truck_.base2front()) {
          continue;
        }
        const auto obs_boundary = obs->tsl_boundarys().EvaluateByT(p.t());
        if (cur_boundary.CollisionCheck(obs_boundary)) {
          TRUNK_LOG_DEBUG << "invalid obs, id: " << obs->obj().id();
          traj.set_invalid(true);
          break;
        }
        const double lon_dis = cur_boundary.CalcLonDistence(obs_boundary);
        const double lat_dis = cur_boundary.CalcLatDistence(obs_boundary);
        cost_obli_dis += 1.0 / std::hypot(lon_dis, lat_dis);
        if (lon_dis == 0.0) {
          cost_lat_dis = 1.0 / lat_dis;
        }
        if (lat_dis == 0.0) {
          if (cur_boundary.max_s() <= obs_boundary.min_s()) {
            cost_front_dis += 1.0 / lon_dis;
            cost_follow_dis += std::max(
                0.0, (1 - tutil::clamp(
                              obs->obj().velocity() - frame->init_point().v(),
                              0.0, config_.max_follow_speed_diff()) /
                              config_.max_follow_speed_diff()) *
                             RSSDistance(frame->init_point().v(),
                                         obs->obj().velocity()) +
                         config_.yield_distane() - lon_dis);
          } else {
            cost_back_dis += 1.0 / lon_dis;
          }
        } else {
          cost_nudge = std::max(cost_nudge, config_.max_nudge_distance() -
                                                lat_dis * std::exp(lon_dis));
        }
      }
      if (traj.invalid()) {
        break;
      }
    }
    traj.set_cost_obstacle_front_distance(traj.cost_obstacle_front_distance() *
                                          cost_front_dis);
    traj.set_cost_obstacle_back_distance(traj.cost_obstacle_back_distance() *
                                         cost_back_dis);
    traj.set_cost_obstacle_lateral_distance(
        traj.cost_obstacle_lateral_distance() * cost_lat_dis);
    traj.set_cost_obstacle_oblique_distance(
        traj.cost_obstacle_oblique_distance() * cost_obli_dis);
    traj.set_cost_obstacle_follow(traj.cost_obstacle_follow() *
                                  cost_follow_dis);
    traj.set_cost_obstacle_nudge(cost_nudge);
  }
  return tpnc::Status::OK();
}

double TslTrajectoryCheckTask::RSSDistance(const double cur_v,
                                           const double v_front) const {
  const double ego_base2front =
      tpnc::Singleton<tmodel::Truck>::GetInstance()->geometry().base2front();
  return std::max(
      0.0, cur_v * config_.rss_action_delay() +
               0.5 * config_.max_acceleration() *
                   std::pow(config_.rss_action_delay(), 2) +
               std::pow(cur_v + config_.rss_action_delay() *
                                    config_.max_acceleration(),
                        2) /
                   2.0 / std::fabs(config_.rss_ego_max_dec()) -
               v_front * v_front / 2.0 / std::fabs(config_.rss_obs_max_dec()));
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
