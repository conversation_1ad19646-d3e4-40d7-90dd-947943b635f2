// Copyright 2024, trunk Inc. All rights reserved
#include "tsl_trajectory_select_task.h"

#include <queue>
#include <vector>

#include "log.h"
#include "port/tsl/tsl_trajectory.h"

namespace trunk {
namespace pnd {
namespace task {

REGISTER(trunk::pnd::task, Task, TslTrajectorySelect);

TslTrajectorySelectTask::TslTrajectorySelectTask()
    : Task("tsl trajectory select task") {}
tpnc::Status TslTrajectorySelectTask::Execute(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  struct cmp {
    bool operator()(const port::TslTrajectory* t1,
                    const port::TslTrajectory* t2) {
      return t1->total_cost() > t2->total_cost();
    }
  };
  std::priority_queue<port::TslTrajectory*, std::vector<port::TslTrajectory*>,
                      cmp>
      pq;
  int center_num = 0;
  for (auto& traj : *reference_line_info->mutable_tsl_trajectorys()) {
    if (traj.invalid()) {
      continue;
    }
    traj.set_lon_cost(config_.weight_lon_velocity() * traj.cost_lon_velocity() +
                      config_.weight_lon_acceleration() *
                          traj.cost_lon_acceleration() +
                      config_.weight_lon_jerk() * traj.cost_lon_jerk());
    traj.set_lat_cost(config_.weight_lat_terminal() * traj.cost_lat_terminal() +
                      config_.weight_lat_velocity() * traj.cost_lat_velocity() +
                      config_.weight_lat_acceleration() *
                          traj.cost_lat_acceleration() +
                      config_.weight_lat_jerk() * traj.cost_lat_jerk());
    traj.set_obstacle_cost(config_.weight_obstacle_front_distance() *
                               traj.cost_obstacle_front_distance() +
                           config_.weight_obstacle_back_distance() *
                               traj.cost_obstacle_back_distance() +
                           config_.weight_obstacle_lateral_distance() *
                               traj.cost_obstacle_lateral_distance() +
                           config_.weight_obstacle_oblique_distance() *
                               traj.cost_obstacle_oblique_distance());
    traj.set_follow_cost(traj.cost_obstacle_follow());
    traj.set_nudge_cost(traj.cost_obstacle_nudge());
    traj.set_total_cost(config_.weight_lat() * traj.lat_cost() +
                        config_.weight_lon() * traj.lon_cost() +
                        config_.weight_obstacle() * traj.obstacle_cost() +
                        config_.weight_follow() * traj.follow_cost() +
                        config_.weight_nudge() * traj.nudge_cost());
    if (std::abs(traj.back().l()) < 0.1) {
      center_num++;
    }
    pq.push(&traj);
  }
  if (pq.empty()) {
    reference_line_info->set_is_drivable({false, "all trajectory is invalid"});
    return tpnc::Status::ERROR("all trajectory is invalid");
  } else {
    reference_line_info->set_optimal_tsl_trajctory(pq.top());
    reference_line_info->set_trajectory(pq.top()->trajectory());
    TRUNK_LOG_INFO << "total trajectory num: "
                   << reference_line_info->tsl_trajectorys().size()
                   << ", valid trajectory num: " << pq.size()
                   << ", center trajectory num: " << center_num;
    for (int i = 0; i < 1 && pq.size() > 0; ++i) {
      auto* traj = pq.top();
      TRUNK_LOG_INFO << "end state, t_end" << traj->back().t()
                     << "l_end: " << traj->back().l()
                     << "v_end: " << traj->back().v();
      // TRUNK_LOG_INFO << traj->cost_lon_velocity();
      // TRUNK_LOG_INFO << traj->cost_lon_acceleration();
      // TRUNK_LOG_INFO << traj->cost_lon_jerk();
      // TRUNK_LOG_INFO << traj->cost_lat_terminal();
      // TRUNK_LOG_INFO << traj->cost_lat_velocity();
      // TRUNK_LOG_INFO << traj->cost_lat_acceleration();
      // TRUNK_LOG_INFO << traj->cost_lat_jerk();
      // TRUNK_LOG_INFO << traj->cost_obstacle_lateral_distance();
      // TRUNK_LOG_INFO << traj->cost_obstacle_oblique_distance();
      // TRUNK_LOG_INFO << traj->cost_obstacle_front_distance();
      // TRUNK_LOG_INFO << traj->cost_obstacle_back_distance();
      // TRUNK_LOG_INFO << traj->cost_obstacle_follow();

      TRUNK_LOG_INFO << traj->lat_cost();
      TRUNK_LOG_INFO << traj->lon_cost();
      TRUNK_LOG_INFO << traj->obstacle_cost();
      TRUNK_LOG_INFO << traj->follow_cost();
      TRUNK_LOG_INFO << traj->nudge_cost();
      pq.pop();
    }
  }

  return tpnc::Status::OK();
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
