// Copyright 2024, trunk Inc. All rights reserved
#pragma once

#include <trunk/common/config.h>

#include "param/tsl_trajectory_select_task_param.h"
#include "task/task.h"

namespace trunk {
namespace pnd {
namespace task {

class TslTrajectorySelectTask : public Task {
 public:
  TslTrajectorySelectTask();
  virtual ~TslTrajectorySelectTask() = default;
  virtual tpnc::Status Execute(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info);

 private:
  double RSSDistance(const double cur_v, const double v_front) const;

 private:
  const TslTrajectorySelectTaskParam& config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()
          ->GetConfig<TslTrajectorySelectTaskParam>(
              "tsl_trajectory_select_task");
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
