// Copyright 2024, trunk Inc. All rights reserved
#pragma once

#include <trunk/common/config.h>

#include <memory>

#include "param/dp_tsl_interver_lattice_task_param.h"
#include "port/macro.h"
#include "port/tsl/tsl_point.h"
#include "task/task.h"

namespace trunk {
namespace pnd {
namespace task {

class Node : public port::TslTrajectoryPoint {
 public:
  Node() = default;
  Node(const port::TslTrajectoryPoint& tsl) : port::TslTrajectoryPoint(tsl){};
  Node(const port::TslTrajectoryPoint&& tsl) : port::TslTrajectoryPoint(tsl){};
  Node(const double ds, const port::TslTrajectoryPoint&& tsl,
       const Node* last = nullptr)
      : ds_(ds), port::TslTrajectoryPoint(tsl), last_(last){};
  Node(const double ds, const port::TslTrajectoryPoint& tsl,
       const Node* last = nullptr)
      : ds_(ds), port::TslTrajectoryPoint(tsl), last_(last){};

  Node Update(const double dt, const tport::SLPoint& u) const {
    return Update(dt, u.s(), u.l());
  }

  Node Update(const double dt, const double jerk, const double dddl) const {
    const double ds = v() * dt;
    return {ds,
            port::TslTrajectoryPoint(
                t() + dt, s() + ds, v() + a() * dt, a() + jerk * dt, jerk,
                l() + dl() * ds, dl() + ddl() * ds, ddl() + dddl * ds, dddl),
            this};
  }

 private:
  MEMBER_PTR_TYPE(const Node*, last, nullptr);
  MEMBER_BASIC_TYPE(double, ds, 0.0);
  MEMBER_BASIC_TYPE(double, cost, 0.0);
  MEMBER_BASIC_TYPE(double, self_cost, 0.0);
};
class DpTslIntervalLatticeTask : public Task {
 public:
  DpTslIntervalLatticeTask();
  virtual ~DpTslIntervalLatticeTask() = default;
  virtual tpnc::Status Execute(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info);

 private:
  double RssDistance(const double cur_v, const double v_front) const;

 private:
  const DpTslIntervalLatticeTaskParam& config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()
          ->GetConfig<DpTslIntervalLatticeTaskParam>(
              "dp_tsl_interval_lattice_task");
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
