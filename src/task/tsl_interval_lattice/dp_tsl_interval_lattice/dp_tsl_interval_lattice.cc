// Copyright 2024, trunk Inc. All rights reserved
#include "dp_tsl_interval_lattice.h"

#include <jian/spdlog/fmt/bundled/chrono.h>

#include <cstdlib>
#include <ctime>
#include <iterator>
#include <limits>
#include <random>
#include <vector>

#include "log.h"
#include "planning.h"
#include "port/tsl/tsl_boundary.h"
#include "trunk/common/port/plan_point.h"
#include "trunk/common/util/tools/tools.h"
#include "util/key.h"
#include "util/util.h"

namespace trunk {
namespace pnd {
namespace task {

REGISTER(trunk::pnd::task, Task, DpTslIntervalLattice);

DpTslIntervalLatticeTask::DpTslIntervalLatticeTask()
    : Task("dp tsl interval lattice") {}

tpnc::Status DpTslIntervalLatticeTask::Execute(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  // t_step为左开右闭，即从t_start+t_step开始
  // 其余变量为左闭区右开。
  //
  //
  const port::SLBoundary ego_boundary(
      -util::truck_.base2tail(), util::truck_.base2front(),
      -util::truck_.half_expand_width(), util::truck_.half_expand_width());
  const double t_start = util::sys_config_.stitch_time();
  const double min_s = reference_line_info->sl_init_point().s();
  const double time = config_.t_end() - t_start;
  const int tseg_num = time / config_.t_step();
  const double s_step = (config_.max_s() - min_s) / config_.sseg_num();
  const double a_step =
      (config_.max_acc() - config_.min_acc()) / config_.aseg_num();
  const double l_step =
      (config_.max_l() - config_.min_l()) / config_.lseg_num();
  const double dl_step =
      (config_.max_dl() - config_.min_dl()) / config_.dlseg_num();

  std::vector<double> t;
  std::vector<std::vector<port::TslBoundary>> tsl_boundarys;
  t.reserve(tseg_num);
  tsl_boundarys.reserve(tseg_num + 1);
  for (double ti = t_start; ti <= config_.t_end(); ti += config_.t_step()) {
    tsl_boundarys.emplace_back();
    auto& tsl_boundary = tsl_boundarys.back();
    tsl_boundary.reserve(reference_line_info->obstacles().Items().size());
    for (auto& obs : reference_line_info->obstacles().Items()) {
      tsl_boundary.emplace_back(obs->tsl_boundarys().EvaluateByT(ti));
    }
    t.push_back(ti);
  }
  std::vector<tport::PathPoint> s_p;
  std::vector<double> s_v_map;
  std::vector<double> s_v_upper;
  std::vector<double> s_v_step;
  std::vector<port::ReferenceLineInfo::LRWidth> s_l_boundary;
  std::vector<std::vector<double>> sv_ddl_lower;
  std::vector<std::vector<double>> sv_ddl_upper;
  std::vector<std::vector<double>> sv_ddl_step;
  std::vector<std::vector<double>> sv_kappa_upper;
  std::vector<std::vector<double>> sv_dddl_lower;
  std::vector<std::vector<double>> sv_dddl_upper;
  std::vector<std::vector<double>> sv_dddl_step;
  std::vector<std::vector<double>> sv_dkappa_upper;
  s_p.reserve(config_.sseg_num());
  s_v_map.reserve(config_.sseg_num());
  s_v_upper.reserve(config_.sseg_num());
  s_v_step.reserve(config_.sseg_num());
  s_l_boundary.reserve(config_.sseg_num());
  sv_ddl_lower.reserve(config_.sseg_num());
  sv_ddl_upper.reserve(config_.sseg_num());
  sv_ddl_step.reserve(config_.sseg_num());
  sv_kappa_upper.reserve(config_.sseg_num());
  sv_dddl_lower.reserve(config_.sseg_num());
  sv_dddl_upper.reserve(config_.sseg_num());
  sv_dddl_step.reserve(config_.sseg_num());
  sv_dkappa_upper.reserve(config_.sseg_num());
  for (double si = min_s; si < config_.max_s(); si += s_step) {
    s_p.push_back(reference_line_info->EvaluateByS(si));
    s_v_map.push_back(reference_line_info->GetSpeedLimitFromS(si));
    const double v_upper = s_v_map.back() * config_.v_boundary_relax_ratio();
    s_v_upper.push_back(v_upper);
    const double v_step = v_upper / config_.vseg_num();
    s_v_step.push_back(v_step);
    s_l_boundary.push_back(reference_line_info->GetEgoDrivableWidth(si));
    sv_ddl_step.emplace_back();
    sv_ddl_lower.emplace_back();
    sv_ddl_upper.emplace_back();
    sv_kappa_upper.emplace_back();
    sv_dddl_step.emplace_back();
    sv_dddl_lower.emplace_back();
    sv_dddl_upper.emplace_back();
    sv_dkappa_upper.emplace_back();
    auto& v_ddl_step = sv_ddl_step.back();
    auto& v_ddl_lower = sv_ddl_lower.back();
    auto& v_ddl_upper = sv_ddl_upper.back();
    auto& v_kappa_upper = sv_kappa_upper.back();
    auto& v_dddl_step = sv_dddl_step.back();
    auto& v_dddl_lower = sv_dddl_lower.back();
    auto& v_dddl_upper = sv_dddl_upper.back();
    auto& v_dkappa_upper = sv_dkappa_upper.back();
    v_ddl_step.reserve(config_.vseg_num());
    v_ddl_lower.reserve(config_.vseg_num());
    v_ddl_upper.reserve(config_.vseg_num());
    v_kappa_upper.reserve(config_.vseg_num());
    v_dddl_step.reserve(config_.vseg_num());
    v_dddl_lower.reserve(config_.vseg_num());
    v_dddl_upper.reserve(config_.vseg_num());
    v_dkappa_upper.reserve(config_.vseg_num());
    const double kappa = s_p.back().kappa();
    const double dkappa = s_p.back().dkappa();
    for (double vi = 0.0; vi < v_upper; vi += v_step) {
      // v_kappa_upper.push_back(util::GetMaxKappa(vi));
      v_ddl_lower.push_back(-v_kappa_upper.back() - kappa);
      v_ddl_upper.push_back(v_kappa_upper.back() - kappa);
      v_ddl_step.push_back((v_ddl_upper.back() - v_ddl_lower.back()) /
                           config_.ddlseg_num());
      v_dkappa_upper.push_back(config_.max_steering_angular_velocity() *
                               util::truck_.max_curvature() /
                               std::max(vi, 1.0));
      v_dddl_lower.push_back(-v_dkappa_upper.back() - dkappa);
      v_dddl_upper.push_back(v_dkappa_upper.back() - dkappa);
      v_dddl_step.push_back((v_dddl_upper.back() - v_dddl_lower.back()) /
                            config_.dddlseg_num());
    }
  }

  std::uniform_real_distribution<double> jerk(config_.min_jerk(),
                                              config_.max_jerk());
  std::default_random_engine random_engine(std::time(NULL));
  const double step_jerk =
      (config_.max_jerk() - config_.min_jerk()) / config_.jerkseg_num();

  port::TslTrajectoryPoint init_tsl(t_start, frame->init_point(),
                                    reference_line_info->sl_init_point());
  if (frame->replan_flag()) {
    init_tsl.set_dl(0.0);
    init_tsl.set_ddl(0.0);
  }
  std::list<std::list<Node>> lists{{Node(init_tsl)}};
  auto init_it = lists.begin()->begin();
  // s l v dl a ddl
  std::vector<std::vector<std::vector<
      std::vector<std::vector<std::vector<std::list<Node>::iterator>>>>>>
      tab(config_.sseg_num(),
          std::vector<std::vector<std::vector<
              std::vector<std::vector<std::list<Node>::iterator>>>>>(
              config_.lseg_num(),
              std::vector<std::vector<
                  std::vector<std::vector<std::list<Node>::iterator>>>>(
                  config_.vseg_num(),
                  std::vector<
                      std::vector<std::vector<std::list<Node>::iterator>>>(
                      config_.dlseg_num(),
                      std::vector<std::vector<std::list<Node>::iterator>>(
                          config_.aseg_num(),
                          std::vector<std::list<Node>::iterator>(
                              config_.ddlseg_num(), init_it))))));
  for (int ti = 0; ti < tseg_num; ++ti) {
    auto& last_list = lists.back();
    lists.emplace_back();
    auto& cur_list = lists.back();
    std::list<std::list<Node>::iterator*> cur_position_ptr;

    for (auto& last : last_list) {
      const int last_si = (last.s() - min_s) / s_step;
      const int last_vi = last.v() / s_v_step[last_si];
      double desired_jerk = config_.k_v0() * (s_v_map[last_si] - last.v()) -
                            config_.k_v1() * last.a();
      double desired_dddl = -config_.k_l0() * last.l() -
                            config_.k_l1() * last.dl() -
                            config_.k_l1() * last.ddl();
      auto last_boundary = ego_boundary + last;
      for (const auto& obs_boundary : tsl_boundarys[ti]) {
        const double lon_dis = obs_boundary.CalcLonDistence(last_boundary);
        const double lat_dis = obs_boundary.CalcLatDistence(last_boundary);
        const double ev = obs_boundary.v().s() - last.v();
        const double ea = obs_boundary.a().s() - last.a();
        const double nudge_distance = tutil::clamp(
            util::StraightLineByTwoPoint(
                config_.lat_distance_min_relative_speed(),
                config_.lat_min_distance(),
                config_.lat_distance_max_relative_speed(),
                config_.lat_max_distance(), -ev),
            config_.lat_min_distance(), config_.lat_max_distance());
        if (lat_dis == 0.0) {
          if (last_boundary.max_s() <= obs_boundary.min_s()) {
            const double es =
                lon_dis - RssDistance(last.v(), obs_boundary.v().s());
            const double lon_s_follow_jerk =
                config_.k_s0() * es + config_.k_s1() * ev + config_.k_s2() * ea;
            desired_jerk = std::min(desired_jerk, lon_s_follow_jerk);
          } else if (lat_dis <= nudge_distance &&
                     ev <= 0.0) {  // 障碍物速度快，则障碍物需要考虑避让
            double el = nudge_distance - lat_dis;
            if (obs_boundary.max_l() > last_boundary.min_l()) {  // to right
              el = -el;
            }
            const double nudge_dddl = config_.k_l0() * el -
                                      config_.k_l1() * last.dl() -
                                      config_.k_l1() * last.ddl();
            if (std::abs(nudge_dddl) > std::abs(desired_dddl)) {
              desired_dddl = nudge_dddl;
            }
          }
        }
      }

      std::uniform_real_distribution<double> dddl(
          sv_dddl_lower[last_si][last_vi], sv_dddl_upper[last_si][last_vi]);
      std::vector<tport::SLPoint> u_steps;
      u_steps.reserve(101);
      u_steps.emplace_back(desired_jerk, desired_dddl);
      for (int i = 0; i < 100; ++i) {
        u_steps.emplace_back(jerk(random_engine), dddl(random_engine));
      }
      // for (auto& u : us) {
      for (auto& u : u_steps) {
        // cur_list.push_back(last.Update(config_.t_step(), jerk(random_engine),
        //                                dddl(random_engine)));
        cur_list.push_back(last.Update(config_.t_step(), u));
        auto cur = std::prev(cur_list.end());
        if (cur->s() < min_s || cur->s() >= config_.max_s()) {
          // TRUNK_LOG_WARN << 0;
          cur_list.pop_back();
          continue;
        }
        if (cur->l() < config_.min_l() || cur->l() >= config_.max_l()) {
          // TRUNK_LOG_WARN << 1;
          cur_list.pop_back();
          continue;
        }
        if (cur->dl() < config_.min_dl() || cur->dl() >= config_.max_dl()) {
          // TRUNK_LOG_WARN << 2;
          cur_list.pop_back();
          continue;
        }
        if (cur->a() < config_.min_acc() || cur->a() >= config_.max_acc()) {
          // TRUNK_LOG_WARN << 3;
          cur_list.pop_back();
          continue;
        }
        const int si = (cur->s() - min_s) / s_step;
        if (cur->v() < 0.0 || cur->v() >= s_v_upper[si]) {
          // TRUNK_LOG_WARN << 4;
          cur_list.pop_back();
          continue;
        }
        const int vi = cur->v() / s_v_step[si];
        if (cur->ddl() < sv_ddl_lower[si][vi] ||
            cur->ddl() >= sv_ddl_upper[si][vi]) {
          // TRUNK_LOG_WARN << 5;
          cur_list.pop_back();
          continue;
        }
        if (cur->l() < s_l_boundary[si].right_width ||
            cur->l() >= s_l_boundary[si].left_width) {
          // TRUNK_LOG_WARN << 6;
          cur_list.pop_back();
          continue;
        }
        auto cur_boundary = ego_boundary + *cur;
        if (std::any_of(tsl_boundarys[ti + 1].begin(),
                        tsl_boundarys[ti + 1].end(),
                        [&cur_boundary](const auto& obs) {
                          return cur_boundary.CollisionCheck(obs);
                        })) {
          // TRUNK_LOG_WARN << 7;
          cur_list.pop_back();
          continue;
        }

        // TODO: 计算cost
        // double jerk = config_.k_v0() * (s_v_map[si] - cur->v()) -
        //               config_.k_v1() * cur->a();
        // double dddl = -config_.k_l0() * cur->l() - config_.k_l1() * cur->dl()
        // -
        //               config_.k_l1() * cur->ddl();
        // for (const auto& obs_boundary : tsl_boundarys[ti]) {
        //   const double lon_dis = obs_boundary.CalcLonDistence(cur_boundary);
        //   const double lat_dis = obs_boundary.CalcLatDistence(cur_boundary);
        //   const double ev = obs_boundary.v().s() - cur->v();
        //   const double ea = obs_boundary.a().s() - cur->a();
        //   const double nudge_distance = tutil::clamp(
        //       util::StraightLineByTwoPoint(
        //           config_.lat_distance_min_relative_speed(),
        //           config_.lat_min_distance(),
        //           config_.lat_distance_max_relative_speed(),
        //           config_.lat_max_distance(), -ev),
        //       config_.lat_min_distance(), config_.lat_max_distance());
        //   if (lat_dis == 0.0) {
        //     if (cur_boundary.max_s() <= obs_boundary.min_s()) {
        //       const double es =
        //           lon_dis - RssDistance(cur->v(), obs_boundary.v().s());
        //       const double lon_s_follow_jerk = config_.k_s0() * es +
        //                                        config_.k_s1() * ev +
        //                                        config_.k_s2() * ea;
        //       jerk = std::min(jerk, lon_s_follow_jerk);
        //     } else if (lat_dis <= nudge_distance &&
        //                ev <= 0.0) {  // 障碍物速度快，则障碍物需要考虑避让
        //       double el = nudge_distance - lat_dis;
        //       if (obs_boundary.max_l() > cur_boundary.min_l()) {  // to right
        //         el = -el;
        //       }
        //       const double nudge_dddl = config_.k_l0() * el -
        //                                 config_.k_l1() * cur->dl() -
        //                                 config_.k_l1() * cur->ddl();
        //       if (std::abs(nudge_dddl) > std::abs(dddl)) {
        //         dddl = nudge_dddl;
        //       }
        //     }
        //   }
        // }
        double cost_lon = config_.weight_lon_track() * config_.t_step() *
                          std::abs(desired_jerk - cur->da());
        double cost_lat = config_.weight_lat_track() * cur->ds() *
                          std::abs(desired_dddl - cur->dddl());
        cur->set_self_cost(cost_lon + cost_lat);

        cur->set_cost(last.cost() + cur->self_cost());
        const int ai = (cur->a() - config_.min_acc()) / a_step;
        const int li = (cur->l() - config_.min_l()) / l_step;
        const int dli = (cur->dl() - config_.min_dl()) / dl_step;
        const int ddli =
            (cur->ddl() - sv_ddl_lower[si][vi]) / sv_ddl_step[si][vi];
        auto& target = tab[si][li][vi][dli][ai][ddli];

        if (target == init_it) {
          // TRUNK_LOG_WARN << "insert";
          target = cur;
          cur_position_ptr.push_back(&target);
        } else if (cur->cost() <= target->cost()) {
          // TRUNK_LOG_WARN << "replace";
          cur_list.erase(target);
          target = cur;
        } else {
          // TRUNK_LOG_WARN << "discard";
          cur_list.pop_back();
        }
      }
    }
    TRUNK_LOG_ERROR << ti << ": " << cur_position_ptr.size();
    if (cur_position_ptr.empty()) {
      lists.pop_back();
      TRUNK_LOG_ERROR << "plan failed";
      break;
    }
    for (auto ptr : cur_position_ptr) {
      *ptr = init_it;
    }
  }
  const Node* optimal_node = nullptr;
  double min_cost = std::numeric_limits<double>::max();
  for (auto& p : lists.back()) {
    if (min_cost > p.cost()) {
      min_cost = p.cost();
      optimal_node = &p;
    }
  }

  std::list<const Node*> tsl_traj;
  for (const Node* ptr = optimal_node; ptr != nullptr; ptr = ptr->last()) {
    tsl_traj.push_front(ptr);
  }
  *reference_line_info->mutable_trajectory() = frame->stitch_trajectory();
  const double diff_s =
      reference_line_info->trajectory().back().s() - init_it->s();
  reference_line_info->mutable_trajectory()->pop_back();
  for (auto& p : *reference_line_info->mutable_trajectory()) {
    p.set_s(p.s() - diff_s);
    p.set_s(p.s() - config_.t_step());
  }
  for (auto* ptr : tsl_traj) {
    auto xy = reference_line_info->SlPoint2PathPoint(*ptr);
    tport::TrajectoryPoint p(xy);
    p.set_s(ptr->s());
    p.set_v(ptr->v());
    p.set_a(ptr->a());
    p.set_da(ptr->da());
    p.set_relative_time(ptr->t());
    reference_line_info->mutable_trajectory()->push_back(p);
  }

  return tpnc::Status::OK();
}

double DpTslIntervalLatticeTask::RssDistance(const double cur_v,
                                             const double v_front) const {
  return std::max(
      0.0, cur_v * config_.rss_action_delay() +
               0.5 * config_.rss_ego_max_acc() *
                   std::pow(config_.rss_action_delay(), 2) +
               std::pow(cur_v + config_.rss_action_delay() *
                                    config_.rss_ego_max_acc(),
                        2) /
                   2.0 / std::fabs(config_.rss_ego_max_dec()) -
               v_front * v_front / 2.0 / std::fabs(config_.rss_obs_max_dec()));
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
