// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include "param/speed_decider_param.h"
#include "task/task.h"
#include "util/util.h"

namespace trunk {
namespace pnd {
namespace task {

class SpeedDeciderTask : public Task {
 public:
  SpeedDeciderTask();

  virtual ~SpeedDeciderTask() = default;

  tpnc::Status Execute(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info) override;

 private:
  enum StPosition {
    ABOVE = 1,  // 处于障碍物前方
    BELOW = 2,  // 处于障碍物后方
    CROSS = 3,  // 与障碍物路径有重叠
  };

  StPosition GetStPosition(const port::SpeedData& speed_profile,
                           const port::STBoundary& st_boundary) const;

  bool CheckKeepClearCrossable(
      const port::SpeedData& speed_profile,
      const port::STBoundary& keep_clear_st_boundary) const;

  bool CheckKeepClearBlocked(const port::IndexedObstacles& obstacles,
                             const port::Obstacle& keep_clear_obstacle) const;

  /**
   * @brief check if the ADC should follow an obstacle by examing the
   *StBoundary of the obstacle.
   * @param boundary The boundary of the obstacle.
   * @return true if the ADC believe it should follow the obstacle, and
   *         false otherwise.
   **/
  bool CheckIsFollowByT(const port::STBoundary& boundary) const;

  bool CreateStopDecision(port::Obstacle& obstacle, double stop_distance) const;

  /**
   * @brief create follow decision based on the boundary
   **/
  bool CreateFollowDecision(port::Obstacle& obstacle) const;

  /**
   * @brief create yield decision based on the boundary
   **/
  bool CreateYieldDecision(port::Obstacle& obstacle) const;

  /**
   * @brief create overtake decision based on the boundary
   **/
  bool CreateOvertakeDecision(port::Obstacle& obstacle) const;

  tpnc::Status MakeObjectDecision(const port::SpeedData& speed_profile,
                                  port::IndexedObstacles* const obstacles);

  void AppendIgnoreDecision(port::Obstacle& obstacle) const;

  /**
   * @brief "too close" is determined by whether ego vehicle will hit the front
   * obstacle if the obstacle drive at current speed and ego vehicle use some
   * reasonable deceleration
   **/
  bool IsFollowTooClose(const port::Obstacle& obstacle) const;

 private:
  port::SLBoundary adc_sl_boundary_;
  tport::TrajectoryPoint init_point_;
  const port::ReferenceLineInfo* reference_line_ = nullptr;
  const SpeedDeciderParam& config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()
          ->GetConfig<SpeedDeciderParam>("speed_decider");
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
