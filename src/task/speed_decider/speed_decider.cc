// Copyright 2023, trunk Inc. All rights reserved

#include "speed_decider.h"

namespace trunk {
namespace pnd {
namespace task {

REGISTER(trunk::pnd::task, Task, SpeedDecider);

SpeedDeciderTask::SpeedDeciderTask() : Task("speed_decider") {}

tpnc::Status SpeedDeciderTask::Execute(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* reference_line_info) {
  adc_sl_boundary_ = reference_line_info->AdcSlBoundary();
  reference_line_ = reference_line_info;
  if (!MakeObjectDecision(reference_line_info->speed_data(),
                          reference_line_info->mutable_obstacles())
           .ok()) {
    const std::string msg = "Get object decision by speed profile failed.";
    TRUNK_LOG_ERROR << msg;
    return tpnc::Status(tpnc::ErrorCode::PLANNING_OTHERS, msg);
  }
  return tpnc::Status::OK();
}

SpeedDeciderTask::StPosition SpeedDeciderTask::GetStPosition(
    const port::SpeedData& speed_profile,
    const port::STBoundary& st_boundary) const {
  StPosition st_position = BELOW;
  if (st_boundary.lower_points().empty()) {
    return st_position;
  }

  bool st_position_set = false;
  const double start_t = st_boundary.lower_points().front().t();
  const double end_t = st_boundary.lower_points().back().t();
  for (size_t i = 0; i + 1 < speed_profile.size(); ++i) {
    const port::STPoint curr_st(speed_profile[i].s(), speed_profile[i].t());
    const port::STPoint next_st(speed_profile[i + 1].s(),
                                speed_profile[i + 1].t());
    if (curr_st.t() < start_t && next_st.t() < start_t) {
      continue;
    }
    if (curr_st.t() > end_t) {
      break;
    }

    // check speed_line collision
    tport::Point2D pt1(curr_st.s(), curr_st.t());
    tport::Point2D pt2(next_st.s(), next_st.t());
    tport::Contour2D speed_line, boundary_polygen;
    speed_line.emplace_back(pt1);
    speed_line.emplace_back(pt2);
    for (auto pt : st_boundary.lower_points()) {
      boundary_polygen.emplace_back(pt.s(), pt.t());
    }
    for (auto pt : st_boundary.upper_points()) {
      boundary_polygen.emplace_back(pt.s(), pt.t());
    }
    if (tutil::ObstacleDetect{}.GjkCheck(speed_line, boundary_polygen)) {
      // ADEBUG << "speed profile cross st_boundaries.";
      st_position = CROSS;

      if (st_boundary.boundary_type() ==
          port::STBoundary::BoundaryType::KEEP_CLEAR) {
        if (!CheckKeepClearCrossable(speed_profile, st_boundary)) {
          st_position = BELOW;
        }
      }
      break;
    }

    // note: st_position can be calculated by checking two st points once
    //       but we need iterate all st points to make sure there is no CROSS
    if (!st_position_set) {
      if (start_t < next_st.t() && curr_st.t() < end_t) {
        port::STPoint bd_point_front = st_boundary.upper_points().front();
        double side = tutil::CrossProd(
            tport::Point2D(bd_point_front.t(), bd_point_front.s()),
            tport::Point2D(curr_st.t(), curr_st.s()),
            tport::Point2D(next_st.t(), next_st.s()));
        st_position = side < 0.0 ? ABOVE : BELOW;
        st_position_set = true;
      }
    }
  }
  return st_position;
}

bool SpeedDeciderTask::CheckKeepClearCrossable(
    const port::SpeedData& speed_profile,
    const port::STBoundary& keep_clear_st_boundary) const {
  bool keep_clear_crossable = true;

  const auto& last_speed_point = speed_profile.back();
  double last_speed_point_v = 0.0;
  if (last_speed_point.v() != 0.0) {
    last_speed_point_v = last_speed_point.v();
  } else {
    const size_t len = speed_profile.size();
    if (len > 1) {
      const auto& last_2nd_speed_point = speed_profile[len - 2];
      last_speed_point_v = (last_speed_point.s() - last_2nd_speed_point.s()) /
                           (last_speed_point.t() - last_2nd_speed_point.t());
    }
  }
  constexpr double kKeepClearSlowSpeed = 2.5;  // m/s
  //  ADEBUG << "last_speed_point_s[" << last_speed_point.s()
  //         << "] st_boundary.max_s[" << keep_clear_st_boundary.max_s()
  //         << "] last_speed_point_v[" << last_speed_point_v << "]";
  if (last_speed_point.s() <= keep_clear_st_boundary.max_s() &&
      last_speed_point_v < kKeepClearSlowSpeed) {
    keep_clear_crossable = false;
  }
  return keep_clear_crossable;
}

bool SpeedDeciderTask::CheckKeepClearBlocked(
    const port::IndexedObstacles& obstacles,
    const port::Obstacle& keep_clear_obstacle) const {
  bool keep_clear_blocked = false;

  // check if overlap with other stop wall
  for (const auto* obstacle : obstacles.Items()) {
    if (obstacle->obj().id() == keep_clear_obstacle.obj().id()) {
      continue;
    }
    const double obstacle_start_s = obstacle->sl_boundary().min_s();
    const double adc_length = 6.0;
    // VehicleConfigHelper::GetConfig().vehicle_param().length();
    const double distance =
        obstacle_start_s - keep_clear_obstacle.sl_boundary().max_s();

    if (obstacle->is_blocking() && distance > 0 &&
        distance < (adc_length / 2)) {
      keep_clear_blocked = true;
      break;
    }
  }
  return keep_clear_blocked;
}

bool SpeedDeciderTask::IsFollowTooClose(const port::Obstacle& obstacle) const {
  if (!obstacle.is_blocking()) {
    return false;
  }

  if (obstacle.st_boundary().min_t() > 0.0) {
    return false;
  }
  const double obs_speed = obstacle.obj().velocity();
  const double ego_speed = init_point_.v();
  if (obs_speed > ego_speed) {
    return false;
  }
  const double distance =
      obstacle.st_boundary().min_s() - config_.min_stop_distance_obstacle();
  constexpr double decel = 1.0;
  return distance < std::pow((ego_speed - obs_speed), 2) * 0.5 / decel;
}

tpnc::Status SpeedDeciderTask::MakeObjectDecision(
    const port::SpeedData& speed_profile,
    port::IndexedObstacles* const obstacles) {
  if (speed_profile.size() < 2) {
    const std::string msg = "dp_st_graph failed to get speed profile.";
    TRUNK_LOG_ERROR << msg;
    return tpnc::Status(tpnc::ErrorCode::PLANNING_OTHERS, msg);
  }
  const double min_s = reference_line_->policy() == "lane_change"
                           ? -200
                           : reference_line_->AdcSlBoundary().min_s();
  for (auto* obstacle : obstacles->Items()) {
    const auto& boundary = obstacle->st_boundary();

    if (boundary.lower_points().empty() || boundary.max_s() < min_s ||
        boundary.max_t() < 0.0 ||
        boundary.min_t() >= speed_profile.back().t()) {
      TRUNK_LOG_DEBUG << "obstacle: " << obstacle->obj().id()
                      << " ,not in speed profile, ignore.";
      AppendIgnoreDecision(*obstacle);
      continue;
    }
    if (obstacle->st_boundary().boundary_type() !=
        port::STBoundary::BoundaryType::UNKNOWN) {
      AppendIgnoreDecision(*obstacle);
      continue;
    }

    auto position = GetStPosition(speed_profile, boundary);
    if (boundary.boundary_type() ==
        port::STBoundary::BoundaryType::KEEP_CLEAR) {
      if (CheckKeepClearBlocked(*obstacles, *obstacle)) {
        position = BELOW;
      }
    }

    auto box = obstacle->obj().xy_contour();
    double start_abs_l =
        std::abs(tutil::TransformFrenet::TransformToFrenet(
                     reference_line_->path_data().qp_norm_path(),
                     obstacle->obj().xy_center())
                     .l());
    TRUNK_LOG_INFO << "position: " << position;
    switch (position) {
      case BELOW:
        if (boundary.boundary_type() ==
            port::STBoundary::BoundaryType::KEEP_CLEAR) {
          if (CreateStopDecision(*obstacle, 0.0)) {
            TRUNK_LOG_INFO << "stop for keep_clear";
            obstacle->AddLongitudinalDecision("dp_st_graph/keep_clear",
                                              port::ObstacleDecision::kStop);
          }
        } else if (CheckIsFollowByT(boundary) &&
                   (boundary.max_t() - boundary.min_t() >
                    config_.follow_min_time_sec()) &&
                   start_abs_l < config_.follow_min_obs_lateral_distance()) {
          // stop for low_speed decelerating
          // if (IsFollowTooClose(*obstacle)) {
          //  if (CreateStopDecision(*obstacle,
          //                         -config_.min_stop_distance_obstacle())) {
          //    TRUNK_LOG_INFO << "stop for follow too close";
          //    obstacle->AddLongitudinalDecision("dp_st_graph/too_close",
          //                                      port::ObstacleDecision::kStop);
          //  }
          //} else {  // high speed or low speed accelerating
          // FOLLOW decision
          // if (CreateFollowDecision(*obstacle)) {
          obstacle->AddLongitudinalDecision("dp_st_graph/follow",
                                            port::ObstacleDecision::kFollow);
          // }
          // }
        } else {
          // YIELD decision
          // XXX: 对快速切入的车辆，是否考虑打上ignore标签，或约束减速度，
          // 防止急减速情况
          if (CreateYieldDecision(*obstacle)) {
            obstacle->AddLongitudinalDecision("dp_st_graph/yield",
                                              port::ObstacleDecision::kYield);
          }
        }
        break;
      case ABOVE:
        if (boundary.boundary_type() ==
            port::STBoundary::BoundaryType::KEEP_CLEAR) {
          obstacle->AddLongitudinalDecision("dp_st_graph/ignore",
                                            port::ObstacleDecision::kIgnore);
        } else {
          // OVERTAKE decision
          if (CreateOvertakeDecision(*obstacle)) {
            obstacle->AddLongitudinalDecision(
                "dp_st_graph/overtake", port::ObstacleDecision::kOvertake);
          }
        }
        break;
      case CROSS:
        // TODO:待调整
        // if (obstacle->obj().velocity() > 5.0) {
        //   if (obstacle->sl_boundary().max_s() < 0.0 &&
        //       CreateOvertakeDecision(*obstacle)) {
        //     obstacle->AddLongitudinalDecision(
        //         "dp_st_graph/cross-overtake",
        //         port::ObstacleDecision::kOvertake);
        //   } else if (CreateFollowDecision(*obstacle)) {
        //     obstacle->AddLongitudinalDecision("dp_st_graph/cross-follow",
        //                                       port::ObstacleDecision::kFollow);
        //   }
        // } else
        if (obstacle->is_blocking() &&
            obstacle->sl_boundary().max_s() > min_s) {
          if (CreateStopDecision(*obstacle,
                                 -config_.min_stop_distance_obstacle())) {
            TRUNK_LOG_INFO << "stop for crossing";
            obstacle->AddLongitudinalDecision("dp_st_graph/cross",
                                              port::ObstacleDecision::kFollow);
          }
          const std::string msg =
              "Failed to find a solution for crossing obstacle:" +
              std::to_string(obstacle->obj().id());
          TRUNK_LOG_ERROR << msg;
          /* return tpnc::Status(tpnc::ErrorCode::PLANNING_OTHERS, msg); */
        }
        break;
      default:
        TRUNK_LOG_ERROR << "Unknown position:" << position;
    }
    AppendIgnoreDecision(*obstacle);
  }
  for (auto* obstacle : obstacles->Items()) {
    TRUNK_LOG_INFO << "obstacle: " << obstacle->obj().id() << " lon_decision: "
                   << obstacle->TypeName(obstacle->lon_decision())
                   << " lat_decision: "
                   << obstacle->TypeName(obstacle->lat_decision())
                   << " pos: " << obstacle->obj().xy_center();
  }
  return tpnc::Status::OK();
}

void SpeedDeciderTask::AppendIgnoreDecision(port::Obstacle& obstacle) const {
  if (obstacle.lon_decision() == port::ObstacleDecision::kNotSet) {
    obstacle.AddLongitudinalDecision("dp_st_graph/ignore",
                                     port::ObstacleDecision::kIgnore);
  }
  //  if (!obstacle->HasLateralDecision()) {
  //    obstacle->AddLateralDecision("dp_st_graph", ignore_decision);
  //  }
}

bool SpeedDeciderTask::CreateStopDecision(port::Obstacle& obstacle,
                                          double stop_distance) const {
  // DCHECK_NOTNULL(stop_decision);

  const auto& boundary = obstacle.st_boundary();
  const double kHeadLength = 8.0;
  double fence_s = adc_sl_boundary_.max_s() + boundary.min_s() + stop_distance;
  if (boundary.boundary_type() == port::STBoundary::BoundaryType::KEEP_CLEAR) {
    fence_s = obstacle.sl_boundary().min_s();
  }
  const double main_stop_s = reference_line_->stop_reference_line_s();
  if (main_stop_s < fence_s - kHeadLength) {
    // ADEBUG << "Stop fence is further away, ignore.";
    return false;
  }

  // const auto fence_point = reference_line_->GetReferencePoint(fence_s);

  // set STOP decision
  // auto* stop = stop_decision->mutable_stop();
  //  stop->set_distance_s(stop_distance);
  //  auto* stop_point = stop->mutable_stop_point();
  //  stop_point->set_x(fence_point.x());
  //  stop_point->set_y(fence_point.y());
  //  stop_point->set_z(0.0);
  //  stop->set_stop_heading(fence_point.heading());

  if (boundary.boundary_type() == port::STBoundary::BoundaryType::KEEP_CLEAR) {
    // stop->set_reason_code(StopReasonCode::STOP_REASON_CLEAR_ZONE);
    TRUNK_LOG_WARN << "STOP_REASON_CLEAR_ZONE";
  }

  // PerceptionObstacle::Type obstacle_type = obstacle.Perception().type();
  //  ADEBUG << "STOP: obstacle_id[" << obstacle.Id() << "] obstacle_type["
  //         << PerceptionObstacle_Type_Name(obstacle_type) << "]";

  return true;
}

bool SpeedDeciderTask::CreateFollowDecision(port::Obstacle& obstacle) const {
  // DCHECK_NOTNULL(follow_decision);

  const double follow_speed = init_point_.v();
  const double follow_distance_s =
      -std::fmax(follow_speed * config_.follow_time_buffer(),
                 config_.follow_min_distance());

  const auto& boundary = obstacle.st_boundary();
  const double reference_s =
      adc_sl_boundary_.max_s() + boundary.min_s() + follow_distance_s;
  const double main_stop_s = reference_line_->stop_reference_line_s();
  if (main_stop_s < reference_s) {
    // ADEBUG << "Follow reference_s is further away, ignore.";
    return false;
  }

  // auto ref_point = reference_line_->GetReferencePoint(reference_s);

  // set FOLLOW decision
  //  follow->set_distance_s(follow_distance_s);
  //  auto* fence_point = follow->mutable_fence_point();
  //  fence_point->set_x(ref_point.x());
  //  fence_point->set_y(ref_point.y());
  //  fence_point->set_z(0.0);
  //  follow->set_fence_heading(ref_point.heading());

  // PerceptionObstacle::Type obstacle_type = obstacle.Perception().type();
  //  ADEBUG << "FOLLOW: obstacle_id[" << obstacle.Id() << "] obstacle_type["
  //         << PerceptionObstacle_Type_Name(obstacle_type) << "]";

  return true;
}

bool SpeedDeciderTask::CreateYieldDecision(port::Obstacle& obstacle) const {
  // DCHECK_NOTNULL(yield_decision);

  double yield_distance = config_.yield_distance();
  // int obstacle_type = obstacle.obj().type();
  //  switch (obstacle_type) {
  //    case PerceptionObstacle::PEDESTRIAN:
  //    case PerceptionObstacle::BICYCLE:
  //      yield_distance = config_.yield_distance_pedestrian_bycicle();
  //      break;
  //    default:
  //      yield_distance = config_.yield_distance();
  //      break;
  //  }

  const auto& obstacle_boundary = obstacle.st_boundary();
  const double yield_distance_s =
      std::max(-obstacle_boundary.min_s(), -yield_distance);

  const double reference_line_fence_s =
      adc_sl_boundary_.max_s() + obstacle_boundary.min_s() + yield_distance_s;
  const double main_stop_s = reference_line_->stop_reference_line_s();
  if (main_stop_s < reference_line_fence_s) {
    // ADEBUG << "Yield reference_s is further away, ignore.";
    return false;
  }

  // auto ref_point =
  // reference_line_->GetReferencePoint(reference_line_fence_s);

  // set YIELD decision
  //  yield->set_distance_s(yield_distance_s);
  //  yield->mutable_fence_point()->set_x(ref_point.x());
  //  yield->mutable_fence_point()->set_y(ref_point.y());
  //  yield->mutable_fence_point()->set_z(0.0);
  //  yield->set_fence_heading(ref_point.heading());

  //  ADEBUG << "YIELD: obstacle_id[" << obstacle.Id() << "] obstacle_type["
  //         << PerceptionObstacle_Type_Name(obstacle_type) << "]";

  return true;
}

bool SpeedDeciderTask::CreateOvertakeDecision(port::Obstacle& obstacle) const {
  // DCHECK_NOTNULL(overtake_decision);

  constexpr double kOvertakeTimeBuffer = 3.0;    // in seconds
  constexpr double kMinOvertakeDistance = 10.0;  // in meters

  // const auto& velocity = obstacle.obj().xy_velocity();
  // obstacle long speed
  const double obstacle_speed =
      obstacle.obj().xy_velocity().x() * std::cos(init_point_.theta()) +
      obstacle.obj().xy_velocity().y() * std::sin(init_point_.theta());

  const double overtake_distance_s = std::fmax(
      std::fmax(init_point_.v(), obstacle_speed) * kOvertakeTimeBuffer,
      kMinOvertakeDistance);

  const auto& boundary = obstacle.st_boundary();
  const double reference_line_fence_s =
      adc_sl_boundary_.max_s() + boundary.min_s() + overtake_distance_s;
  const double main_stop_s = reference_line_->stop_reference_line_s();
  if (main_stop_s < reference_line_fence_s) {
    // ADEBUG << "Overtake reference_s is further away, ignore.";
    return false;
  }

  // auto ref_point =
  // reference_line_->GetReferencePoint(reference_line_fence_s);

  // set OVERTAKE decision
  //  overtake->set_distance_s(overtake_distance_s);
  //  overtake->mutable_fence_point()->set_x(ref_point.x());
  //  overtake->mutable_fence_point()->set_y(ref_point.y());
  //  overtake->mutable_fence_point()->set_z(0.0);
  //  overtake->set_fence_heading(ref_point.heading());

  // PerceptionObstacle::Type obstacle_type = obstacle.Perception().type();
  //  ADEBUG << "OVERTAKE: obstacle_id[" << obstacle.Id() << "] obstacle_type["
  //         << PerceptionObstacle_Type_Name(obstacle_type) << "]";

  return true;
}

bool SpeedDeciderTask::CheckIsFollowByT(
    const port::STBoundary& boundary) const {
  if (boundary.lower_points().front().s() >
      boundary.lower_points().back().s()) {
    return false;
  }
  constexpr double kFollowTimeEpsilon = 1e-3;
  constexpr double kFollowCutOffTime = 0.5;
  if (boundary.min_t() > kFollowCutOffTime ||
      boundary.max_t() < kFollowTimeEpsilon) {
    return false;
  }
  return true;
}
}  // namespace task
}  // namespace pnd
}  // namespace trunk
