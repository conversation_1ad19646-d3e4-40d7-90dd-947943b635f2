// Copyright 2023, trunk Inc. All rights reserved

#include "task.h"

#include <absl/time/clock.h>
#include <absl/time/time.h>

#include "log.h"

namespace trunk {
namespace pnd {
namespace task {

tpnc::Status Task::ExecuteAndCalRunTime(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  const auto start_run_time = absl::Now();
  tpnc::Status ret = tpnc::Status::OK();
  if (!reference_line_info->is_drivable().first) {
    return tpnc::Status::OK();
  }
  ret = Execute(frame, reference_line_info);
  const double run_time =
      absl::ToDoubleMilliseconds(absl::Now() - start_run_time);
  reference_line_info->AddRunTime(run_time);
  TRUNK_LOG_DEBUG << GetName() << " use time: " << run_time << " ms.";
  return ret;
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
