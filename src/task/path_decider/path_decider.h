// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/enum_states.h>

#include "param/path_decider_param.h"
#include "port/frame.h"
#include "task/task.h"
#include "util/key.h"

namespace trunk {
namespace pnd {
namespace task {

class PathDeciderTask : public Task {
 public:
  PathDeciderTask();
  virtual ~PathDeciderTask() = default;
  tpnc::Status Execute(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info) override;

 private:
  bool MakeObjectDecision(const port::PathData& path_data,
                          const port::IndexedObstacles& obstacles);

  bool MakeStaticObstacleDecision(const port::PathData& path_data,
                                  const port::IndexedObstacles& obstacles);

  bool MakeDynamicObstacleDecision(const port::PathData& path_data,
                                   const port::IndexedObstacles& obstacles);

  bool MergeWithMainStop(const port::Obstacle& obstacle);

  port::PathSLPoint GetEgoNearestPoint(const port::PathSLData& path,
                                       const port::SLBoundary& sl);
  port::ReferenceLineInfo* reference_line_info_ = nullptr;
  const PathDeciderParam* config_ = nullptr;
  tpnc::TurnSignal turn_signal_ = tpnc::TurnSignal::NO_TURN;
  std::shared_ptr<port::Frame> frame_ = nullptr;
  // TODO:
  // 此时假设每次运行完decider后都会reset一下，将其放置在decider里或者reference_line里都行，待检查
  double stop_reference_line_s_ = std::numeric_limits<double>::max();
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
