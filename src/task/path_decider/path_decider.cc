// Copyright 2023, trunk Inc. All rights reserved

#include "path_decider.h"

#include "log.h"

namespace trunk {
namespace pnd {
namespace task {

REGISTER(trunk::pnd::task, Task, PathDecider);

PathDeciderTask::PathDeciderTask() : Task("path_decider") {
  config_ = &tpnc::Singleton<tpnc::Config>::GetInstance()
                 ->GetConfig<PathDeciderParam>("path_decider");
}

tpnc::Status PathDeciderTask::Execute(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  frame_ = frame;
  if (reference_line_info == nullptr) {
    reference_line_info_ = reference_line_info;
    TRUNK_LOG_ERROR << "未传入参考线信息";
    return tpnc::Status::ERROR();
  }
  reference_line_info_ = reference_line_info;
  if (!MakeObjectDecision(reference_line_info->path_data(),
                          reference_line_info->obstacles())) {
    TRUNK_LOG_ERROR << "Failed to make decision based on tunnel";
    return tpnc::Status::ERROR();
  }
  turn_signal_ = frame->turn_signal();
  return tpnc::Status::OK();
}

bool PathDeciderTask::MakeObjectDecision(
    const port::PathData& path_data, const port::IndexedObstacles& obstacles) {
  if (!MakeStaticObstacleDecision(path_data, obstacles)) {
    TRUNK_LOG_ERROR << "Failed to make decisions for static obstacles";
    return false;
  }
  if (!MakeDynamicObstacleDecision(path_data, obstacles)) {
    TRUNK_LOG_ERROR << "Failed to make decisions for dynamic obstacles";
    // return false;
  }
  for (auto* obstacle : obstacles.Items()) {
    TRUNK_LOG_INFO << "obstacle: " << obstacle->obj().id()
                   << " lon_decision: " << (int)obstacle->lon_decision()
                   << " lat_decision: " << (int)obstacle->lat_decision();
  }
  return true;
}

bool PathDeciderTask::MakeStaticObstacleDecision(
    const port::PathData& path_data, const port::IndexedObstacles& obstacles) {
  // TODO: 当前传入path_data里的dp的结果
  const auto path_dp_result = path_data.dp_sl_path();
  if (path_dp_result.size() == 0) {
    TRUNK_LOG_ERROR << " Path is empty";
    return false;
  }
  // 车辆模型
  const auto model = tpnc::Singleton<tmodel::Truck>::GetInstance();
  tutil::TruckParam model_param(*model);
  const double half_width = model_param.head_width() / 2.0;
  const double lateral_radius = half_width + config_->lateral_ignore_buffer();
  const double lateral_stop_radius =
      half_width +
      config_->static_decision_nudge_l_buffer();  // 需要停车的横向距离
  // 只考虑静态障碍物
  for (auto path_obstacle : obstacles.Items()) {
    const auto obstacle = path_obstacle->obj();
    if (obstacle.xy_contour().size() < 3) {
      continue;
    }
    if (!path_obstacle->obj().is_static()) {
      continue;
    }
    if (path_obstacle->is_virtual()) {
      TRUNK_LOG_INFO << "stop for virtual obstacle";
      path_obstacle->AddLongitudinalDecision("PathDeciderTask/nearest-stop",
                                             port::ObstacleDecision::kStop);
      continue;
    }

    // 路边存在锥桶，防止变道过去
    // if (reference_line_info_->policy() == "lane_change" &&
    //     static_cast<port::ObjectType>(path_obstacle->obj().type()) ==
    //         port::ObjectType::TRAFFIC_CONE) {
    //   path_obstacle->AddLongitudinalDecision(
    //       "PathDeciderTask/traffic_cone-stop",
    //       port::ObstacleDecision::kStop);
    // }
    // TODO: 需要考虑处理逻辑
    // 过滤已经处理过的障碍物
    if (path_obstacle->lon_decision() == port::ObstacleDecision::kIgnore &&
        path_obstacle->lat_decision() == port::ObstacleDecision::kIgnore) {
      continue;
    }

    if (path_obstacle->lon_decision() == port::ObstacleDecision::kStop) {
      continue;
    }
    if ((path_obstacle->lat_decision() == port::ObstacleDecision::kLeftNudge ||
         path_obstacle->lat_decision() ==
             port::ObstacleDecision::kRightNudge)) {
      continue;
    }
    // TODO: 障碍物处于禁停区
    auto object_decision = port::ObstacleDecision::kIgnore;
    const auto sl_boundary = path_obstacle->sl_boundary();

    // 忽略掉不在参考线范围内的障碍物
    // if (sl_boundary.max_s() < path_dp_result.front().s() ||
    //     sl_boundary.min_s() > path_dp_result.back().s()) {
    //   // TODO:可以更换成不在一定区域内的障碍物
    //   path_obstacle->AddLongitudinalDecision("PathDeciderTask/not-in-s",
    //                                          object_decision);
    //   path_obstacle->AddLateralDecision("PathDeciderTask/not-in-s",
    //                                     object_decision);
    //   continue;
    // }

    // 当前使用参考线的长度
    if (sl_boundary.max_s() <
            reference_line_info_->reference_line().front().s() ||
        sl_boundary.min_s() >
            reference_line_info_->reference_line().back().s()) {
      // TODO:可以更换成不在一定区域内的障碍物
      continue;
    }
    // st图上障碍物的位置判断
    const auto ego_point = GetEgoNearestPoint(path_dp_result, sl_boundary);
    TRUNK_LOG_INFO << "id: " << obstacle.id()
                   << " obs_max_s: " << sl_boundary.max_s()
                   << " obs_min_s: " << sl_boundary.min_s()
                   << " obs_max_l: " << sl_boundary.max_l()
                   << " obs_min_l: " << sl_boundary.min_l()
                   << " ego_point_s: " << ego_point.s()
                   << " ego_point_l: " << ego_point.l()
                   << " half_width:" << half_width;

    if (ego_point.l() - lateral_radius > sl_boundary.max_l() ||
        ego_point.l() + lateral_radius < sl_boundary.min_l()) {
      TRUNK_LOG_INFO << "not in l";
      path_obstacle->AddLateralDecision("PathDeciderTask/not-in-l",
                                        object_decision);
    } else if (ego_point.l() - lateral_stop_radius < sl_boundary.max_l() &&
               ego_point.l() + lateral_stop_radius > sl_boundary.min_l()) {
      // 障碍物与自车存在重叠，停车
      object_decision = port::ObstacleDecision::kStop;
      if (MergeWithMainStop(*path_obstacle)) {
        // object_decision = port::ObstacleDecision::kStop;
        TRUNK_LOG_INFO << "stop for nearest obstacle";
        path_obstacle->AddLongitudinalDecision("PathDeciderTask/nearest-stop",
                                               object_decision);
      } else {
        // 优先选最近的障碍物停车
        TRUNK_LOG_INFO << "stop for not nearest obstacle";
        path_obstacle->AddLongitudinalDecision(
            "PathDeciderTask/not-nearest-stop", object_decision);
      }

    } else if (config_->enable_nudge_decision()) {
      // nudge decision
      if (ego_point.l() - lateral_stop_radius > sl_boundary.max_l()) {
        // left nudge
        object_decision = port::ObstacleDecision::kLeftNudge;
        path_obstacle->AddLateralDecision("PathDeciderTask/left-nudge",
                                          object_decision);
        TRUNK_LOG_INFO << "left nudge";
      } else {
        // right nudge
        object_decision = port::ObstacleDecision::kRightNudge;
        path_obstacle->AddLateralDecision("PathDeciderTask/right-nudge",
                                          object_decision);
        TRUNK_LOG_INFO << "right nudge";
      }
    }
  }
  return true;
}

bool PathDeciderTask::MakeDynamicObstacleDecision(
    const port::PathData& path_data, const port::IndexedObstacles& obstacles) {
  const auto path_dp_result = path_data.dp_sl_path();
  if (path_dp_result.size() == 0) {
    TRUNK_LOG_ERROR << " Path is empty";
    return false;
  }

  // 不准备换道时，可以忽略车后的障碍物
  for (auto obstacle : reference_line_info_->obstacles().Items()) {
    if (obstacle->sl_boundary().max_s() < 0) {
      if (reference_line_info_->policy() == "lane_follow") {
        obstacle->AddLongitudinalDecision("PathDeciderTask/ignore-obs-behind",
                                          port::ObstacleDecision::kIgnore);
      } else {
        // 变道车道，忽略不在目标车道的后方车辆
        if (obstacle->sl_boundary().min_l() > util::kHighwayLaneHalfWidth ||
            obstacle->sl_boundary().max_l() < -util::kHighwayLaneHalfWidth) {
          obstacle->AddLongitudinalDecision("PathDeciderTask/ignore-obs-behind",
                                            port::ObstacleDecision::kIgnore);
        }
        // 忽略自车正后方的车辆
        if (!(obstacle->sl_boundary().max_l() <
                  reference_line_info_->self_sl().l() -
                      0.5 * util::truck_.width() + 0.3 ||
              obstacle->sl_boundary().min_l() >
                  reference_line_info_->self_sl().l() +
                      0.5 * util::truck_.width() - 0.3)) {
          obstacle->AddLongitudinalDecision("PathDeciderTask/ignore-obs-behind",
                                            port::ObstacleDecision::kIgnore);
        }
      }
    }
  }

  // 动态障碍物
  const auto model = tpnc::Singleton<tmodel::Truck>::GetInstance();
  tutil::TruckParam model_param(*model);
  const double lateral_radius =
      model_param.head_width() / 2.0 + config_->lateral_ignore_buffer();
  const double lateral_stop_radius =
      model_param.head_width() / 2.0 +
      config_->static_decision_nudge_l_buffer();  // 需要停车的横向距离
  if (!util::sys_config_.with_trailer()) {
    model_param.set_trailer_base2tail(0.0);
  }
  if (frame_->last_drived_reference_line_info() == nullptr) {
    return false;
  }
  const auto last_path_data =
      frame_->last_drived_reference_line_info()->trajectory();
  for (const auto& path_point : last_path_data) {
    const auto t = path_point.relative_time();
    auto ego_sl = tutil::TransformFrenet::TransformToFrenet(
        reference_line_info_->origin_reference_line(), path_point);
    // 生成自车的
    const double ego_max_l = ego_sl.l() + model_param.head_width() / 2.0;
    const double ego_min_l = ego_sl.l() - model_param.head_width() / 2.0;
    const double ego_max_s = ego_sl.s() + model_param.head_base2front();
    const double ego_min_s = ego_sl.s() - model_param.trailer_base2tail();
    for (auto obstacle : reference_line_info_->obstacles().Items()) {
      if (obstacle->obj().is_static()) {
        continue;
      }
      if (obstacle->is_virtual()) {
        continue;
      }
      if (obstacle->lon_decision() != port::ObstacleDecision::kNotSet ||
          obstacle->lat_decision() != port::ObstacleDecision::kNotSet) {
        // 数据已经处理过了
        continue;
      }
      // 只进行横向的决策 left_nudge | right_nudge

      // 1. 生成轨迹的sl_boundary
      const auto sl_boundary = obstacle->sl_boundary();
      const auto width = (sl_boundary.max_l() - sl_boundary.min_l()) / 2.0;
      const auto height = (sl_boundary.max_s() - sl_boundary.min_s()) / 2.0;
      if (obstacle->obj().trajectory().empty()) {
        continue;
      }
      const auto obstacle_point = obstacle->obj().trajectory().EvaluateByT(t);
      auto obj_sl = tutil::TransformFrenet::TransformToFrenet(
          reference_line_info_->origin_reference_line(), obstacle_point);
      const auto obj_max_s = obj_sl.s() + height;
      const auto obj_min_s = obj_sl.s() - height;
      const auto obj_max_l = obj_sl.l() + width;
      const auto obj_min_l = obj_sl.l() - width;
      if (obj_min_s > ego_max_s + 10 || obj_max_s < ego_min_s - 10) {
        continue;
      } else if (config_->enable_nudge_decision()) {
        if (ego_min_l - obj_max_l < config_->lateral_ignore_buffer() &&
            ego_min_l - obj_max_l > config_->static_decision_nudge_l_buffer()) {
          // left_nudge
          auto object_decision = port::ObstacleDecision::kLeftNudge;
          obstacle->AddLateralDecision("PathDeciderTask/left-nudge",
                                       object_decision);
          TRUNK_LOG_INFO << "left nudge";
          break;

        } else if (obj_min_l - ego_max_l < config_->lateral_ignore_buffer() &&
                   obj_min_l - ego_max_l >
                       config_->static_decision_nudge_l_buffer()) {
          // right_nudge
          auto object_decision = port::ObstacleDecision::kRightNudge;
          obstacle->AddLateralDecision("PathDeciderTask/right-nudge",
                                       object_decision);
          TRUNK_LOG_INFO << "right nudge";
          break;
        }
      }
    }
  }
  return true;
}

// 生成停车指令, 假定速度规划不需要停车距离
bool PathDeciderTask::MergeWithMainStop(const port::Obstacle& obstacle) {
  auto reference_line = *(reference_line_info_->mutable_reference_line());
  // TODO: 检查参考线是否填充值，如果已经填充，可以删除掉
  if (reference_line.back().s() < 0.01) {
    tutil::FillPathS(reference_line);
  }
  // 生成障碍物的sl值
  // TODO: 目前最小停车距离根据参数来设置
  const auto ego_boundary = reference_line_info_->AdcSlBoundary();
  double stop_ref_s =
      obstacle.sl_boundary().min_s() - config_->min_radius_stop_distance();
  if (stop_ref_s < 0 || stop_ref_s > reference_line.back().s()) {
    TRUNK_LOG_INFO << "Ignore object: " << obstacle.obj().id()
                   << " fence route_s[" << stop_ref_s << "] not in range[0, "
                   << reference_line.back().s() << "]";
    return false;
  }

  const double kStopBuff = 1.0;
  stop_ref_s =
      std::fmax(stop_ref_s,
                ego_boundary.max_s() - kStopBuff);  // TODO: 检查自车边界的原因
  // 记录停车的最小距离
  if (stop_ref_s >= reference_line_info_->stop_reference_line_s()) {
    TRUNK_LOG_DEBUG << "stop point is further than current main stop point.";
    return false;
  }
  reference_line_info_->set_stop_reference_line_s(stop_ref_s);
  return true;
}

// TODO: 根据障碍物边界生成自车最近的位置，满足条件的s最小的值
port::PathSLPoint PathDeciderTask::GetEgoNearestPoint(
    const port::PathSLData& path, const port::SLBoundary& sl) {
  auto it_lower =
      std::lower_bound(path.begin(), path.end(), sl.min_s(),
                       [](const port::PathSLPoint p, const double s) {
                         return p.s() < s;
                       });  // lower_bound 和upper_bound中的参数顺序应该反过来
  if (it_lower == path.end()) {
    return path.back();
  }
  auto it_upper = std::upper_bound(
      it_lower, path.end(), sl.max_s(),
      [](const double s, const port::PathSLPoint p) { return s < p.s(); });
  double min_dist = std::numeric_limits<double>::max();
  auto min_it = it_upper;
  for (auto it = it_lower; it != it_upper; ++it) {
    if (it->l() >= sl.min_l() && it->l() <= sl.max_l()) {
      return *it;
    } else if (it->l() > sl.max_l()) {
      double diff = it->l() - sl.max_l();
      if (diff < min_dist) {
        min_dist = diff;
        min_it = it;
      }
    } else {
      double diff = sl.min_l() - it->l();
      if (diff < min_dist) {
        min_dist = diff;
        min_it = it;
      }
    }
  }
  return *min_it;
};
}  // namespace task
}  // namespace pnd
}  // namespace trunk
