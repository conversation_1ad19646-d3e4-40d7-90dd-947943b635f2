// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <absl/strings/str_format.h>
#include <absl/time/clock.h>
#include <trunk/common/common.h>

#include "config.h"
#include "log.h"
#include "param/qp_st_speed_param.h"
#include "param/sys_param.h"
#include "port/frame.h"
#include "port/st/speed_limit.h"
#include "solver/smoothing_spline/eigen_osqp_spline_1d_solver.h"
#include "solver/smoothing_spline/spline_1d_solver.h"
#include "task/st_graph/speed_limit_decider.h"
#include "task/st_graph/st_boundary_mapper.h"
#include "task/task.h"
#include "util/key.h"
#include "util/util.h"

namespace trunk {
namespace pnd {
namespace task {

class QpSplineStSpeedOptimizerTask : public Task {
 public:
  QpSplineStSpeedOptimizerTask();

  virtual ~QpSplineStSpeedOptimizerTask() = default;

  virtual tpnc::Status Execute(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info);

  const std::string& name() const { return name_; }

  void Init();

  bool Search(port::ReferenceLineInfo* const ref_line_info,
              const std::vector<port::STBoundary>& boundaries,
              const std::pair<double, double>& accel_bound);

  /**
   * @brief 添加约束
   * 1. 起点位置约束
   * 2. 起点速度约束
   * 3. 单调性约束(防止前后来回开)
   * 4. 加速度约束
   * 5. 边界约束(如果有障碍物，需要考虑是超车还是跟车)
   * */
  bool AddConstraint(const tport::TrajectoryPoint& init_point,
                     const port::SpeedLimit& speed_limit,
                     const std::vector<port::STBoundary>& boundries,
                     const std::pair<double, double>& accel_bound);

  bool GetSConstraintByTime(const std::vector<port::STBoundary>& boundaries,
                            const double time, const double total_path_s,
                            double* const s_upper_bound,
                            double* const s_lower_bound) const;

  bool AddKernel(const std::vector<port::STBoundary>& boundaries);
  bool AddCruiseReferenceLineKernel(const double weight);
  bool Solve();

  /**
   * @brief 与前车保持的安全距离采用rss模型的dmin算法
   * @details 假设前车以最大减速度突然减速，且自车还在加速，加速时间为系统延迟
   * 自车延迟过后再用最大减速度减速时的安全距离
   * */
  bool AddFollowReferenceLineKernel(
      const std::vector<port::STBoundary>& boundaries, const double weight);
  const port::SpeedData GetHistorySpeed() const;

  // 查找每个采样时刻t的速度上限
  bool EstimateSpeedUpperBound(const tport::TrajectoryPoint& init_point,
                               const port::SpeedLimit& speed_st_pts,
                               std::vector<double>* speed_upper_bound) const;

  bool AddYieldReferenceLineKernel(
      const std::vector<port::STBoundary>& boundaries, const double weight);

  bool AddDpStReferenceKernel(const double weight) const;

  /**
   * @brief rss model中车辆的安全距离
   * @details 默认前车突然以最大减速度刹车，自车正在加速过程中
   *  求此时自车安全减速需要保持的车距
   * */
  double RSSDistance(const double v_front);

 private:
  std::shared_ptr<solver::Spline1dSolver> spline_solver_ = nullptr;

 protected:
  std::string name_;
  int spline_order_;

  bool is_change_lane_ = false;

  std::vector<double> t_knots_;
  double t_knots_resolution_ = 0.0;
  double t_evaluated_resolution_ = 0.0;

  double total_path_length_ = 0.0;
  double lane_change_speed_relax_percentage_ = 0.05;

  // 采样时间
  std::vector<double> t_evaluated_;
  port::SpeedData last_speed_data_;
  std::vector<double> cruise_;
  port::SpeedData reference_dp_speed_points_;
  tport::TrajectoryPoint init_point_;
  port::SpeedLimit speed_limit_;

  QpSpeedOptimizerParam qp_speed_config_;
  StGraphParam st_graph_param_;
  SysParam sys_config_;
  std::vector<double> speed_vec_;
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
