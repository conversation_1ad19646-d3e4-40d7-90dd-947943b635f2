// Copyright 2023, trunk Inc. All rights reserved

#include "qp_spline_st_speed_optimizer.h"

namespace trunk {
namespace pnd {
namespace task {

REGISTER(trunk::pnd::task, Task, QpSplineStSpeedOptimizer);

namespace {
const double trajectory_time_min_interval = 0.2;
}  // namespace

QpSplineStSpeedOptimizerTask::QpSplineStSpeedOptimizerTask()
    : Task("qp st speed optimizer task"),
      spline_solver_(std::make_shared<solver::EigenOsqpSpline1dSolver>(
          std::vector<double>(), 0)) {
  Init();
}

void QpSplineStSpeedOptimizerTask::Init() {
  st_graph_param_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()->GetConfig<StGraphParam>(
          "st_graph");
  qp_speed_config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()
          ->GetConfig<QpSpeedOptimizerParam>("qp_speed_optimizer");
  sys_config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()->GetConfig<SysParam>("sys");
  lane_change_speed_relax_percentage_ = qp_speed_config_.lc_relax_percent();
  t_knots_resolution_ =
      (qp_speed_config_.total_time() /
       qp_speed_config_.qp_spline_config().number_of_discrete_graph_t());
  spline_order_ = qp_speed_config_.qp_spline_config().spline_order();
  // init knots
  double curr_t = 0.0;
  uint32_t num_spline =
      qp_speed_config_.qp_spline_config().number_of_discrete_graph_t() - 1;
  for (uint32_t i = 0; i <= num_spline; ++i) {
    t_knots_.push_back(curr_t);
    curr_t += t_knots_resolution_;
  }

  uint32_t num_evaluated_t = 10 * num_spline + 1;

  // init evaluated t positions
  curr_t = 0.0;
  t_evaluated_resolution_ =
      qp_speed_config_.total_time() / (num_evaluated_t - 1);
  for (uint32_t i = 0; i < num_evaluated_t; ++i) {
    t_evaluated_.push_back(curr_t);
    curr_t += t_evaluated_resolution_;
  }
  last_speed_data_.clear();
}

tpnc::Status QpSplineStSpeedOptimizerTask::Execute(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  is_change_lane_ = reference_line_info->overtake_flag();
  // 遍历每一个reference_lines_info
  std::pair<double, double> accel_bound{
      qp_speed_config_.preferred_min_deceleration(),
      qp_speed_config_.preferred_max_acceleration()};
  init_point_ = frame->init_point();
  // 1. 获取障碍物boundary
  std::vector<port::STBoundary> speed_st_boundaries;

  StBoundaryMapper st_boundary_mapper(
      reference_line_info->AdcSlBoundary(), *reference_line_info,
      reference_line_info->path_data(), qp_speed_config_.total_path_length(),
      qp_speed_config_.total_time());
  st_boundary_mapper.CreateStBoundary();
  speed_vec_.clear();
  for (const auto& obstacle : reference_line_info->obstacles().Items()) {
    speed_st_boundaries.emplace_back(obstacle->st_boundary());
    speed_vec_.emplace_back(obstacle->obj().velocity());
  }
  speed_limit_.Clear();
  SpeedLimitDecider speed_limit_decider(reference_line_info->AdcSlBoundary(),
                                        st_graph_param_, *reference_line_info,
                                        frame->current_reference_line_info(),
                                        frame->tnp_speed_limit());
  speed_limit_decider.GetSpeedLimits(reference_line_info->obstacles(),
                                     &speed_limit_);
  if (!Search(reference_line_info, speed_st_boundaries, accel_bound)) {
    TRUNK_LOG_ERROR << "Failed to solve with ideal acceleration conditions. "
                       "Use secondary choice instead.";
    std::pair<double, double> accel_bound{qp_speed_config_.min_deceleration(),
                                          qp_speed_config_.max_acceleration()};
    if (!Search(reference_line_info, speed_st_boundaries, accel_bound)) {
      std::string msg("Spline QP speed solver Failed.");
      return tpnc::Status(tpnc::ErrorCode::PLANNING_UNSOLVABLE, msg);
    }
  }
  if (!reference_line_info->CombinePathAndSpeedProfile(
          frame->init_point().relative_time(), frame->init_point().s())) {
    reference_line_info->set_is_drivable({false, ""});
    TRUNK_LOG_ERROR << "Fail to aggregate planning trajectory";
    return tpnc::Status::ERROR("Fail to aggregate planning trajectory");
  }
  return tpnc::Status::OK();
}

bool QpSplineStSpeedOptimizerTask::Search(
    port::ReferenceLineInfo* const ref_line_info,
    const std::vector<port::STBoundary>& boundaries,
    const std::pair<double, double>& accel_bound) {
  constexpr double kBounadryEpsilon = 1e-2;
  spline_solver_->Reset(t_knots_, spline_order_);

  for (auto boundary : boundaries) {
    if (boundary.IsPointInBoundary(port::STPoint(0.0, 0.0)) ||
        (std::fabs(boundary.min_t()) < kBounadryEpsilon &&
         std::fabs(boundary.min_s()) < kBounadryEpsilon)) {
      const double t_output_resolution = trajectory_time_min_interval;
      double time = 0.0;
      while (time < qp_speed_config_.total_time() + t_output_resolution) {
        port::SpeedSTPoint speed_data(0.0, time, 0.0, 0.0, 0.0);
        // 障碍物碰撞直接填0
        ref_line_info->mutable_speed_data()->emplace_back(speed_data);
        time += t_output_resolution;
      }
      TRUNK_LOG_INFO << "Base pt in Boundary or mint_t = min_s = 0!";
      return true;
    }
  }

  cruise_.clear();
  reference_dp_speed_points_ = ref_line_info->speed_data();
  TRUNK_LOG_INFO << "Velocity: " << init_point_.v();
  if (!AddConstraint(init_point_, speed_limit_, boundaries, accel_bound)) {
    const std::string msg = "Add constraint failed!";
    TRUNK_LOG_ERROR << msg;
    return false;
  }

  if (!AddKernel(boundaries)) {
    const std::string msg = "Add kernel failed!";
    TRUNK_LOG_ERROR << msg;
    return false;
  }

  absl::Duration solve_time;
  auto last_timestamp = absl::Now();
  if (!Solve()) {
    TRUNK_LOG_ERROR << "Solve qp problem failed!";
    return false;
  }
  solve_time += (absl::Now() - last_timestamp);
  TRUNK_LOG_INFO << absl::StrFormat(
      "QpSplineStSpeedOptimizerTask time to solve is %.3f ms.",
      absl::ToDoubleMilliseconds(solve_time));

  // extract output
  ref_line_info->mutable_speed_data()->clear();
  const solver::Spline1d& spline = spline_solver_->spline();

  const double t_output_resolution = trajectory_time_min_interval;
  double time = 0.0;
  double total_time = qp_speed_config_.total_time();

  // 按照间隔提取
  while (time < total_time + t_output_resolution) {
    double s = spline(time);
    double v = std::max(0.0, spline.Derivative(time));
    double a = spline.SecondOrderDerivative(time);
    double da = spline.ThirdOrderDerivative(time);
    ref_line_info->mutable_speed_data()->AppendSpeedSTPoint(s, time, v, a, da);
    time += t_output_resolution;
    TRUNK_LOG_DEBUG << absl::StrFormat("t: %.2f, s: %.2f, v: %.2f, a: %.2f",
                                       time, s, v, a);
  }
  last_speed_data_ = *ref_line_info->mutable_speed_data();
  return true;
}

bool QpSplineStSpeedOptimizerTask::AddConstraint(
    const tport::TrajectoryPoint& init_point,
    const port::SpeedLimit& speed_limit,
    const std::vector<port::STBoundary>& boundaries,
    const std::pair<double, double>& accel_bound) {
  solver::Spline1dConstraint* constraint =
      spline_solver_->mutable_spline_constraint();

  if (!constraint->AddPointConstraint(0.0, 0.0)) {
    TRUNK_LOG_ERROR << "add st start point constraint failed";
    return false;
  }
  if (!constraint->AddPointDerivativeConstraint(0.0, init_point_.v())) {
    TRUNK_LOG_ERROR << "add st start point velocity constraint failed!";
    return false;
  }

  // monotone constraint
  if (!constraint->AddMonotoneInequalityConstraint(t_evaluated_)) {
    TRUNK_LOG_ERROR << "add monotone inequality constraint failed!";
    return false;
  }

  // smoothness constraint
  if (!constraint->AddThirdDerivativeSmoothConstraint()) {
    const std::string msg = "add smoothness joint constraint failed!";
    TRUNK_LOG_ERROR << msg;
    return false;
  }

  // boundary constraint
  std::vector<double> s_upper_bound;
  std::vector<double> s_lower_bound;

  for (const double curr_t : t_evaluated_) {
    double lower_s = 0.0;
    double upper_s = 0.0;
    GetSConstraintByTime(boundaries, curr_t,
                         qp_speed_config_.total_path_length(), &upper_s,
                         &lower_s);
    s_upper_bound.push_back(upper_s);
    s_lower_bound.push_back(lower_s);
  }

  assert(t_evaluated_.size() == s_lower_bound.size());
  assert(t_evaluated_.size() == s_upper_bound.size());
  // 每一个时间 t 都对应着s上下限
  if (!constraint->AddBoundary(t_evaluated_, s_lower_bound, s_upper_bound)) {
    const std::string msg = "Fail to apply distance constraints.";
    TRUNK_LOG_ERROR << msg;
    return false;
  }

  // 速度约束
  std::vector<double> speed_upper_bound(t_evaluated_.size(), 5.0);
  if (!EstimateSpeedUpperBound(init_point, speed_limit, &speed_upper_bound)) {
    TRUNK_LOG_ERROR << "Fail to estimate speed upper constraints.";
    return false;
  }
  // 默认下限是0
  std::vector<double> speed_lower_bound(t_evaluated_.size(), 0.0);
  assert(t_evaluated_.size() == speed_upper_bound.size());
  assert(t_evaluated_.size() == speed_lower_bound.size());
  if (!constraint->AddDerivativeBoundary(t_evaluated_, speed_lower_bound,
                                         speed_upper_bound)) {
    const std::string msg = "Fail to apply speed constraints.";
    TRUNK_LOG_ERROR << msg;
    return false;
  }
  for (size_t i = 0; i < t_evaluated_.size(); ++i) {
    TRUNK_LOG_DEBUG << absl::StrFormat(
        "t_evaluated: %.2f, speed: [%.2f, %.2f], s: [%.2f, %.2f], "
        "s最低均速: %.2f, s最高均速: %.2f",
        t_evaluated_[i], speed_lower_bound[i], speed_upper_bound[i],
        s_lower_bound[i], s_upper_bound[i],
        s_lower_bound[i] / (t_evaluated_[i] + 0.001),
        s_upper_bound[i] / (t_evaluated_[i] + 0.001) > 35.0
            ? 35.0
            : s_upper_bound[i] / (t_evaluated_[i] + 0.001));
  }

  // 加速度限制
  std::vector<double> accel_lower_bound(t_evaluated_.size(), accel_bound.first);
  std::vector<double> accel_upper_bound(t_evaluated_.size(),
                                        accel_bound.second);

  assert(t_evaluated_.size() == accel_lower_bound.size());
  assert(t_evaluated_.size() == accel_upper_bound.size());
  if (!constraint->AddSecondDerivativeBoundary(t_evaluated_, accel_lower_bound,
                                               accel_upper_bound)) {
    const std::string msg = "Fail to apply acceleration constraints.";
    return false;
  }

  return true;
}

bool QpSplineStSpeedOptimizerTask::AddKernel(
    const std::vector<port::STBoundary>& boundaries) {
  solver::Spline1dKernel* spline_kernel =
      spline_solver_->mutable_spline_kernel();
  auto qp_conf = qp_speed_config_.qp_spline_config();

  if (qp_conf.accel_kernel_weight() > 0) {
    spline_kernel->AddSecondOrderDerivativeMatrix(
        qp_conf.accel_kernel_weight());
  }

  if (qp_conf.jerk_kernel_weight() > 0) {
    spline_kernel->AddThirdOrderDerivativeMatrix(qp_conf.jerk_kernel_weight());
  }

  if (!AddCruiseReferenceLineKernel(qp_conf.cruise_weight())) {
    TRUNK_LOG_ERROR << "QpSplineStSpeedOptimizerTask::AddKernel";
    return false;
  }

  if (!AddFollowReferenceLineKernel(boundaries, qp_conf.follow_weight())) {
    TRUNK_LOG_ERROR << "QpSplineStGraph::AddKernel";
    return false;
  }

  if (!AddYieldReferenceLineKernel(boundaries, qp_conf.yield_weight())) {
    TRUNK_LOG_ERROR << "QpSplineStGraph::AddKernel";
    return false;
  }
  if (!AddDpStReferenceKernel(qp_conf.dp_st_reference_weight())) {
    TRUNK_LOG_ERROR << "QpSplineStGraph::AddKernel";
    return false;
  }

  // init point jerk continuous kernel

  (*spline_kernel->mutable_kernel_matrix())(2, 2) +=
      2.0 * 4.0 * qp_speed_config_.qp_spline_config().init_jerk_kernel_weight();
  (*spline_kernel->mutable_offset())(2, 0) +=
      -4.0 * init_point_.a() *
      qp_speed_config_.qp_spline_config().init_jerk_kernel_weight();

  spline_kernel->AddRegularization(
      qp_speed_config_.qp_spline_config().regularization_weight());

  return true;
}

bool QpSplineStSpeedOptimizerTask::Solve() { return spline_solver_->Solve(); }

bool QpSplineStSpeedOptimizerTask::AddCruiseReferenceLineKernel(
    const double weight) {
  auto* spline_kernel = spline_solver_->mutable_spline_kernel();
  double dist_ref = qp_speed_config_.total_path_length();
  for (uint32_t i = 0; i < t_evaluated_.size(); ++i) {
    cruise_.push_back(dist_ref);
  }
  assert(t_evaluated_.size() == cruise_.size());

  if (t_evaluated_.size() > 0) {
    spline_kernel->AddReferenceLineKernelMatrix(
        t_evaluated_, cruise_,
        weight * qp_speed_config_.total_time() /
            static_cast<double>(t_evaluated_.size()));
  }

  return true;
}

double QpSplineStSpeedOptimizerTask::RSSDistance(const double v_front) {
  // const double ego_base2front =
  // tpnc::Singleton<tmodel::Truck>::GetInstance()->geometry().base2front();
  double delay_t = 1.0 / sys_config_.main_loop_frequency();
  return std::max(
      0.0,
      init_point_.v() * delay_t +
          std::pow(
              init_point_.v() + delay_t * qp_speed_config_.max_acceleration(),
              2) /
              2.0 / std::fabs(qp_speed_config_.preferred_min_deceleration()) -
          v_front * v_front / 2.0 /
              std::fabs(qp_speed_config_.front_vehicle_max_deceleration()));
}

bool QpSplineStSpeedOptimizerTask::AddFollowReferenceLineKernel(
    const std::vector<port::STBoundary>& boundaries, const double weight) {
  auto* spline_kernel = spline_solver_->mutable_spline_kernel();
  std::vector<double> ref_s;
  std::vector<double> filtered_evaluate_t;
  const double ego_base2front =
      tpnc::Singleton<tmodel::Truck>::GetInstance()->geometry().base2front();
  for (size_t i = 0; i < t_evaluated_.size(); ++i) {
    const double curr_t = t_evaluated_[i];
    double s_min = std::numeric_limits<double>::infinity();
    bool success = false;
    for (size_t idx = 0; idx < boundaries.size(); ++idx) {
      port::STBoundary boundary = boundaries[idx];
      if (boundary.boundary_type() != port::STBoundary::BoundaryType::FOLLOW) {
        continue;
      }
      if (curr_t < boundary.min_t() || curr_t > boundary.max_t()) {
        continue;
      }
      double d_min =
          qp_speed_config_.qp_spline_config().follow_drag_distance() +
          ego_base2front + RSSDistance(speed_vec_[idx]);
      double s_upper = 0.0;
      double s_lower = 0.0;
      // 找出每一个时间对应的上下界
      if (boundary.GetUnblockBoundarySRange(curr_t, &s_upper, &s_lower)) {
        success = true;
        s_min = std::min(s_min, std::max(s_upper - d_min, 0.0));
      }
    }
    if (success && s_min < cruise_[i]) {
      filtered_evaluate_t.push_back(curr_t);
      ref_s.push_back(s_min);
    }
  }
  assert(filtered_evaluate_t.size() == ref_s.size());

  if (!ref_s.empty()) {
    spline_kernel->AddReferenceLineKernelMatrix(
        filtered_evaluate_t, ref_s,
        weight * qp_speed_config_.total_time() /
            static_cast<double>(t_evaluated_.size()));
  }

  return true;
}

bool QpSplineStSpeedOptimizerTask::AddYieldReferenceLineKernel(
    const std::vector<port::STBoundary>& boundaries, const double weight) {
  auto* spline_kernel = spline_solver_->mutable_spline_kernel();
  std::vector<double> ref_s;
  std::vector<double> filtered_evaluate_t;
  for (size_t i = 0; i < t_evaluated_.size(); ++i) {
    const double curr_t = t_evaluated_[i];
    double s_min = std::numeric_limits<double>::infinity();
    bool success = false;
    for (const auto& boundary : boundaries) {
      if (boundary.boundary_type() != port::STBoundary::BoundaryType::YIELD) {
        continue;
      }
      if (curr_t < boundary.min_t() || curr_t > boundary.max_t()) {
        continue;
      }
      double s_upper = 0.0;
      double s_lower = 0.0;

      if (boundary.GetUnblockBoundarySRange(curr_t, &s_upper, &s_lower)) {
        success = true;
        double yield_drag_distance = 20.0;
        double characteristic_length = 1.0;
        s_min = std::min(s_min,
                         s_upper - characteristic_length - yield_drag_distance);
      }
    }
    if (success && s_min < cruise_[i]) {
      filtered_evaluate_t.push_back(curr_t);
      ref_s.push_back(s_min);
    }
  }
  assert(filtered_evaluate_t.size() == ref_s.size());

  if (!ref_s.empty()) {
    spline_kernel->AddReferenceLineKernelMatrix(
        filtered_evaluate_t, ref_s,
        weight * qp_speed_config_.total_time() /
            static_cast<double>(t_evaluated_.size()));
  }

  return true;
}

bool QpSplineStSpeedOptimizerTask::AddDpStReferenceKernel(
    const double weight) const {
  std::vector<double> t_pos;
  std::vector<double> s_pos;
  for (auto point : reference_dp_speed_points_) {
    t_pos.push_back(point.t());
    s_pos.push_back(point.s());
  }
  auto* spline_kernel = spline_solver_->mutable_spline_kernel();
  if (!t_pos.empty()) {
    spline_kernel->AddReferenceLineKernelMatrix(
        t_pos, s_pos,
        weight * qp_speed_config_.total_time() /
            static_cast<double>(t_pos.size()));
  }
  return true;
}

bool QpSplineStSpeedOptimizerTask::GetSConstraintByTime(
    const std::vector<port::STBoundary>& boundaries, const double time,
    const double total_path_s, double* const s_upper_bound,
    double* const s_lower_bound) const {
  *s_upper_bound = total_path_s;

  for (const port::STBoundary& boundary : boundaries) {
    double s_upper = 200.0;
    double s_lower = 0.0;

    if (!boundary.GetUnblockBoundarySRange(time, &s_upper, &s_lower)) {
      continue;
    }
    if (boundary.boundary_type() == port::STBoundary::BoundaryType::STOP ||
        boundary.boundary_type() == port::STBoundary::BoundaryType::FOLLOW ||
        boundary.boundary_type() == port::STBoundary::BoundaryType::YIELD) {
      *s_upper_bound = std::fmin(*s_upper_bound, s_upper);
    } else if (boundary.boundary_type() ==
               port::STBoundary::BoundaryType::OVERTAKE) {
      *s_lower_bound = std::fmax(*s_lower_bound, s_lower);
    } else {
      // TRUNK_LOG_WARN << "未处理的boundary type: "
      //                 << boundary.TypeName(boundary.boundary_type());
    }
  }
  return true;
}

const port::SpeedData QpSplineStSpeedOptimizerTask::GetHistorySpeed() const {
  if (util::last_frame_ == nullptr) {
    TRUNK_LOG_WARN << "last frame is empty";
    return port::SpeedData();
  }
  const port::ReferenceLineInfo* last_reference_line_info =
      util::last_frame_->drive_reference_line_info();
  if (!last_reference_line_info) {
    TRUNK_LOG_DEBUG << "last reference line info is empty";
    return port::SpeedData();
  }
  return last_reference_line_info->speed_data();
}

bool QpSplineStSpeedOptimizerTask::EstimateSpeedUpperBound(
    const tport::TrajectoryPoint& init_point,
    const port::SpeedLimit& speed_limit,
    std::vector<double>* speed_upper_bound) const {
  if (speed_upper_bound == nullptr) {
    return false;
  }

  speed_upper_bound->clear();

  // use v to estimate position: not accurate, but feasible in cyclic
  // processing. We can do the following process multiple times and use
  // previous cycle's results for better estimation.
  const double v = init_point.v();
  auto last_speed_data = GetHistorySpeed();

  if (static_cast<double>(t_evaluated_.size() +
                          speed_limit.speed_limit_points().size()) <
      static_cast<double>(t_evaluated_.size()) *
          std::log(
              static_cast<double>(speed_limit.speed_limit_points().size()))) {
    uint32_t i = 0;
    uint32_t j = 0;
    while (i < t_evaluated_.size() &&
           j + 1 < speed_limit.speed_limit_points().size()) {
      // 应该是根据当前速度预估t时刻到达的s，或者根据上一帧的规划速度预估t时刻的s
      double distance = v * t_evaluated_[i];
      if (!last_speed_data.empty() && distance < last_speed_data.back().s()) {
        port::SpeedSTPoint p;
        last_speed_data.EvaluateByTime(t_evaluated_[i], &p);
        distance = p.s();
      }
      constexpr double kDistanceEpsilon = 1e-6;
      if (fabs(distance - speed_limit.speed_limit_points()[j].first) <
          kDistanceEpsilon) {
        speed_upper_bound->push_back(
            speed_limit.speed_limit_points()[j].second);
        ++i;
      } else if (distance < speed_limit.speed_limit_points()[j].first) {
        ++i;
      } else if (distance <= speed_limit.speed_limit_points()[j + 1].first) {
        speed_upper_bound->push_back(speed_limit.GetSpeedLimitByS(distance));
        ++i;
      } else {
        ++j;
      }
    }

    for (size_t k = speed_upper_bound->size(); k < t_evaluated_.size(); ++k) {
      speed_upper_bound->push_back(
          tutil::Kmh2Ms(sys_config_.config_speed_limit()));
    }
  } else {
    auto cmp = [](const std::pair<double, double>& p1, const double s) {
      return p1.first < s;
    };

    const auto& speed_limit_points = speed_limit.speed_limit_points();
    for (const double t : t_evaluated_) {
      double s = v * t;
      if (!last_speed_data.empty() && s < last_speed_data.back().s()) {
        port::SpeedSTPoint p;
        last_speed_data.EvaluateByTime(t, &p);
        s = p.s();
      }

      // NOTICE: we are using binary search here based on two assumptions:
      // (1) The s in speed_limit_points increase monotonically.
      // (2) The evaluated_t_.size() << number of speed_limit_points.size()
      //
      // If either of the two assumption is failed, a new algorithm must be
      // used to replace the binary search.

      const auto& it = std::lower_bound(speed_limit_points.begin(),
                                        speed_limit_points.end(), s, cmp);
      if (it != speed_limit_points.end()) {
        speed_upper_bound->push_back(it->second);
      } else {
        speed_upper_bound->push_back(speed_limit_points.back().second);
      }
    }
  }

  if (is_change_lane_) {
    for (uint32_t k = 0; k < t_evaluated_.size(); ++k) {
      speed_upper_bound->at(k) *= (1.0 + lane_change_speed_relax_percentage_);
    }
  }

  const double kTimeBuffer = 3.0;
  const double kSpeedBuffer = 0.1 + init_point.v() - speed_upper_bound->front();
  for (uint32_t k = 0; k < t_evaluated_.size() && t_evaluated_[k] < kTimeBuffer;
       ++k) {
    speed_upper_bound->at(k) = std::fmax(
        speed_upper_bound->at(k) +
            kSpeedBuffer * (kTimeBuffer - t_evaluated_[k]) / kTimeBuffer,
        speed_upper_bound->at(k));
  }

  return true;
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
