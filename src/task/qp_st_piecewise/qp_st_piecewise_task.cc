// Copyright 2024, trunk Inc. All right reserved.

#include "qp_st_piecewise_task.h"

#include <vector>

#include "log.h"
#include "math/curve/piecewise_jerk_1d.h"
#include "port/st/speed_data.h"
#include "task/st_graph/speed_limit_decider.h"
#include "task/st_graph/st_boundary_mapper.h"
#include "util/key.h"
#include "util/util.h"

namespace trunk {
namespace pnd {
namespace task {

REGISTER(trunk::pnd::task, Task, QpStPiecewise);

QpStPiecewiseTask::QpStPiecewiseTask() : Task("qp st piecewise task") {}

tpnc::Status QpStPiecewiseTask::Execute(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  this->reference_line_info_ = reference_line_info;
  const auto& norm_path = reference_line_info->path_data().qp_norm_path();
  const auto& dp_speed = reference_line_info->speed_data();
  auto init_point = frame->init_point();
  init_point.set_s(tutil::TransformFrenet::TransformToFrenet(
                       reference_line_info->reference_line(), init_point)
                       .s());
  if (norm_path.back().s() - norm_path.front().s() < 5.0) {
    TRUNK_LOG_WARN << "dp_sl_path is too short.";
    reference_line_info_->set_is_drivable({false, "qp_st_piecewise failure"});
    return tpnc::Status::ERROR();
  }
  // 关键参数计算
  size_t optimized_size = config_.total_time() / config_.dt() + 1;
  if (optimized_size < 2) {
    return tpnc::Status::ERROR("the length of DP path is too short");
  }
  util::uniform_slice(0.0, config_.total_time(), optimized_size - 1,
                      t_evaluated_);

  StBoundaryMapper st_boundary_mapper(
      reference_line_info->AdcSlBoundary(), *reference_line_info,
      reference_line_info->path_data(), config_.total_length(),
      config_.total_time());
  st_boundary_mapper.CreateStBoundary();

  const double dt_square = config_.dt() * config_.dt();

  // kernel
  const double hessian_0order_base =
      config_.weight_dp()[0] + config_.weight_ref()[0];
  const double hessian_1order_base = config_.weight_dp()[1] +
                                     config_.weight_ref()[1] +
                                     config_.weight_norm()[1];
  const double hessian_2order_base =
      config_.weight_dp()[2] + config_.weight_ref()[2] +
      config_.weight_norm()[2] + 2.0 * config_.weight_norm()[3] / dt_square;
  const double hessian_cross_coefficient =
      -config_.weight_norm()[3] / dt_square;

  // constrains
  std::pair<std::vector<double>, std::vector<double>> boundary_0order;
  std::pair<std::vector<double>, std::vector<double>> boundary_1order;
  std::pair<double, double> boundary_2order{config_.max_dec(),
                                            config_.max_acc()};
  SpeedLimitDecider speed_limit_decider(
      reference_line_info->AdcSlBoundary(),
      tpnc::Singleton<tpnc::Config>::GetInstance()->GetConfig<StGraphParam>(
          "st_graph"),
      *reference_line_info, frame->current_reference_line_info(),
      frame->tnp_speed_limit());
  port::SpeedLimit speed_limit;
  speed_limit_decider.GetSpeedLimits(reference_line_info->obstacles(),
                                     &speed_limit);
  if (!EstimateSpeedUpperBound(init_point, speed_limit,
                               &boundary_1order.second)) {
    return tpnc::Status::ERROR("Fail to estimate speed upper constraints.");
  }
  boundary_1order.first.resize(boundary_1order.second.size(), 0.0);
  port::SpeedData ref_st;
  ref_st.reserve(optimized_size);
  ref_st.emplace_back(0.0, init_point.s(), init_point.v(), init_point.a(),
                      init_point.da());
  const double ego_base2front =
      tpnc::Singleton<tmodel::Truck>::GetInstance()->geometry().base2front();
  for (size_t i = 0; i < t_evaluated_.size(); ++i) {
    const double curr_t = t_evaluated_[i];
    double s_min = std::numeric_limits<double>::infinity();
    bool success = false;
    for (const auto& obstacle : reference_line_info_->obstacles().Items()) {
      const auto& boundary = obstacle->st_boundary();
      if (boundary.boundary_type() != port::STBoundary::BoundaryType::FOLLOW) {
        continue;
      }
      if (curr_t < boundary.min_t() || curr_t > boundary.max_t()) {
        continue;
      }
      double d_min = config_.min_safe_distance() + ego_base2front +
                     RSSDistance(init_point.v(), obstacle->obj().velocity());
      double s_upper = 0.0;
      double s_lower = 0.0;
      // 找出每一个时间对应的上下界
      if (boundary.GetUnblockBoundarySRange(curr_t, &s_upper, &s_lower)) {
        success = true;
        s_min = std::min(s_min, std::max(s_upper - d_min, 0.0));
      }
    }
    if (i > 0) {
      double s = ref_st.back().s() + boundary_1order.second[i] * config_.dt();
      if (success) {
        s = std::min(s, s_min);
      }
      ref_st.emplace_back(std::min(s, s_min), curr_t, boundary_1order.second[i],
                          0.0, 0.0);
    }
  }
  for (int i = 0; i < optimized_size; ++i) {
    double lower_s = 0.0;
    double upper_s = 0.0;
    GetSConstraintByTime(t_evaluated_[i], upper_s, lower_s);
    boundary_0order.second.push_back(upper_s);
    boundary_0order.first.push_back(lower_s);
  }

  // gradient
  std::vector<double> gradient_0order_base;
  std::vector<double> gradient_1order_base;
  std::vector<double> gradient_2order_base;
  gradient_0order_base.reserve(optimized_size);
  for (int i = 0; i < optimized_size; ++i) {
    const auto dp_data = dp_speed.EvaluateByTime(t_evaluated_[i]);
    const auto& ref_data = ref_st[i];
    gradient_0order_base.emplace_back(-config_.weight_dp()[0] * dp_data.s() -
                                      config_.weight_ref()[0] * ref_data.s());
    gradient_1order_base.emplace_back(-config_.weight_dp()[1] * dp_data.v() -
                                      config_.weight_ref()[1] * ref_data.v());
    gradient_2order_base.emplace_back(-config_.weight_dp()[2] * dp_data.a() -
                                      config_.weight_ref()[2] * ref_data.a());
  }

  // QP 定义
  Eigen::MatrixXd hessian =
      Eigen::MatrixXd::Zero(3 * optimized_size, 3 * optimized_size);
  Eigen::VectorXd gradient = Eigen::VectorXd::Zero(3 * optimized_size);
  Eigen::MatrixXd affine =
      Eigen::MatrixXd::Zero(6 * optimized_size, 3 * optimized_size);
  Eigen::VectorXd boundary_lower = Eigen::VectorXd::Zero(6 * optimized_size);
  Eigen::VectorXd boundary_upper = Eigen::VectorXd::Zero(6 * optimized_size);

  // 赋值
  for (size_t i = 0; i < optimized_size; i++) {
    hessian(i, i) = hessian_0order_base;
    hessian(optimized_size + i, optimized_size + i) = hessian_1order_base;
    hessian(2 * optimized_size + i, 2 * optimized_size + i) =
        hessian_2order_base;
    gradient(i) = gradient_0order_base[i];
    gradient(optimized_size + i) = gradient_1order_base[i];
    gradient(2 * optimized_size + i) = gradient_2order_base[i];
    // boundary
    affine(i, i) = 1.0;
    affine(optimized_size + i, optimized_size + i) = 1.0;
    affine(2 * optimized_size + i, 2 * optimized_size + i) = 1.0;
    // continuity constrains
    boundary_lower(i) = boundary_0order.first.at(i);
    boundary_upper(i) = boundary_0order.second.at(i);
    boundary_lower(optimized_size + i) = boundary_1order.first[i];
    boundary_upper(optimized_size + i) = boundary_1order.second[i];
    boundary_lower(2 * optimized_size + i) = boundary_2order.first;
    boundary_upper(2 * optimized_size + i) = boundary_2order.second;
  }
  // modified
  // 0order
  hessian(optimized_size - 1, optimized_size - 1) += config_.weight_end()[0];
  // 1order
  hessian(2 * optimized_size - 1, 2 * optimized_size - 1) +=
      config_.weight_end()[1];
  // 2order
  hessian(2 * optimized_size, 2 * optimized_size) -=
      config_.weight_norm()[3] / dt_square;
  hessian(3 * optimized_size - 1, 3 * optimized_size - 1) +=
      -config_.weight_norm()[3] / dt_square + config_.weight_end()[2];

  gradient(optimized_size - 1) -= config_.weight_end()[0] * ref_st.back().s();
  gradient(2 * optimized_size - 1) -=
      config_.weight_end()[1] * ref_st.back().v();
  gradient(3 * optimized_size - 1) -=
      config_.weight_end()[2] * ref_st.back().a();
  // hessian : cross term
  for (size_t i = 2 * optimized_size + 1; i < 3 * optimized_size; i++) {
    hessian(i, i - 1) = hessian_cross_coefficient;
    hessian(i - 1, i) = hessian_cross_coefficient;
  }
  // affine : init state
  affine(3 * optimized_size, 0) = 1.0;
  boundary_lower(3 * optimized_size) = init_point.s();
  boundary_upper(3 * optimized_size) = init_point.s();
  affine(4 * optimized_size, optimized_size) = 1.0;
  boundary_lower(4 * optimized_size) = init_point.v();
  boundary_upper(4 * optimized_size) = init_point.v();
  affine(5 * optimized_size, 2 * optimized_size) = 1.0;
  boundary_lower(5 * optimized_size) = init_point.a();
  boundary_upper(5 * optimized_size) = init_point.a();
  // affine :  continuity constrains
  for (int i = 1; i < optimized_size; i++) {
    // acceleration : acc(i) = acc(i-1) + ∫jerk ds = acc(i-1) + jerk * ds
    affine(5 * optimized_size + i, 2 * optimized_size + i) = -1.0;
    affine(5 * optimized_size + i, 2 * optimized_size + i - 1) = 1.0;
    boundary_lower(5 * optimized_size + i) = config_.min_jerk() * config_.dt();
    boundary_upper(5 * optimized_size + i) = config_.max_jerk() * config_.dt();
    // velocity : velo(i) = velo(i-1) + ∫acc ds
    // velocity : = velo(i-1) + acc(i-1) * ds + jerk * ds^2 / 2
    // velocity : = velo(i-1) + ds * (2 acc(i-1) + jerk / ds) / 2
    // velocity : = velo(i-1) + ds * (acc(i-1) + acc(i)) / 2
    affine(4 * optimized_size + i, 2 * optimized_size + i) = 0.5 * config_.dt();
    affine(4 * optimized_size + i, 2 * optimized_size + i - 1) =
        0.5 * config_.dt();
    affine(4 * optimized_size + i, optimized_size + i) = -1.0;
    affine(4 * optimized_size + i, optimized_size + i - 1) = 1.0;
    boundary_lower(4 * optimized_size + i) = -0.0;
    boundary_upper(4 * optimized_size + i) = 0.0;
    // position : posi(i) = posi(i-1) + ∫velo ds
    // position : = posi(i-1) + velo(i-1) * ds + acc(i-1) * ds^2 / 2 + jerk *
    // ds^3 / 6
    // position : = posi(i-1) + velo(i-1) * ds + (3 * acc(i-1) + jerk *
    //  ds) * ds^2 / 6
    // position : = posi(i-1) + velo(i-1) * ds + (2 * acc(i-1) +
    //   acc(i)) * ds^2 / 6
    affine(3 * optimized_size + i, 2 * optimized_size + i) = dt_square / 6.0;
    affine(3 * optimized_size + i, 2 * optimized_size + i - 1) =
        dt_square / 3.0;
    affine(3 * optimized_size + i, optimized_size + i - 1) = config_.dt();
    affine(3 * optimized_size + i, i) = -1.0;
    affine(3 * optimized_size + i, i - 1) = 1.0;
    boundary_lower(3 * optimized_size + i) = -0.0;
    boundary_upper(3 * optimized_size + i) = 0.0;
  }

  Eigen::SparseMatrix<double> sparse_hessian = hessian.sparseView();
  Eigen::SparseMatrix<double> sparse_affine = affine.sparseView();
  // OSQP
  OsqpEigen::Solver piecewise_jerk_path;
  piecewise_jerk_path.settings()->setVerbosity(false);
  piecewise_jerk_path.settings()->setWarmStart(true);
  // set the initial data of the QP piecewise_jerk_path
  piecewise_jerk_path.data()->setNumberOfVariables(3 * optimized_size);
  piecewise_jerk_path.data()->setNumberOfConstraints(6 * optimized_size);
  if (!piecewise_jerk_path.data()->setHessianMatrix(sparse_hessian))
    return tpnc::Status::ERROR("speed QP error: set hessian failure");
  if (!piecewise_jerk_path.data()->setGradient(gradient))
    return tpnc::Status::ERROR("speed QP error: set gradient failure");
  if (!piecewise_jerk_path.data()->setLinearConstraintsMatrix(sparse_affine))
    return tpnc::Status::ERROR("speed QP error: set affine failure");
  if (!piecewise_jerk_path.data()->setLowerBound(boundary_lower))
    return tpnc::Status::ERROR("speed QP error: set lower_boundary failure");
  if (!piecewise_jerk_path.data()->setUpperBound(boundary_upper))
    return tpnc::Status::ERROR("speed QP error: set upper_boundary failure");
  // instantiate the piecewise_jerk_path
  if (!piecewise_jerk_path.initSolver())
    return tpnc::Status::ERROR("speed QP error: solver init failure");

  // Solve.
  if (!piecewise_jerk_path.solve())
    return tpnc::Status::ERROR("path QP error: solver failure");

  Eigen::VectorXd qp_solution = piecewise_jerk_path.getSolution();

  math::PiecewiseJerk1d st_speed(qp_solution(0), qp_solution(optimized_size),
                                 qp_solution(2 * optimized_size));
  for (size_t i = 2 * optimized_size + 1; i < 3 * optimized_size; i++) {
    const double jerk = (qp_solution(i) - qp_solution(i - 1)) / config_.dt();
    st_speed.AppendSegment(jerk, config_.dt());
  }
  reference_line_info_->mutable_speed_data()->clear();
  for (double t = 0; t < st_speed.ParamLength(); t += config_.dt()) {
    reference_line_info_->mutable_speed_data()->emplace_back(
        st_speed.Evaluate(0, t), t, st_speed.Evaluate(1, t),
        st_speed.Evaluate(2, t), st_speed.Evaluate(3, t));
  }
  if (!reference_line_info->CombinePathAndSpeedProfile(
          frame->init_point().relative_time(), frame->init_point().s())) {
    reference_line_info->set_is_drivable({false, ""});
    TRUNK_LOG_ERROR << "Fail to aggregate planning trajectory";
    return tpnc::Status::ERROR("Fail to aggregate planning trajectory");
  }

  return tpnc::Status::OK();
}

double QpStPiecewiseTask::RSSDistance(const double cur_v,
                                      const double v_front) const {
  const double ego_base2front =
      tpnc::Singleton<tmodel::Truck>::GetInstance()->geometry().base2front();
  return std::max(
      0.0,
      cur_v * config_.rss_action_delay() +
          0.5 * config_.max_acc() * std::pow(config_.rss_action_delay(), 2) +
          std::pow(cur_v + config_.rss_action_delay() * config_.max_acc(), 2) /
              2.0 / std::fabs(config_.rss_ego_max_dec()) -
          v_front * v_front / 2.0 / std::fabs(config_.rss_obs_max_dec()));
}

bool QpStPiecewiseTask::GetSConstraintByTime(const double time,
                                             double& s_upper_bound,
                                             double& s_lower_bound) const {
  s_upper_bound = config_.total_length();

  // for (const port::STBoundary& boundary : boundaries) {
  for (const auto& obstacle : reference_line_info_->obstacles().Items()) {
    const auto& boundary = obstacle->st_boundary();
    double s_upper = 200.0;
    double s_lower = 0.0;
    if (!boundary.GetUnblockBoundarySRange(time, &s_upper, &s_lower)) {
      continue;
    }
    if (boundary.boundary_type() == port::STBoundary::BoundaryType::STOP ||
        boundary.boundary_type() == port::STBoundary::BoundaryType::FOLLOW ||
        boundary.boundary_type() == port::STBoundary::BoundaryType::YIELD) {
      s_upper_bound = std::fmin(s_upper_bound, s_upper);
    } else if (boundary.boundary_type() ==
               port::STBoundary::BoundaryType::OVERTAKE) {
      s_lower_bound = std::fmax(s_lower_bound, s_lower);
    } else {
      // TRUNK_LOG_WARN << "未处理的boundary type: "
      //                 << boundary.TypeName(boundary.boundary_type());
    }
  }
  return true;
}

bool QpStPiecewiseTask::EstimateSpeedUpperBound(
    const tport::TrajectoryPoint& init_point,
    const port::SpeedLimit& speed_limit,
    std::vector<double>* speed_upper_bound) const {
  if (speed_upper_bound == nullptr) {
    return false;
  }

  speed_upper_bound->clear();

  // use v to estimate position: not accurate, but feasible in cyclic
  // processing. We can do the following process multiple times and use
  // previous cycle's results for better estimation.
  port::SpeedData last_speed_data;
  const double v = init_point.v();
  if (util::last_frame_ != nullptr) {
    const port::ReferenceLineInfo* last_reference_line_info =
        util::last_frame_->drive_reference_line_info();
    if (last_reference_line_info) {
      last_speed_data = last_reference_line_info->speed_data();
    }
  }

  if (static_cast<double>(t_evaluated_.size() +
                          speed_limit.speed_limit_points().size()) <
      static_cast<double>(t_evaluated_.size()) *
          std::log(
              static_cast<double>(speed_limit.speed_limit_points().size()))) {
    uint32_t i = 0;
    uint32_t j = 0;
    while (i < t_evaluated_.size() &&
           j + 1 < speed_limit.speed_limit_points().size()) {
      // 应该是根据当前速度预估t时刻到达的s，或者根据上一帧的规划速度预估t时刻的s
      double distance = v * t_evaluated_[i];
      if (!last_speed_data.empty() && distance < last_speed_data.back().s()) {
        port::SpeedSTPoint p;
        last_speed_data.EvaluateByTime(t_evaluated_[i], &p);
        distance = p.s();
      }
      constexpr double kDistanceEpsilon = 1e-6;
      if (fabs(distance - speed_limit.speed_limit_points()[j].first) <
          kDistanceEpsilon) {
        speed_upper_bound->push_back(
            speed_limit.speed_limit_points()[j].second);
        ++i;
      } else if (distance < speed_limit.speed_limit_points()[j].first) {
        ++i;
      } else if (distance <= speed_limit.speed_limit_points()[j + 1].first) {
        speed_upper_bound->push_back(speed_limit.GetSpeedLimitByS(distance));
        ++i;
      } else {
        ++j;
      }
    }

    for (size_t k = speed_upper_bound->size(); k < t_evaluated_.size(); ++k) {
      speed_upper_bound->push_back(
          tutil::Kmh2Ms(util::sys_config_.config_speed_limit()));
    }
  } else {
    auto cmp = [](const std::pair<double, double>& p1, const double s) {
      return p1.first < s;
    };

    const auto& speed_limit_points = speed_limit.speed_limit_points();
    for (const double t : t_evaluated_) {
      double s = v * t;
      if (!last_speed_data.empty() && s < last_speed_data.back().s()) {
        port::SpeedSTPoint p;
        last_speed_data.EvaluateByTime(t, &p);
        s = p.s();
      }

      // NOTICE: we are using binary search here based on two assumptions:
      // (1) The s in speed_limit_points increase monotonically.
      // (2) The evaluated_t_.size() << number of speed_limit_points.size()
      //
      // If either of the two assumption is failed, a new algorithm must be
      // used to replace the binary search.

      const auto& it = std::lower_bound(speed_limit_points.begin(),
                                        speed_limit_points.end(), s, cmp);
      if (it != speed_limit_points.end()) {
        speed_upper_bound->push_back(it->second);
      } else {
        speed_upper_bound->push_back(speed_limit_points.back().second);
      }
    }
  }

  if (reference_line_info_->overtake_flag()) {
    for (uint32_t k = 0; k < t_evaluated_.size(); ++k) {
      speed_upper_bound->at(k) *=
          (1.0 + config_.lane_change_speed_relax_ratio());
    }
  }

  const double kTimeBuffer = 3.0;
  const double kSpeedBuffer = 0.1 + init_point.v() - speed_upper_bound->front();
  for (uint32_t k = 0; k < t_evaluated_.size() && t_evaluated_[k] < kTimeBuffer;
       ++k) {
    speed_upper_bound->at(k) = std::fmax(
        speed_upper_bound->at(k) +
            kSpeedBuffer * (kTimeBuffer - t_evaluated_[k]) / kTimeBuffer,
        speed_upper_bound->at(k));
  }

  return true;
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
