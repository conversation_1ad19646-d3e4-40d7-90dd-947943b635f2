// Copyright 2024, trunk Inc. All rights reserved.

#pragma once

#include <OsqpEigen/OsqpEigen.h>

#include <Eigen/Dense>

#include "param/qp_st_piecewise_task_param.h"
#include "task/st_graph/speed_limit_decider.h"
#include "task/task.h"

namespace trunk {
namespace pnd {
namespace task {

class QpStPiecewiseTask : public Task {
 public:
  QpStPiecewiseTask();
  virtual ~QpStPiecewiseTask() = default;

  virtual tpnc::Status Execute(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info);

 private:
  double RSSDistance(const double cur_v, const double v_front) const;
  bool GetSConstraintByTime(const double time, double& s_upper_bound,
                            double& s_lower_bound) const;
  bool EstimateSpeedUpperBound(const tport::TrajectoryPoint& init_point,
                               const port::SpeedLimit& speed_limit,
                               std::vector<double>* speed_upper_bound) const;

 private:
  const QpStPiecewiseTaskParam& config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()
          ->GetConfig<QpStPiecewiseTaskParam>("qp_st_piecewise_task");
  port::ReferenceLineInfo* reference_line_info_ = nullptr;
  std::vector<double> t_evaluated_;
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
