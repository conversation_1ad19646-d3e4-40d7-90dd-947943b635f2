// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <absl/time/clock.h>
#include <absl/time/time.h>

#include <cstddef>

#include "log.h"
#include "param/qp_st_speed_param.h"
#include "param/st_graph_param.h"
#include "param/sys_param.h"
#include "port/frame.h"

namespace trunk {
namespace pnd {
namespace task {

class StBoundaryMapper {
 public:
  StBoundaryMapper(const port::SLBoundary& adc_sl_boundary,
                   const port::ReferenceLineInfo& reference_line,
                   const port::PathData& path_data,
                   const double planning_distance, const double planning_time);

  virtual ~StBoundaryMapper() = default;

  bool CreateStBoundary() const;

 private:
  bool CheckOverlap(const tport::PathPoint& path_point,
                    const tport::Contour2D& obs_box, const double buffer) const;

  /**
   * Creates valid st boundary upper_points and lower_points
   * If return true, upper_points.size() > 1 and
   * upper_points.size() = lower_points.size()
   */
  bool GetOverlapBoundaryPoints(const tport::Path& path_points,
                                const port::Obstacle& obstacle,
                                std::vector<port::STPoint>* upper_points,
                                std::vector<port::STPoint>* lower_points) const;

  bool MapStopDecision(port::Obstacle& stop_obstacle,
                       const port::ObstacleDecision& decision) const;

  bool MapWithDecision(port::Obstacle& obstacle,
                       const port::ObstacleDecision& decision) const;

  bool MapWithoutDecision(port::Obstacle& obstacle,
                          const port::ObstacleDecision& decision) const;

  /**
   * @brief ST边界上插新值
   * @details 扩展st的边界，s范围扩大，t填充
   * */
  void ExpandSTBoundary(std::vector<port::STPoint>& lower_pts,
                        std::vector<port::STPoint>& upper_pts, const double s,
                        const double t) const;

 private:
  STBoundaryConfig st_boundary_config_;
  SysParam sys_config_;
  const port::SLBoundary& adc_sl_boundary_;
  const port::ReferenceLineInfo& reference_line_;
  const port::PathData& path_data_;
  const double planning_distance_;
  const double planning_time_;
  std::unique_ptr<tutil::CollisionDetect> collision_detect_ = nullptr;
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
