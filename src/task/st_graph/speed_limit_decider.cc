// Copyright 2023, trunk Inc. All rights reserved

#include "speed_limit_decider.h"

#include "log.h"
#include "util/key.h"
#include "util/util.h"

namespace trunk {
namespace pnd {
namespace task {

SpeedLimitDecider::SpeedLimitDecider(
    const port::SLBoundary& adc_sl_boundary, const StGraphParam& config,
    const port::ReferenceLineInfo& reference_line_info,
    const port::ReferenceLineInfo* current_reference_line_info,
    const double tnp_speed_limit)
    : adc_sl_boundary_(adc_sl_boundary),
      st_boundary_config_(config),
      reference_line_info_(reference_line_info),
      path_data_(reference_line_info.path_data()),
      vehicle_param_(tpnc::Singleton<tmodel::Truck>::GetInstance()->geometry()),
      current_reference_line_info_(current_reference_line_info),
      tnp_speed_limit_(tnp_speed_limit) {}

tpnc::Status SpeedLimitDecider::GetSpeedLimits(
    const port::IndexedObstacles& obstacles,
    port::SpeedLimit* const speed_limit_data) const {
  if (speed_limit_data == nullptr) {
    TRUNK_LOG_ERROR << "speed limit nullptr";
  }

  std::vector<double> avg_kappa;
  GetAvgKappa(path_data_.qp_norm_path(), &avg_kappa);
  const auto& discretized_path = path_data_.qp_norm_path();
  const auto& frenet_path = path_data_.qp_sl_path();
  for (size_t i = 0; i < discretized_path.size(); ++i) {
    const double path_s = discretized_path.at(i).s();
    const double frenet_point_s = frenet_path.at(i).s();
    if (frenet_point_s > reference_line_info_.reference_line().back().s()) {
      // TRUNK_LOG_WARN << "path length [" << frenet_point_s
      // << "] is LARGER than reference_line_ length ["
      // << reference_line_info_.reference_line().back().s()
      // << "]. Please debug before proceeding.";
      break;
    }

    // (1) speed limit from map, 下匝道hack
    double speed_limit_on_reference_line =
        current_reference_line_info_ &&
                reference_line_info_.reference_line().front().s() >
                    frenet_point_s
            ? current_reference_line_info_->GetSpeedLimitFromS(frenet_point_s)
            : reference_line_info_.GetSpeedLimitFromS(frenet_point_s);

    // (2) speed limit from path curvature
    //  -- 2.1: limit by centripetal force (acceleration)
    const double centri_acc_speed_limit =
        std::sqrt(GetCentricAccLimit(std::fabs(avg_kappa[i])) /
                  std::fmax(std::fabs(avg_kappa[i]),
                            st_boundary_config_.minimal_kappa()));

    // -- 2.2: limit by centripetal jerk
    double centri_jerk_speed_limit = std::numeric_limits<double>::max();
    if (i + 1 < discretized_path.size()) {
      const double ds =
          discretized_path.at(i + 1).s() - discretized_path.at(i).s();
      // DCHECK_GE(ds, 0.0);
      const double kEpsilon = 1e-9;
      const double centri_jerk =
          std::fabs(avg_kappa[i + 1] - avg_kappa[i]) / (ds + kEpsilon);
      centri_jerk_speed_limit = std::fmax(
          10.0, st_boundary_config_.centri_jerk_speed_coeff() / centri_jerk);
    }

    // (3) speed limit from nudge obstacles
    double nudge_obstacle_speed_limit = std::numeric_limits<double>::max();
    for (const auto* const_obstacle : obstacles.Items()) {
      if (const_obstacle->is_virtual()) {
        continue;
      }
      if (const_obstacle->lat_decision() !=
              port::ObstacleDecision::kRightNudge &&
          const_obstacle->lat_decision() !=
              port::ObstacleDecision::kLeftNudge) {
        continue;
      }

      /* ref line:
       * -------------------------------
       *    start_s   end_s
       * ------|  adc   |---------------
       * ------------|  obstacle |------
       */
      if (frenet_point_s + vehicle_param_.base2front() <
              const_obstacle->sl_boundary().min_s() ||
          frenet_point_s - vehicle_param_.base2tail() >
              const_obstacle->sl_boundary().max_s()) {
        continue;  // PerceptionSLBoundary().start_s()/end_s()
      }
      constexpr double kRange = 1.0;  // meters
      const auto& nudge = const_obstacle->lat_decision();

      // Please notice the differences between adc_l and frenet_point_l
      const double frenet_point_l = frenet_path.at(i).l();

      // obstacle is on the right of ego vehicle (at path point i)
      bool is_close_on_left =
          (nudge == port::ObstacleDecision::kLeftNudge) &&
          (frenet_point_l - vehicle_param_.width() * 0.5 - kRange <
           const_obstacle->sl_boundary()
               .max_l());  // PerceptionSLBoundary().end_l()

      // obstacle is on the left of ego vehicle (at path point i)
      bool is_close_on_right =
          (nudge == port::ObstacleDecision::kRightNudge) &&
          (const_obstacle->sl_boundary().min_l() - kRange <
           frenet_point_l + vehicle_param_.width() *
                                0.5);  // PerceptionSLBoundary().start_l()

      if (is_close_on_left || is_close_on_right) {
        double nudge_speed_ratio = 1.0;
        if (const_obstacle->obj().is_static()) {
          nudge_speed_ratio =
              st_boundary_config_.static_obs_nudge_speed_ratio();
        } else {
          nudge_speed_ratio =
              st_boundary_config_.dynamic_obs_nudge_speed_ratio();
        }
        nudge_obstacle_speed_limit =
            nudge_speed_ratio * speed_limit_on_reference_line;
        break;
      }
    }

    double curr_speed_limit = 0.0;
    if (st_boundary_config_.enable_nudge_slowdown()) {
      curr_speed_limit =
          std::fmax(st_boundary_config_.lowest_speed(),
                    tutil::MinElement(std::vector<double>{
                        speed_limit_on_reference_line, centri_acc_speed_limit,
                        centri_jerk_speed_limit, nudge_obstacle_speed_limit,
                        tnp_speed_limit_}));
    } else {
      curr_speed_limit =
          std::fmax(st_boundary_config_.lowest_speed(),
                    tutil::MinElement(std::vector<double>{
                        speed_limit_on_reference_line, centri_acc_speed_limit,
                        centri_jerk_speed_limit, tnp_speed_limit_}));
    }
    if (curr_speed_limit < speed_limit_on_reference_line) {
      TRUNK_LOG_DEBUG << absl::StrFormat(
          "s: %.2f, curr: %.2f, acc: %.2f, jerk: %.2f, map: %.2f, nudge: %.2f",
          path_s, curr_speed_limit, centri_acc_speed_limit,
          centri_jerk_speed_limit > 35.0 ? 35.0 : centri_jerk_speed_limit,
          speed_limit_on_reference_line,
          nudge_obstacle_speed_limit > 35.0 ? 35.0
                                            : nudge_obstacle_speed_limit);
    }
    speed_limit_data->AppendSpeedLimit(path_s, curr_speed_limit);
  }  // end of for

  return tpnc::Status::OK();
}

double SpeedLimitDecider::GetCentricAccLimit(const double kappa) const {
  // this function uses a linear model with upper and lower bound to
  // determine centric acceleration limit

  // suppose acc = k1 * v + k2
  // consider acc = v ^ 2 * kappa
  // we determine acc by the two functions above, with uppper and lower
  // speed bounds
  // 根据v线性插值找a
  const double v_high = st_boundary_config_.high_speed_threshold();
  const double v_low = st_boundary_config_.low_speed_threshold();

  const double h_v_acc =
      st_boundary_config_.high_speed_centric_acceleration_limit();
  const double l_v_acc =
      st_boundary_config_.low_speed_centric_acceleration_limit();

  if (std::fabs(v_high - v_low) < 1.0) {
    TRUNK_LOG_ERROR << "High speed and low speed threshold are too close to "
                       "each other. Check config file, high speed threshold = "
                    << v_high << ", low speed threshold = " << v_low;
    return h_v_acc;
  }
  const double kMinKappaEpsilon = 1e-9;
  if (kappa < kMinKappaEpsilon) {
    return h_v_acc;
  }

  const double k1 = (h_v_acc - l_v_acc) / (v_high - v_low);
  const double k2 = h_v_acc - v_high * k1;

  // 联立suppose acc和consider acc的二次函数的解
  const double v = (k1 + std::sqrt(k1 * k1 + 4.0 * kappa * k2)) / (2.0 * kappa);

  if (v > v_high) {
    return h_v_acc;
  } else if (v < v_low) {
    return l_v_acc;
  } else {
    return v * k1 + k2;
  }
  // 假设了一个速度和加速度的线性映射关系, 根据这个假设的关系和运动学公式
  // 以及当前曲率, 算出了一个假设的速度, 再根据这个假设的速度查找之前定义
  // 的假设映射关系, 找到了一个对应的加速度, 再根据运动学公式将这个加速度
  // 转换成速度约束
  // 本质上是一个曲率到限速的一个非线性映射, 绕了很大一个弯
  //
  // 疑问: 为什么不直接做个从曲率到限速的一个线性映射或幂函数映射,
  // 效果区别很大吗
}

void SpeedLimitDecider::GetAvgKappa(
    const std::vector<tport::PathPoint>& path_points,
    std::vector<double>* kappa) const {
  // 根据num_points_to_avg_kappa找到一个点附近的n个点, 做曲率平均处理
  // num_points_to_avg_kappa的点数跟实际测试结果有点对不上, 可能函数有点问题
  //
  // 疑问: 为什么还要平均一下, 路径规划优化出来的路径还不够平滑吗

  // CHECK_NOTNULL(kappa);
  const int kHalfNumPoints = st_boundary_config_.num_points_to_avg_kappa() / 2;
  // CHECK_GT(kHalfNumPoints, 0);
  kappa->clear();
  kappa->resize(path_points.size());
  double sum = 0.0;
  int start = 0;
  int end = 0;
  while (end < static_cast<int>(path_points.size()) &&
         end - start < kHalfNumPoints + 1) {
    sum += path_points[end].kappa();
    ++end;
  }

  int iter = 0;
  while (iter < static_cast<int>(path_points.size())) {
    kappa->at(iter) = sum / (end - start);
    if (start < iter - kHalfNumPoints) {
      sum -= path_points[start].kappa();
      ++start;
    }
    if (end < static_cast<int>(path_points.size())) {
      sum += path_points[end].kappa();
      ++end;
    }
    ++iter;
  }
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
