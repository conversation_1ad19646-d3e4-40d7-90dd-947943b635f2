// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <absl/strings/str_format.h>
#include <param/st_graph_param.h>
#include <trunk/common/common.h>

#include "port/frame.h"
#include "port/reference_line_info.h"
#include "port/st/speed_limit.h"

namespace trunk {
namespace pnd {
namespace task {

class SpeedLimitDecider {
 public:
  SpeedLimitDecider(const port::SLBoundary& adc_sl_boundary,
                    const StGraphParam& config,
                    const port::ReferenceLineInfo& reference_line,
                    const port::ReferenceLineInfo* current_reference_line,
                    const double tnp_speed_limit);

  tpnc::Status GetSpeedLimits(const port::IndexedObstacles& obstacles,
                              port::SpeedLimit* const speed_limit_data) const;

 private:
  /**
   * @brief 由当前kappa求出对应横向加速度
   * @details 设加速度和速度变化为线性方程
   * 形如acc = k1 velocity + k2                (1)
   * 由加速度公式知: acc = velocity^2 / kappa  (2)
   * 联立(1) (2)可得velocity = [一元二次方程通解]
   * 输出加速度
   * */
  double GetCentricAccLimit(const double kappa) const;

  void GetAvgKappa(const std::vector<tport::PathPoint>& path_points,
                   std::vector<double>* kappa) const;

 private:
  const port::SLBoundary& adc_sl_boundary_;
  const StGraphParam& st_boundary_config_;
  const port::ReferenceLineInfo& reference_line_info_;
  const port::PathData& path_data_;
  const tcommon::model::Geometry& vehicle_param_;
  const port::ReferenceLineInfo* current_reference_line_info_;
  const double tnp_speed_limit_ = 100.0;
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
