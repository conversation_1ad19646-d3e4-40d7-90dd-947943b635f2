// Copyright 2023, trunk Inc. All rights reserved

#include "st_graph_data.h"

namespace trunk {
namespace pnd {
namespace task {

StGraphData::StGraphData(
    const std::vector<const port::STBoundary*>& st_boundaries,
    const tport::TrajectoryPoint& init_point,
    const port::SpeedLimit& speed_limit, const double path_data_length)
    : st_boundaries_(st_boundaries),
      init_point_(init_point),
      speed_limit_(speed_limit),
      path_data_length_(path_data_length) {}

const std::vector<const port::STBoundary*>& StGraphData::st_boundaries() const {
  return st_boundaries_;
}

const tport::TrajectoryPoint& StGraphData::init_point() const {
  return init_point_;
}

const port::SpeedLimit& StGraphData::speed_limit() const {
  return speed_limit_;
}

double StGraphData::path_data_length() const { return path_data_length_; }
}  // namespace task
}  // namespace pnd
}  // namespace trunk
