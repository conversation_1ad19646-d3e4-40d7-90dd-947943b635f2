// Copyright 2023, trunk Inc. All rights reserved

#include "st_boundary_mapper.h"

#include "log.h"
#include "math/coordinate_transformation/coordiniate_transformation.h"
#include "port/obstacle.h"
#include "util/key.h"

namespace trunk {
namespace pnd {
namespace task {

namespace {
constexpr double kBoundaryTBuffer = 0.1;
constexpr double kBoundarySBuffer = 0.1;
}  // namespace

StBoundaryMapper::StBoundaryMapper(
    const port::SLBoundary& adc_sl_boundary,
    const port::ReferenceLineInfo& reference_line,
    const port::PathData& path_data, const double planning_distance,
    const double planning_time)
    : adc_sl_boundary_(adc_sl_boundary),
      reference_line_(reference_line),
      path_data_(path_data),
      planning_distance_(planning_distance),
      planning_time_(planning_time) {
  st_boundary_config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()
          ->GetConfig<QpSpeedOptimizerParam>("qp_speed_optimizer")
          .st_boundary_config();
  sys_config_ =
      tpnc::Singleton<tpnc::Config>::GetInstance()->GetConfig<SysParam>("sys");
  collision_detect_ = std::make_unique<tutil::CollisionDetect>();
}

bool StBoundaryMapper::CreateStBoundary() const {
  const auto& obstacles = reference_line_.obstacles();

  if (planning_time_ < 0.0) {
    TRUNK_LOG_ERROR << "Fail to get params since planning_time_ < 0.";
    return false;
  }

  if (path_data_.qp_sl_path().size() < 2) {
    TRUNK_LOG_ERROR
        << "Fail to get params because of too few path points. path points "
           "size: "
        << path_data_.qp_sl_path().size() << ".";
    return false;
  }

  port::Obstacle* stop_obstacle = nullptr;
  port::ObstacleDecision stop_decision;
  double min_stop_s = std::numeric_limits<double>::max();

  for (auto* const_obstacle : obstacles.Items()) {
    auto* obstacle = const_obstacle;

    const auto& sl_boundary = obstacle->sl_boundary();
    if (reference_line_.policy() == "lane_follow" &&
        sl_boundary.max_s() +
                st_boundary_config_.st_boundary_filtering_distance() <
            0.0) {
      TRUNK_LOG_INFO << obstacle->obj().id()
                     << "# is at behind of oge, filtered.";
      continue;
    }
    if ((sl_boundary.max_l() < -util::kHighwayLaneHalfWidth ||
         sl_boundary.min_l() > util::kHighwayLaneHalfWidth)) {
      if (reference_line_.policy() == "lane_follow" &&
          obstacle->obj().xy_velocity().x() < 0.0) {
        TRUNK_LOG_INFO << obstacle->obj().id()
                       << "# is a reverse vehicle from another lane, filtred";
        continue;
      }
    }
    // if (obstacle->obj().is_static() && obstacle->HasNudge()) {
    //   TRUNK_LOG_INFO << obstacle->obj().id()
    //                  << " do not map S-T Boundary for nudge decision";
    //   continue;
    // }

    if (obstacle->lon_decision() == port::ObstacleDecision::kNotSet) {
      MapWithoutDecision(*obstacle, obstacle->lon_decision());
      continue;
    }

    const auto& decision = obstacle->lon_decision();
    if (decision == port::ObstacleDecision::kStop) {
      // STOP decisio停在前方障碍物现在的位置，视为静态障碍物时停障
      const double stop_s = obstacle->sl_boundary().min_s();
      if (stop_s < reference_line_.AdcSlBoundary().min_s()) {
        TRUNK_LOG_WARN
            << "Invalid stop decision. not stop at behind of current s: "
            << reference_line_.AdcSlBoundary().min_s()
            << ", stop s: " << stop_s;
        continue;
      }
      // 如果障碍物停车的，有一个最小停车s
      if (stop_s < min_stop_s) {
        stop_obstacle = obstacle;
        min_stop_s = stop_s;
        stop_decision = decision;
      }
    } else if (decision == port::ObstacleDecision::kFollow ||
               decision == port::ObstacleDecision::kOvertake ||
               decision == port::ObstacleDecision::kYield) {
      if (!MapWithDecision(*obstacle, decision)) {
        TRUNK_LOG_ERROR << "Fail to map obstacle " << obstacle->obj().id()
                        << " with decision: ";
        return false;
      }
    } else if (decision != port::ObstacleDecision::kIgnore) {
      TRUNK_LOG_WARN << "No mapping for decision: ";
    }
  }  // end of obstacles for

  if (stop_obstacle) {
    bool success = MapStopDecision(*stop_obstacle, stop_decision);
    if (!success) {
      std::string msg = "Fail to MapStopDecision.";
      TRUNK_LOG_ERROR << msg;
      return false;
    }
  }
  return true;
}

bool StBoundaryMapper::MapStopDecision(
    port::Obstacle& stop_obstacle,
    const port::ObstacleDecision& stop_decision) const {
  // DCHECK(stop_decision.has_stop()) << "Must have stop decision";

  // if (stop_obstacle.obj().trajectory().front().s() >
  if (stop_obstacle.sl_boundary().min_s() >
      adc_sl_boundary_.max_s() + planning_distance_) {
    return true;
  }

  double st_stop_s = 0.0;
  const auto model = tpnc::Singleton<tmodel::Truck>::GetInstance();
  const double stop_ref_s = stop_obstacle.sl_boundary().min_s() -
                            st_boundary_config_.stop_distance() -
                            model->geometry().base2front();

  if (stop_ref_s > path_data_.qp_sl_path().back().s()) {
    st_stop_s = stop_ref_s;
  } else {
    tport::PathPoint stop_point =
        tutil::FindInterpPointByDistance(path_data_.dp_norm_path(), stop_ref_s);
    if ((reference_line_.self_sl().s() < reference_line_.sl_init_point().s() &&
         stop_point.s() < reference_line_.self_sl().s()) ||
        stop_point.s() > path_data_.dp_norm_path().back().s()) {
      return false;
    }
    st_stop_s = stop_point.s();
  }

  constexpr double kStopEpsilon = 1e-2;
  const double s_min = std::max(0.0, st_stop_s - kStopEpsilon);
  const double s_max =
      std::fmax(s_min, std::fmax(planning_distance_,
                                 reference_line_.reference_line().back().s()));

  port::STPoints lower, upper;
  port::STBoundary st_boundary;
  lower.emplace_back(s_min, 0.0);
  lower.emplace_back(s_min, planning_time_);
  upper.emplace_back(s_max, 0.0);
  upper.emplace_back(s_max + st_boundary_config_.boundary_buffer(),
                     planning_time_);

  st_boundary.set_max_s(s_max);
  st_boundary.set_min_s(s_min);
  st_boundary.set_max_t(planning_time_);
  st_boundary.set_min_t(0.0);
  st_boundary.set_boundary_type(port::STBoundary::BoundaryType::STOP);
  st_boundary.set_lower_points(lower);
  st_boundary.set_upper_points(upper);
  stop_obstacle.set_st_boundary(st_boundary);
  return true;
}

bool StBoundaryMapper::GetOverlapBoundaryPoints(
    const tport::Path& path_points, const port::Obstacle& obstacle,
    std::vector<port::STPoint>* upper_points,
    std::vector<port::STPoint>* lower_points) const {
  const auto start_run_time = absl::Now();
  const auto model = tpnc::Singleton<tmodel::Truck>::GetInstance();

  double back_buffer = st_boundary_config_.back_min_buffer();
  if (path_points.empty()) {
    TRUNK_LOG_ERROR << "No points in path_data_.discretized_path().";
    return false;
  }

  const double obs_len =
      obstacle.sl_boundary().max_s() - obstacle.sl_boundary().min_s();
  const double obs_wid =
      obstacle.sl_boundary().max_l() - obstacle.sl_boundary().min_l();
  const auto& trajectory = obstacle.obj().trajectory();
  if (trajectory.empty()) {
    // 障碍物没有预测轨迹
    for (const auto& curr_point_on_path : path_points) {
      if (curr_point_on_path.s() > planning_distance_) {
        TRUNK_LOG_INFO << "curr point: " << curr_point_on_path.s()
                       << ", planning_dis: " << planning_distance_;
        break;
      }
      if (util::last_frame_ != nullptr &&
          util::last_frame_->drive_reference_line_info() != nullptr &&
          !util::last_frame_->drive_reference_line_info()
               ->trajectory()
               .empty()) {
        double ego_v = util::last_frame_->drive_reference_line_info()
                           ->trajectory()
                           .EvaluateByS(curr_point_on_path.s())
                           .v();
        back_buffer = st_boundary_config_.back_min_buffer() +
                      std::fmax(0.0, obstacle.obj().velocity() - ego_v) *
                          st_boundary_config_.back_t();
      }
      const tport::Contour2D obs_box = obstacle.obj().xy_contour();
      if (CheckOverlap(curr_point_on_path, obs_box, back_buffer)) {
        const double backward_distance = -model->geometry().base2front();
        const double forward_distance = model->geometry().length() +
                                        model->geometry().width() + obs_len +
                                        obs_wid;
        double low_s =
            std::fmax(0.0, curr_point_on_path.s() + backward_distance);
        double high_s = std::fmin(planning_distance_,
                                  curr_point_on_path.s() + forward_distance);
        lower_points->emplace_back(low_s, 0.0);
        upper_points->emplace_back(high_s, 0.0);
        if (obstacle.obj().velocity() <
            sys_config_.static_obstacle_velocity_threshold()) {
          lower_points->emplace_back(low_s, planning_time_);
          upper_points->emplace_back(high_s, planning_time_);
        } else {
          TRUNK_LOG_DEBUG << "obstacle trajectory is empty while velocity is: "
                          << obstacle.obj().velocity();
          // catch 预测异常,防止有速度无轨迹情况
          double assume_dis = obstacle.obj().velocity() * planning_time_;
          lower_points->emplace_back(low_s + assume_dis, planning_time_);
          upper_points->emplace_back(high_s + assume_dis, planning_time_);
        }
        break;
      }
    }
  } else {
    const size_t default_num_point = 50;
    tport::Path discretized_path;
    // 0. 抽稀参考轨迹
    if (path_points.size() > 2 * default_num_point) {
      const size_t ratio = path_points.size() / default_num_point;
      tport::Path sampled_path_points;
      for (size_t i = 0; i < path_points.size(); ++i) {
        if (i % ratio == 0) {
          sampled_path_points.push_back(path_points[i]);
        }
      }
      discretized_path = std::move(sampled_path_points);
    } else {
      discretized_path = path_points;
    }

    // 1. 预测轨迹碰撞检测
    tport::Contour2D temp_contour;
    // 提取预测边界
    for (const auto& pt : obstacle.obj().xy_contour()) {
      temp_contour.emplace_back(pt - obstacle.obj().xy_center());
    }

    for (const auto& pt : trajectory) {
      tport::Contour2D obs_box;
      for (const auto& contour_pt : temp_contour) {
        auto temp_pt = contour_pt;
        trunk::pnd::math::CoordinateRotationAroundOrigin(
            temp_pt, pt.theta() - obstacle.obj().heading());
        obs_box.emplace_back(temp_pt + tport::Point2D(pt));
      }

      double trajectory_point_time = pt.relative_time();
      constexpr double kNegtiveTimeThreshold = -1.0;
      if (trajectory_point_time < kNegtiveTimeThreshold) {
        continue;
      }
      // 快速找到碰撞点
      const double step_length = model->geometry().base2front();
      auto path_len = std::min(sys_config_.ref_line_front_length(),
                               discretized_path.back().s());
      for (double path_s = 0.0; path_s < path_len; path_s += step_length) {
        // int pt_idx = tutil::FindPointIndexByDistance(
        //     discretized_path, path_s + discretized_path.front().s());
        // tport::PathPoint curr_adc_path_point = discretized_path[pt_idx];
        tport::PathPoint curr_adc_path_point =
            discretized_path.EvaluateByS(path_s + discretized_path.front().s());

        if (util::last_frame_ != nullptr &&
            util::last_frame_->drive_reference_line_info() != nullptr &&
            !util::last_frame_->drive_reference_line_info()
                 ->trajectory()
                 .empty()) {
          double ego_v = util::last_frame_->drive_reference_line_info()
                             ->trajectory()
                             .EvaluateByS(curr_adc_path_point.s())
                             .v();
          back_buffer = st_boundary_config_.back_min_buffer() +
                        std::fmax(0.0, obstacle.obj().velocity() - ego_v) *
                            st_boundary_config_.back_t();
        }

        if (CheckOverlap(curr_adc_path_point, obs_box, back_buffer)) {
          double forward_distance = model->geometry().length() +
                                    model->geometry().width() + obs_len +
                                    obs_wid;
          if (util::sys_config_.with_trailer()) {
            forward_distance += model->trailer().geometry().base2tail();
          }
          const double fine_tuning_step_length = 0.5;  // 单位:m

          bool find_low = false;
          bool find_high = false;
          double low_s = std::fmax(0.0, path_s - step_length);
          double high_s =
              std::fmin(discretized_path.back().s(), path_s + forward_distance);

          while (low_s < high_s) {
            if (find_low && find_high) {
              break;
            }
            if (!find_low) {
              // int pt_idx = tutil::FindPointIndexByDistance(
              //     discretized_path, low_s + discretized_path.front().s());
              // tport::PathPoint point_low = discretized_path[pt_idx];
              tport::PathPoint point_low = discretized_path.EvaluateByS(
                  low_s + discretized_path.front().s());
              if (!CheckOverlap(point_low, obs_box, back_buffer)) {
                low_s += fine_tuning_step_length;
              } else {
                find_low = true;
              }
            }
            if (!find_high) {
              // int pt_idx = tutil::FindPointIndexByDistance(
              //     discretized_path, high_s + discretized_path.front().s());
              // tport::PathPoint point_high = discretized_path[pt_idx];
              tport::PathPoint point_high = discretized_path.EvaluateByS(
                  high_s + discretized_path.front().s());
              if (!CheckOverlap(point_high, obs_box, back_buffer)) {
                high_s -= fine_tuning_step_length;
              } else {
                find_high = true;
              }
            }
          }
          double point_extension = 0.0;
          if (find_high && find_low) {
            lower_points->emplace_back(low_s - point_extension,
                                       trajectory_point_time);
            upper_points->emplace_back(high_s + point_extension,
                                       trajectory_point_time);
          }
          break;
        }
      }
    }
    // ST_bounary 延长（针对于预测轨迹长度比规划轨迹长，且碰撞的）
    if (lower_points->size() && upper_points->size()) {
      const double end_t = trajectory.back().relative_time();
      const double end_s = discretized_path.back().s();
      if (lower_points->back().t() < end_t &&
          upper_points->back().t() < end_t &&
          end_s - upper_points->back().s() <= 0.0) {
        lower_points->emplace_back(
            lower_points->back().s() +
                (end_t - lower_points->back().t()) * obstacle.obj().velocity(),
            end_t);
        upper_points->emplace_back(
            upper_points->back().s() +
                (end_t - upper_points->back().t()) * obstacle.obj().velocity(),
            end_t);
      }
    }
  }
  // XXX 临时
  const double run_time =
      absl::ToDoubleMilliseconds(absl::Now() - start_run_time);
  TRUNK_LOG_DEBUG << "get over lap use time: " << run_time << " ms.";
  return (lower_points->size() > 1 && upper_points->size() > 1);
}

bool StBoundaryMapper::MapWithDecision(
    port::Obstacle& obstacle, const port::ObstacleDecision& decision) const {
  std::vector<port::STPoint> lower_points;
  std::vector<port::STPoint> upper_points;

  if (!obstacle.st_boundary().lower_points().empty() &&
      !obstacle.st_boundary().upper_points().empty()) {
    lower_points = obstacle.st_boundary().lower_points();
    upper_points = obstacle.st_boundary().upper_points();
  } else {
    if (!GetOverlapBoundaryPoints(path_data_.qp_norm_path(), obstacle,
                                  &upper_points, &lower_points)) {
      return true;
    }
    ExpandSTBoundary(lower_points, upper_points, kBoundarySBuffer,
                     kBoundaryTBuffer);
  }

  //   // 如下方法在lower_bound相同时会生成类似于stop的st图，VTI-12466
  //   原因是预测轨迹的连续几个时刻都与自车的同一个位置（solution=0.5m）发生碰撞。
  //   // 由于目前的预测线长为8s
  //   >现在的速度规划时长7m，故先注释这段代码，后续有需要时再启用并修复该问题
  // 重启该段代码，但仅在障碍物预测轨迹时长不足planning_time_时进行延长(增加的条件)，
  // 因为障碍物轨迹被截断了 VTI-12790
  if ((decision == port::ObstacleDecision::kFollow ||
       decision == port::ObstacleDecision::kYield) &&
      obstacle.obj().trajectory().size() &&
      obstacle.obj().trajectory().back().relative_time() < planning_time_ &&
      lower_points.back().t() < planning_time_) {
    // 将障碍物匀速延长，避免预测轨迹不足致使自车加速之后提前恢复速度
    // XXX: 考虑使用障碍车速度进行延长，避免stop生成
    const double diff_s = lower_points.back().s() - lower_points.front().s();
    const double diff_t = lower_points.back().t() - lower_points.front().t();
    double extend_lower_s =
        diff_s / diff_t * (planning_time_ - lower_points.front().t()) +
        lower_points.front().s();
    const double extend_upper_s =
        extend_lower_s + (upper_points.back().s() - lower_points.back().s()) +
        1.0;
    upper_points.emplace_back(extend_upper_s, planning_time_);
    lower_points.emplace_back(extend_lower_s, planning_time_);
  }

  port::STBoundary::BoundaryType bt;
  if (decision == port::ObstacleDecision::kFollow) {
    bt = port::STBoundary::BoundaryType::FOLLOW;
  } else if (decision == port::ObstacleDecision::kYield) {
    bt = port::STBoundary::BoundaryType::YIELD;
  } else {
    bt = port::STBoundary::BoundaryType::OVERTAKE;
  }
  port::STBoundary boundary;
  boundary.set_max_s(upper_points.back().s());
  boundary.set_min_s(lower_points.front().s());
  boundary.set_max_t(planning_time_);
  boundary.set_min_t(0.0);
  boundary.set_boundary_type(bt);
  boundary.set_lower_points(lower_points);
  boundary.set_upper_points(upper_points);

  obstacle.set_st_boundary(boundary);

  return true;
}

bool StBoundaryMapper::MapWithoutDecision(
    port::Obstacle& obstacle,
    const port::ObstacleDecision& stop_decision) const {
  std::vector<port::STPoint> lower_points;
  std::vector<port::STPoint> upper_points;

  if (!GetOverlapBoundaryPoints(path_data_.qp_norm_path(), obstacle,
                                &upper_points, &lower_points)) {
    return true;
  }

  ExpandSTBoundary(lower_points, upper_points, kBoundarySBuffer,
                   kBoundaryTBuffer);
  port::STBoundary boundary;

  boundary.set_max_s(upper_points.back().s());
  boundary.set_min_s(lower_points.front().s());
  boundary.set_max_t(upper_points.back().t());
  boundary.set_min_t(lower_points.front().t());
  boundary.set_boundary_type(port::STBoundary::BoundaryType::UNKNOWN);
  boundary.set_lower_points(lower_points);
  boundary.set_upper_points(upper_points);
  obstacle.set_st_boundary(boundary);

  return true;
}

bool StBoundaryMapper::CheckOverlap(const tport::PathPoint& path_point,
                                    const tport::Contour2D& obs_box,
                                    const double buffer) const {
  const auto model = tpnc::Singleton<tmodel::Truck>::GetInstance();
  tutil::TruckParam truck_param(*model);
  double lat_boundary_buffer = st_boundary_config_.lat_boundary_buffer();
  // double back_buffer = st_boundary_config_.back_buffer();
  if (reference_line_.policy() == "lane_follow") {
    lat_boundary_buffer = 0.1;
    // back_buffer = 0.5;
  }
  bool check_trailer = true;
  if (!util::sys_config_.with_trailer()) {
    truck_param.set_trailer_base2tail(0.0);
    truck_param.ExpandHeadBase2Tail(buffer);
    check_trailer = false;
  } else {
    truck_param.ExpandTrailerBase2Tail(buffer);
    truck_param.ExpandTrailerWidth(std::min(2 * lat_boundary_buffer, 3.0));
  }
  truck_param.ExpandHeadWidth(std::min(2 * lat_boundary_buffer, 3.0));
  truck_param.ExpandHeadBase2Front(0.0);
  return collision_detect_->DetectSingleObstacle(
      path_point, truck_param, obs_box, path_point.trailer_theta(),
      check_trailer);
}

void StBoundaryMapper::ExpandSTBoundary(std::vector<port::STPoint>& lower_pts,
                                        std::vector<port::STPoint>& upper_pts,
                                        double s, double t) const {
  if (lower_pts.size() < 2) {
    return;
  }
  // 拓展s
  std::vector<port::STPoint> lower_expand_s;
  std::vector<port::STPoint> upper_expand_s;
  for (size_t i = 0; i < lower_pts.size(); ++i) {
    lower_expand_s.emplace_back(
        port::STPoint(lower_pts[i].s() - s, lower_pts[i].t()));
    upper_expand_s.emplace_back(
        port::STPoint(upper_pts[i].s() + s, upper_pts[i].t()));
  }

  // 拓展t
  std::vector<port::STPoint> lower_expand_t;
  std::vector<port::STPoint> upper_expand_t;
  const double left_delta_t =
      std::max(lower_pts[1].t() - lower_pts[0].t(), 0.1);
  const double lower_left_delta_s = lower_pts[1].s() - lower_pts[0].s();
  const double upper_left_delta_s = upper_pts[1].s() - upper_pts[0].s();

  lower_expand_t.emplace_back(
      port::STPoint(lower_pts[0].s() - t * lower_left_delta_s / left_delta_t,
                    lower_pts[0].t() - t));
  upper_expand_t.emplace_back(
      port::STPoint(upper_pts[0].s() - t * upper_left_delta_s / left_delta_t,
                    upper_pts.front().t() - t));
  // 疑问: 为什么要在front和back再设置一遍s, 而且还减了一个eps, 先注释
  // A:因为lower_left_delta_s 和
  // upper_left_delta_s的大小不一样，所以上述的计算会导致lower_points.front().s()
  // > upper_points.front().s(), 下面这个保证上边界大于下边界，
  // 下面upper_points也是同样的操作
  lower_expand_t.front().set_s(
      std::min(lower_expand_t.front().s(),
               upper_expand_t.front().s() - tport::kMathEpsilon));

  for (size_t i = 0; i < lower_expand_s.size(); ++i) {
    lower_expand_t.emplace_back(lower_expand_s[i]);
    upper_expand_t.emplace_back(upper_expand_s[i]);
  }

  size_t length = lower_expand_t.size();

  const double right_delta_t = std::max(
      lower_expand_t[length - 1].t() - lower_expand_t[length - 2].t(), 0.1);
  const double lower_right_delta_s =
      lower_expand_t[length - 1].s() - lower_expand_t[length - 2].s();
  const double upper_right_delta_s =
      upper_expand_t[length - t].s() - upper_expand_t[length - 2].s();

  lower_expand_t.emplace_back(port::STPoint(
      lower_expand_t.back().s() + t * lower_right_delta_s / right_delta_t,
      lower_expand_t.back().t() + t));
  upper_expand_t.emplace_back(port::STPoint(
      upper_expand_t.back().s() + t * upper_right_delta_s / right_delta_t,
      upper_expand_t.back().t() + t));
  upper_expand_t.back().set_s(
      std::max(upper_expand_t.back().s(),
               lower_expand_t.back().s() + tport::kMathEpsilon));

  lower_pts = lower_expand_t;
  upper_pts = upper_expand_t;
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
