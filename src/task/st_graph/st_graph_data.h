// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/common.h>
#include "port/st/speed_limit.h"
#include "port/st/st_boundary.h"

namespace trunk {
namespace pnd {
namespace task {

class StGraphData {
 public:
  StGraphData(const std::vector<const port::STBoundary*>& st_boundaries,
              const tport::TrajectoryPoint& init_point,
              const port::SpeedLimit& speed_limit,
              const double path_data_length);

  StGraphData() = default;

  const std::vector<const port::STBoundary*>& st_boundaries() const;

  const tport::TrajectoryPoint& init_point() const;

  const port::SpeedLimit& speed_limit() const;

  double path_data_length() const;

 private:
  std::vector<const port::STBoundary*> st_boundaries_;
  tport::TrajectoryPoint init_point_;

  port::SpeedLimit speed_limit_;
  double path_data_length_ = 0.0;
};
}  // namespace task
}  // namespace pnd
}  // namespace trunk
