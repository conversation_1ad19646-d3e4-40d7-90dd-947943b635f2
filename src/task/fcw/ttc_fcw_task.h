// Copyright 2024, trunk Inc. All rights reserved

#pragma once

#include "task/task.h"

namespace trunk {
namespace pnd {

class TtcFcwTaskParam;

namespace task {

class TtcFcwTask : public Task {
 public:
  TtcFcwTask();

  virtual ~TtcFcwTask() = default;

  tpnc::Status Execute(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info) override;

 private:
  const TtcFcwTaskParam* config_;

  tutil::TruckParam truck_param_;
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
