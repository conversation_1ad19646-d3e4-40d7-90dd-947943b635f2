// Copyright 2024, trunk Inc. All rights reserved

#include "ttc_fcw_task.h"

#include "param/ttc_fcw_task_param.h"
#include "task/task.h"

namespace trunk {
namespace pnd {
namespace task {

namespace {

constexpr double kObstacleStaticThreshold = tutil::Kmh2Ms(3.0);

}  // namespace

REGISTER(trunk::pnd::task, Task, TtcFcw);

TtcFcwTask::TtcFcwTask() : Task("ttc fcw") {
  const auto model = tpnc::Singleton<tmodel::Truck>::GetInstance();
  truck_param_ = tutil::TruckParam(*model);
}

tpnc::Status TtcFcwTask::Execute(
    const std::shared_ptr<port::Frame>& frame,
    port::ReferenceLineInfo* const reference_line_info) {
  // sanity check
  if (reference_line_info == nullptr) {
    return tpnc::Status(tpnc::ErrorCode::PLANNING_NULLPTR,
                        "reference_line_info nullptr");
  }

  // TODO: 考虑抽象该部分
  int temp_idx_path = std::numeric_limits<int>::max();
  int temp_idx_obj = 0;
  const auto& trajectory = reference_line_info->trajectory();
  const auto& obs = frame->env().obstacles();
  double obj_v = std::numeric_limits<double>::max();
  double obj_distance = std::numeric_limits<double>::max();
  if (trajectory.empty() || obs.empty()) {
    return tpnc::Status::OK();
  }
  if (tutil::Collision::DetectObstaclesForPath(
          trajectory, tutil::GetConvexes(obs), truck_param_, temp_idx_path,
          temp_idx_obj)) {
    const double path_point_theta = trajectory.at(temp_idx_path).theta();
    obj_distance = trajectory.at(temp_idx_path).s();
    obj_v = obs.at(temp_idx_obj).xy_velocity().x() * cos(path_point_theta) +
            obs.at(temp_idx_obj).xy_velocity().y() * sin(path_point_theta);
    if (obj_v < -tutil::Kmh2Ms(5.0)) {
      // 对向来车不考虑
      return tpnc::Status::OK();
    }
    if (obj_v < kObstacleStaticThreshold) {
      obj_v = 0.0;
    }
  } else {
    return tpnc::Status::OK();
  }

  const double self_v = frame->env().vehicle().velocity();
  if (obj_distance < 0.0) {
    return tpnc::Status::OK();
  }
  if (self_v < config_->valid_speed()) {
    return tpnc::Status::OK();
  }
  const double relative_v = self_v - obj_v;
  if (relative_v <= 0.0) {
    return tpnc::Status::OK();
  }
  // AEB FCW max_dec判断
  const double ttc = obj_distance / relative_v;
  if (ttc >= 0.0 && ttc <= config_->ttc_level_3()) {
    reference_line_info->mutable_flags_set()->insert("fcw3");
  } else if (ttc > config_->ttc_level_3() && ttc <= config_->ttc_level_2()) {
    reference_line_info->mutable_flags_set()->insert("fcw2");
  } else if (ttc > config_->ttc_level_2() && ttc <= config_->ttc_level_1()) {
    reference_line_info->mutable_flags_set()->insert("fcw1");
  }
  // TODO: AEB

  return tpnc::Status::OK();
}

}  // namespace task
}  // namespace pnd
}  // namespace trunk
