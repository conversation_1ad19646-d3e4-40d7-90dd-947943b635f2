// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include "port/frame.h"

namespace trunk {
namespace pnd {
namespace task {

class Task {
 public:
  Task() = default;

  explicit Task(const std::string s) : name_(s) {}

  virtual ~Task() = default;

  virtual tpnc::Status Execute(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info) = 0;

  const std::string& GetName() const { return name_; }

  tpnc::Status ExecuteAndCalRunTime(
      const std::shared_ptr<port::Frame>& frame,
      port::ReferenceLineInfo* const reference_line_info);

 private:
  std::string name_;
};

}  // namespace task
}  // namespace pnd
}  // namespace trunk
