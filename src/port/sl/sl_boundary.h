// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/common.h>
#include <trunk/common/config.h>

namespace trunk {
namespace pnd {
namespace port {

/////////////////////////////////////////////////////////////////
// The min_s and max_s are longitudinal values.
// min_s <= max_s.
//
//              max_s
//                ^
//                |
//          S  direction
//                |
//            min_s
//
// The min_l and max_l are lateral values.
// min_l <= max_l. Left side of the reference line is positive,
// and right side of the reference line is negative.
//  max_l  <-----L direction---- min_l
/////////////////////////////////////////////////////////////////

class SLBoundary {
 public:
  SLBoundary(const double min_s, const double max_s, const double min_l,
             const double max_l);
  SLBoundary() = default;

  SLBoundary& operator+=(const tport::SLPoint& sl);
  SLBoundary& operator-=(const tport::SLPoint& sl);
  SLBoundary& operator+=(const SLBoundary& other);
  SLBoundary& operator-=(const SLBoundary& other);
  SLBoundary& operator*=(const double ratio);

  SLBoundary operator+(const tport::SLPoint& sl) const;
  SLBoundary operator-(const tport::SLPoint& sl) const;

  bool CollisionCheck(const SLBoundary& other) const;
  double CalcDistence(const SLBoundary& other) const;
  double CalcDistence(double lon, double lat) const;
  double CalcLonDistence(const SLBoundary& other) const;
  double CalcLatDistence(const SLBoundary& other) const;

 private:
  MEMBER_BASIC_TYPE(double, min_s, std::numeric_limits<double>::max());
  MEMBER_BASIC_TYPE(double, max_s, std::numeric_limits<double>::lowest());
  MEMBER_BASIC_TYPE(double, min_l, std::numeric_limits<double>::max());
  MEMBER_BASIC_TYPE(double, max_l, std::numeric_limits<double>::lowest());
};

}  // namespace port
}  // namespace pnd
}  // namespace trunk
