// Copyright 2023, trunk Inc. All rights reserved

#include "path_point.h"

namespace trunk {
namespace pnd {
namespace port {

// Sums two TrajectoryPoint
PathSLPoint& PathSLPoint::operator+=(const PathSLPoint& other) {
  SLPoint::operator+=(other);
  dl_ += other.dl_;
  ddl_ += other.ddl_;
  return *this;
}

// Subtracts two TrajectoryPoint
PathSLPoint& PathSLPoint::operator-=(const PathSLPoint& other) {
  SLPoint::operator-=(other);
  dl_ -= other.dl_;
  ddl_ -= other.ddl_;
  return *this;
}

// Multiplies TrajectoryPoint by a scalar
PathSLPoint& PathSLPoint::operator*=(const double ratio) {
  SLPoint::operator*=(ratio);
  dl_ *= ratio;
  ddl_ *= ratio;
  return *this;
}

}  // namespace port
}  // namespace pnd
}  // namespace trunk
