// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/common.h>

namespace trunk {
namespace pnd {
namespace port {

class PathSLPoint : public tport::SLPoint {
 public:
  PathSLPoint() = default;
  PathSLPoint(double s, double l, double dl, double ddl)
      : SLPoint(s, l), dl_(dl), ddl_(ddl) {}

  MEMBER_BASIC_TYPE(double, dl, 0.0);
  MEMBER_BASIC_TYPE(double, ddl, 0.0);

  // Sums two TrajectoryPoint
  PathSLPoint& operator+=(const PathSLPoint& other);

  // Subtracts two TrajectoryPoint
  PathSLPoint& operator-=(const PathSLPoint& other);

  // Multiplies TrajectoryPoint by a scalar
  PathSLPoint& operator*=(const double ratio);
};

using SLPointPair = std::pair<tport::SLPoint, tport::SLPoint>;
using SLPoints = std::vector<tport::SLPoint>;
using SLBoundaries = std::pair<SLPoints, SLPoints>;
using PathSLPoints = std::vector<PathSLPoint>;

}  // namespace port
}  // namespace pnd
}  // namespace trunk
