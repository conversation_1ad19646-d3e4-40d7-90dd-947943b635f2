// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include "path_point.h"

namespace trunk {
namespace pnd {
namespace port {

class PathSLData : public PathSLPoints {
 public:
  const PathSLPoint EvaluateByS(const double s) const;
  bool EvaluateByS(const double s, PathSLPoint* const sl_point) const;
};

class PathData {
 public:
  PathData() = default;

  double TotalLength() const;

  // get set members
  // normal coordinate path
  MEMBER_COMPLEX_TYPE(PathSLData, dp_sl_path);
  MEMBER_COMPLEX_TYPE(tport::Path, dp_norm_path);
  MEMBER_COMPLEX_TYPE(PathSLData, qp_sl_path);
  MEMBER_COMPLEX_TYPE(tport::Path, qp_norm_path);
};

}  // namespace port
}  // namespace pnd
}  // namespace trunk
