// Copyright 2023, trunk Inc. All rights reserved

#include "path_data.h"

#include "log.h"

namespace trunk {
namespace pnd {
namespace port {

const PathSLPoint PathSLData::EvaluateByS(const double s) const {
  PathSLPoint sl_point;
  if (!EvaluateByS(s, &sl_point)) {
    TRUNK_LOG_WARN << "Evaluate sl_point failure!";
  }
  return sl_point;
}

bool PathSLData::EvaluateByS(const double s,
                             PathSLPoint* const sl_point) const {
  if (size() < 2) {
    TRUNK_LOG_WARN << "size = " << size() << ", too short!";
    return false;
  }
  if (!(front().s() < s + 1.0 && s - 1.0 < back().s())) {
    TRUNK_LOG_WARN << "length = " << back().s() - front().s() << ", too short! "
                   << "s = " << s << ", front = " << front().s()
                   << ", back = " << back().s();
    return false;
  }

  auto comp = [](const PathSLPoint& sl, const double s) { return sl.s() < s; };

  auto it_lower = std::lower_bound(begin(), end(), s, comp);
  if (it_lower == end()) {
    *sl_point = back();
  } else if (it_lower == begin()) {
    *sl_point = front();
  } else {
    const auto& p0 = *(it_lower - 1);
    const auto& p1 = *it_lower;
    double s0 = p0.s();
    double s1 = p1.s();

    sl_point->set_s(s);

    double l = tutil::Lerp(p0.l(), s0, p1.l(), s1, s);
    sl_point->set_l(l);

    double dl = tutil::Lerp(p0.dl(), s0, p1.dl(), s1, s);
    sl_point->set_dl(dl);

    double ddl = tutil::Lerp(p0.ddl(), s0, p1.ddl(), s1, s);
    sl_point->set_ddl(ddl);
  }
  return true;
}

double PathData::TotalLength() const {
  if (!qp_norm_path_.empty()) {
    return qp_norm_path_.back().s();
  }
  if (!dp_norm_path_.empty()) {
    return dp_norm_path_.back().s();
  }
  return 0.0;
}

}  // namespace port
}  // namespace pnd
}  // namespace trunk
