// Copyright 2023, trunk Inc. All rights reserved

#include "port/sl/sl_boundary.h"

#include <cmath>
namespace trunk {
namespace pnd {
namespace port {

SLBoundary::SLBoundary(const double min_s, const double max_s,
                       const double min_l, const double max_l)
    : min_s_(min_s), max_s_(max_s), min_l_(min_l), max_l_(max_l) {}

SLBoundary& SLBoundary::operator+=(const tport::SLPoint& sl) {
  this->min_l_ += sl.l();
  this->max_l_ += sl.l();
  this->min_s_ += sl.s();
  this->max_s_ += sl.s();
  return *this;
}

SLBoundary& SLBoundary::operator-=(const tport::SLPoint& sl) {
  this->min_l_ -= sl.l();
  this->max_l_ -= sl.l();
  this->min_s_ -= sl.s();
  this->max_s_ -= sl.s();
  return *this;
}

SLBoundary& SLBoundary::operator+=(const SLBoundary& other) {
  this->min_l_ += other.min_l_;
  this->max_l_ += other.max_l_;
  this->min_s_ += other.min_s_;
  this->max_s_ += other.max_s_;
  return *this;
}

SLBoundary& SLBoundary::operator-=(const SLBoundary& other) {
  this->min_l_ -= other.min_l_;
  this->max_l_ -= other.max_l_;
  this->min_s_ -= other.min_s_;
  this->max_s_ -= other.max_s_;
  return *this;
}

SLBoundary& SLBoundary::operator*=(const double ratio) {
  this->min_l_ *= ratio;
  this->max_l_ *= ratio;
  this->min_s_ *= ratio;
  this->max_s_ *= ratio;
  return *this;
}

SLBoundary SLBoundary::operator+(const tport::SLPoint& sl) const {
  return SLBoundary(*this) += sl;
}
SLBoundary SLBoundary::operator-(const tport::SLPoint& sl) const {
  return SLBoundary(*this) -= sl;
}

bool SLBoundary::CollisionCheck(const SLBoundary& other) const {
  if (this->max_s_ >= other.min_s_ && this->min_s_ <= other.max_s_ &&
      this->max_l_ >= other.min_l_ && this->min_l_ <= other.max_l_) {
    return true;
  }
  return false;
}
double SLBoundary::CalcLonDistence(const SLBoundary& other) const {
  return std::max(
      0.0, std::max(other.min_s_ - this->max_s_, this->min_s_ - other.max_s_));
}
double SLBoundary::CalcLatDistence(const SLBoundary& other) const {
  return std::max(
      0.0, std::max(other.min_l_ - this->max_l_, this->min_l_ - other.max_l_));
}
double SLBoundary::CalcDistence(const SLBoundary& other) const {
  return CalcDistence(CalcLonDistence(other), CalcLatDistence(other));
}
double SLBoundary::CalcDistence(double lon, double lat) const {
  return std::hypot(std::max(0.0, lon), std::max(0.0, lat));
}

}  // namespace port
}  // namespace pnd
}  // namespace trunk
