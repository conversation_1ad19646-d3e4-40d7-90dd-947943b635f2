// Copyright 2023, trunk Inc. All rights reserved

#include "reference_line_info.h"

#include <jian/spdlog/fmt/bundled/core.h>

#include <iterator>
#include <limits>

#include "log.h"
#include "port/st/speed_data.h"
#include "port/st/speed_point.h"
#include "trunk/common/port/base_point.h"
#include "trunk/common/port/plan_point.h"
#include "trunk/common/util/coordinate/transformfrenet.h"
#include "util/key.h"
#include "util/util.h"

namespace trunk {
namespace pnd {
namespace port {

bool ReferenceLineInfo::Init(const tport::PredictionObstacles& obstacles,
                             const tport::VehiclePose& vehicle,
                             const tport::PathPoint& init_point,
                             const tport::PathPoint& path_init_point,
                             const tport::PathPoint& target_point) {
  tutil::TransformUtm::Utm2Vehicle(vehicle, reference_line_);
  sl_init_point_ = CalculateSlPoint(init_point);
  auto oper = [this](auto& p) { p.set_s(p.s() - sl_init_point_.s()); };
  std::for_each(origin_reference_line_.begin(), origin_reference_line_.end(),
                oper);
  std::for_each(reference_line_.begin(), reference_line_.end(), oper);
  std::for_each(sl_lane_boundaries_.first.begin(),
                sl_lane_boundaries_.first.end(), oper);
  std::for_each(sl_lane_boundaries_.second.begin(),
                sl_lane_boundaries_.second.end(), oper);
  std::for_each(sl_road_boundaries_.first.begin(),
                sl_road_boundaries_.first.end(), oper);
  std::for_each(sl_road_boundaries_.second.begin(),
                sl_road_boundaries_.second.end(), oper);
  std::for_each(sl_drivable_boundaries_.first.begin(),
                sl_drivable_boundaries_.first.end(), oper);
  std::for_each(sl_drivable_boundaries_.second.begin(),
                sl_drivable_boundaries_.second.end(), oper);
  std::for_each(sl_ego_road_boundaries_.first.begin(),
                sl_ego_road_boundaries_.first.end(), oper);
  std::for_each(sl_ego_road_boundaries_.second.begin(),
                sl_ego_road_boundaries_.second.end(), oper);
  std::for_each(sl_ego_drivable_boundaries_.first.begin(),
                sl_ego_drivable_boundaries_.first.end(), oper);
  std::for_each(sl_ego_drivable_boundaries_.second.begin(),
                sl_ego_drivable_boundaries_.second.end(), oper);
  sl_init_point_.set_s(0.0);
  self_sl_ = tutil::TransformFrenet::TransformToFrenet(reference_line_,
                                                       tport::Point2D());
  sl_path_init_point_ = CalculateSlPoint(path_init_point);
  sl_target_point_ = CalculateSlPoint(target_point);

  auto erase = [](auto& vec, int begin_idx, int end_idx) {
    if (end_idx < vec.size()) {
      vec.erase(vec.begin() + end_idx, vec.end());
    }
    if (begin_idx <= vec.size()) {
      vec.erase(vec.begin(), vec.begin() + begin_idx);
    }
  };
  const int begin_idx = std::distance(
      reference_line_.begin(),
      std::lower_bound(
          reference_line_.begin(), reference_line_.end(),
          -util::sys_config_.ref_line_back_length(),
          [](const tport::PathPoint& p, double s) { return p.s() < s; }));
  const int end_idx = std::distance(
      reference_line_.begin(),
      std::lower_bound(
          reference_line_.begin(), reference_line_.end(),
          util::sys_config_.ref_line_front_length(),
          [](const tport::PathPoint& p, double s) { return p.s() <= s; }));
  const int refernece_end_idx = end_idx - 2;
  erase(reference_line_, begin_idx, std::max(begin_idx, refernece_end_idx));
  if (reference_line_.size() < 2) {
    return false;
  }
  erase(sl_lane_boundaries_.first, begin_idx, end_idx);
  erase(sl_lane_boundaries_.second, begin_idx, end_idx);
  erase(sl_road_boundaries_.first, begin_idx, end_idx);
  erase(sl_road_boundaries_.second, begin_idx, end_idx);
  erase(sl_drivable_boundaries_.first, begin_idx, end_idx);
  erase(sl_drivable_boundaries_.second, begin_idx, end_idx);
  erase(lane_boundaries_.first, begin_idx, end_idx);
  erase(lane_boundaries_.second, begin_idx, end_idx);
  erase(road_boundaries_.first, begin_idx, end_idx);
  erase(road_boundaries_.second, begin_idx, end_idx);
  erase(drivable_boundaries_.first, begin_idx, end_idx);
  erase(drivable_boundaries_.second, begin_idx, end_idx);
  const int ego_begin_idx = std::distance(
      sl_ego_road_boundaries_.first.begin(),
      std::lower_bound(
          sl_ego_road_boundaries_.first.begin(),
          sl_ego_road_boundaries_.first.end(),
          -util::sys_config_.ref_line_back_length(),
          [](const tport::SLPoint& p, double s) { return p.s() < s; }));
  const int ego_end_idx = std::distance(
      sl_ego_drivable_boundaries_.first.begin(),
      std::lower_bound(
          sl_ego_drivable_boundaries_.first.begin(),
          sl_ego_drivable_boundaries_.first.end(),
          util::sys_config_.ref_line_front_length(),
          [](const tport::SLPoint& p, double s) { return p.s() <= s; }));
  erase(sl_ego_road_boundaries_.first, ego_begin_idx, ego_end_idx);
  erase(sl_ego_road_boundaries_.second, ego_begin_idx, ego_end_idx);
  erase(sl_ego_drivable_boundaries_.first, ego_begin_idx, ego_end_idx);
  erase(sl_ego_drivable_boundaries_.second, ego_begin_idx, ego_end_idx);
  erase(ego_road_boundaries_.first, ego_begin_idx, ego_end_idx);
  erase(ego_road_boundaries_.second, ego_begin_idx, ego_end_idx);
  erase(ego_drivable_boundaries_.first, ego_begin_idx, ego_end_idx);
  erase(ego_drivable_boundaries_.second, ego_begin_idx, ego_end_idx);
  tutil::TransformUtm::Utm2Vehicle(vehicle, origin_reference_line_);
  tutil::TransformUtm::Utm2Vehicle2D(vehicle, lane_boundaries_.first);
  tutil::TransformUtm::Utm2Vehicle2D(vehicle, lane_boundaries_.second);
  tutil::TransformUtm::Utm2Vehicle2D(vehicle, road_boundaries_.first);
  tutil::TransformUtm::Utm2Vehicle2D(vehicle, road_boundaries_.second);
  tutil::TransformUtm::Utm2Vehicle2D(vehicle, drivable_boundaries_.first);
  tutil::TransformUtm::Utm2Vehicle2D(vehicle, drivable_boundaries_.second);
  tutil::TransformUtm::Utm2Vehicle2D(vehicle, ego_road_boundaries_.first);
  tutil::TransformUtm::Utm2Vehicle2D(vehicle, ego_road_boundaries_.second);
  tutil::TransformUtm::Utm2Vehicle2D(vehicle, ego_drivable_boundaries_.first);
  tutil::TransformUtm::Utm2Vehicle2D(vehicle, ego_drivable_boundaries_.second);
  for (int i = infos_.size() - 1; i >= 0; --i) {
    if (infos_[i].begin_idx >= refernece_end_idx) {
      infos_.pop_back();
      continue;
    }
    infos_[i].end_idx = std::min(infos_[i].end_idx, refernece_end_idx);
    infos_[i].end_idx -= begin_idx;
    infos_[i].begin_idx -= begin_idx;
    infos_[i].begin_idx = std::max(0, infos_[i].begin_idx);
    if (infos_[i].end_idx < 0) {
      infos_.erase(infos_.begin(), infos_.begin() + i);
      break;
    }
  }
  if (util::last_frame_ != nullptr) {
    for (auto& info : infos_) {
      auto* last_reference_line_info =
          util::last_frame_->GetReferenceLineById(info.lane_id);
      if (last_reference_line_info) {
        last_reference_line_info_ = last_reference_line_info;
        break;
      }
    }
    if (last_reference_line_info_) {
      to_auto_drive_ = last_reference_line_info_->to_auto_drive();
      using_sl_adaptive_ = last_reference_line_info_->using_sl_adaptive();
    }
    // 是否是变道车道，以及变道方向的判断
    const auto* last_drive_reference_line_info =
        util::last_frame_->drive_reference_line_info();
    if (last_drive_reference_line_info == nullptr) {
      TRUNK_LOG_WARN << "last frame drive refernece line info is nullptr";
    } else if (last_drive_reference_line_info->reference_line().empty()) {
      TRUNK_LOG_WARN << "last frame refernece line is empty";
    } else if (last_reference_line_info_ != last_drive_reference_line_info) {
      // 转向标志
      const auto sl = last_drive_reference_line_info->TransformToFrenet(
          reference_line_.front());
      if (std::abs(sl.l()) > util::sys_config_.lane_change_lat_thres()) {
        lane_change_flag_ = sl.l() > 0 ? tpnc::TurnSignal::LEFT_TURN
                                       : tpnc::TurnSignal::RIGHT_TURN;
      }
    } else {
      // 超车标志: 上一帧走的这条道，且是超车状态
      if (last_reference_line_info_->overtake_flag()) {
        overtake_flag_ = true;
      }
    }
  }
  tutil::TruckParam truck_param(util::model_);

  tutil::TruckModel vehicle_model(truck_param);
  vehicle_model.RectangleRep(tport::Point4D());
  tutil::TruckModel adc_model(truck_param);
  adc_model.RectangleRep(init_point);

  tport::Contour2D vehicle_temp = vehicle_model.contour();
  tport::Contour2D adc_temp = adc_model.contour();
  if (util::sys_config_.with_trailer()) {
    // 带挂模型
    // vehicle
    vehicle_model.trailer().RectangleRep(tport::Point4D());
    vehicle_temp.reserve(vehicle_model.contour().size() +
                         vehicle_model.trailer().contour().size());
    for (const auto& pt : vehicle_model.trailer().contour()) {
      vehicle_temp.emplace_back(pt);
    }
    adc_model.trailer().RectangleRep(init_point);
    adc_temp.reserve(adc_model.contour().size() +
                     adc_model.trailer().contour().size());
    for (const auto& pt : adc_model.trailer().contour()) {
      adc_temp.emplace_back(pt);
    }
  }

  sl_boundary_info_.vehicle_sl_boundary_ =
      util::GenerateSLBound(reference_line_, vehicle_temp);
  sl_boundary_info_.adc_sl_boundary_ =
      util::GenerateSLBound(reference_line_, adc_temp);

  for (const auto& obstacle : obstacles) {
    if (AddObstacle(obstacle) == nullptr) {
      TRUNK_LOG_ERROR << "Obstacles add failure!!!";
      return false;
    }
  }
  for (auto& info : infos_) {
    id_ += ("-" + info.lane_id + "-");
  }
  return true;
}

const ReferenceLineInfo::Info* ReferenceLineInfo::GetInfoByS(double s) const {
  if (infos_.empty() || infos_.front().begin_idx < 0) {
    return nullptr;
  }
  if (reference_line_[infos_.front().begin_idx].s() >= s) {
    return &infos_.front();
  }
  auto it = std::lower_bound(infos_.begin(), infos_.end(), s,
                             [this](const auto& info, double s) {
                               return reference_line_[info.begin_idx].s() < s;
                             });
  if (it == infos_.begin()) {
    return &infos_.front();
  }
  return &*(--it);
}

Obstacle* ReferenceLineInfo::AddObstacle(
    const tport::PredictionObstacle& obstacle) {
  auto* m_obs = obstacles_.Add(obstacle.id(), obstacle);
  if (m_obs == nullptr) {
    return nullptr;
  }
  m_obs->set_sl_boundary(
      util::GenerateSLBound(reference_line_, obstacle.xy_contour()));
  m_obs->mutable_tsl_boundarys()->emplace_back(0.0, m_obs->sl_boundary());
  const auto sl0 = TransformToFrenet(obstacle.xy_center());
  auto add_tsl_boundary = [this, &m_obs,
                           &sl0](const tport::TrajectoryPoint& p) {
    tport::Point2D xy_velocity(std::cos(p.theta()) * p.v(),
                               std::sin(p.theta()) * p.v());
    const auto match_point = tutil::GetClosestMatchPoint(reference_line_, p);
    const double delta_theta =
        tutil::NormalizeAngle(p.theta() - match_point.theta());
    const tport::SLPoint sl(match_point.s(),
                            tutil::CartesianFrenetConverter::CalculateLateral(
                                match_point.x(), match_point.y(),
                                match_point.theta(), p.x(), p.y()));
    tport::SLPoint speed_sl(std::cos(delta_theta) * p.v(),
                            std::sin(delta_theta) * p.v());
    tport::SLPoint acc_sl(std::cos(delta_theta) * p.a(),
                          std::sin(delta_theta) * p.a());
    auto delta_sl = sl - sl0;
    m_obs->mutable_tsl_boundarys()->emplace_back(
        p.relative_time(), m_obs->sl_boundary() + delta_sl, speed_sl, acc_sl);
  };
  tport::TrajectoryPoint p0;
  p0.set_x(obstacle.xy_center().x());
  p0.set_y(obstacle.xy_center().y());
  p0.set_v(obstacle.velocity());
  if (!obstacle.trajectory().empty()) {
    p0.set_a(obstacle.trajectory().front().a());
    p0.set_theta(obstacle.trajectory().front().theta());
  } else {
    p0.set_theta(obstacle.heading());
    p0.set_a(0.0);
  }
  add_tsl_boundary(p0);
  for (auto p : obstacle.trajectory()) {
    add_tsl_boundary(p);
  }
  return m_obs;
}

bool ReferenceLineInfo::GetBoundary(double s, const SLBoundaries& boundaries,
                                    LRWidth& lr) const {
  if (boundaries.first.size() < 2 || boundaries.second.size() < 2) {
    TRUNK_LOG_WARN << "the size of boundary < 2";
    return false;
  }
  auto cal_l = [](const SLPoints& boundary, const double s) {
    auto iter_back = std::lower_bound(
        boundary.begin(), boundary.end(), s,
        [](const tport::SLPoint& p, const double s) { return p.s() < s; });
    auto iter_front = iter_back;
    if (iter_back == boundary.begin()) {
      std::advance(iter_back, 1);
    } else if (iter_front == boundary.end()) {
      std::advance(iter_back, -1);
      std::advance(iter_front, -2);
    } else {
      iter_front = std::prev(iter_back, 1);
    }
    return tutil::Lerp(iter_front->l(), iter_front->s(), iter_back->l(),
                       iter_back->s(), s);
  };
  lr.left_width = cal_l(boundaries.first, s);
  lr.right_width = cal_l(boundaries.second, s);
  return true;
}
const ReferenceLineInfo::LRWidth ReferenceLineInfo::GetEgoDrivableWidth(
    double s) const {
  LRWidth lr(std::numeric_limits<double>::max(),
             std::numeric_limits<double>::lowest());
  if (!GetBoundary(s, sl_ego_drivable_boundaries_, lr)) {
    TRUNK_LOG_WARN << "GetEgoDrivableWidth failure!!!---s = " << s
                   << ", reference_line.front.s = "
                   << reference_line_.front().s()
                   << ", reference_line.back.s = " << reference_line_.back().s()
                   << ", using default road width(12m).";
    lr.left_width = 6.0;
    lr.right_width = -6.0;
  }
  return lr;
}

const ReferenceLineInfo::LRWidth ReferenceLineInfo::GetEgoRoadWidth(
    double s) const {
  LRWidth lr(std::numeric_limits<double>::max(),
             std::numeric_limits<double>::lowest());
  if (!GetBoundary(s, sl_ego_road_boundaries_, lr)) {
    TRUNK_LOG_WARN << "GetEgoRoadWidth failure!!!---s = " << s
                   << ", reference_line.front.s = "
                   << reference_line_.front().s()
                   << ", reference_line.back.s = " << reference_line_.back().s()
                   << ", using default road width(12m).";
    lr.left_width = 6.0;
    lr.right_width = -6.0;
  }
  return lr;
}

const ReferenceLineInfo::LRWidth ReferenceLineInfo::GetDrivableWidth(
    double s) const {
  LRWidth lr(std::numeric_limits<double>::max(),
             std::numeric_limits<double>::lowest());
  if (!GetBoundary(s, sl_drivable_boundaries_, lr)) {
    TRUNK_LOG_WARN << "GetDrivableWidth failure!!!---s = " << s
                   << ", reference_line.front.s = "
                   << reference_line_.front().s()
                   << ", reference_line.back.s = " << reference_line_.back().s()
                   << ", using default road width(12m).";
    lr.left_width = 6.0;
    lr.right_width = -6.0;
  }
  return lr;
}

const ReferenceLineInfo::LRWidth ReferenceLineInfo::GetRoadWidth(
    double s) const {
  LRWidth lr(std::numeric_limits<double>::max(),
             std::numeric_limits<double>::lowest());
  if (!GetBoundary(s, sl_road_boundaries_, lr)) {
    TRUNK_LOG_WARN << "GetRoadWidth failure!!!---s = " << s
                   << ", reference_line.front.s = "
                   << reference_line_.front().s()
                   << ", reference_line.back.s = " << reference_line_.back().s()
                   << ", using default road width(12m).";
    lr.left_width = 6.0;
    lr.right_width = -6.0;
  }
  return lr;
}

const ReferenceLineInfo::LRWidth ReferenceLineInfo::GetLaneWidth(
    double s) const {
  LRWidth lr(std::numeric_limits<double>::max(),
             std::numeric_limits<double>::lowest());
  if (!GetBoundary(s, sl_lane_boundaries_, lr)) {
    TRUNK_LOG_WARN << "GetLaneWidth failure!!!---s = " << s
                   << ", using default lane width(4.0m).";
    lr.left_width = util::kHighwayLaneHalfWidth;
    lr.right_width = -util::kHighwayLaneHalfWidth;
  }
  return lr;
}

bool ReferenceLineInfo::IsStartFrom(
    const ReferenceLineInfo& another_line) const {
  if (reference_line_.empty() || another_line.reference_line().empty()) {
    return false;
  }
  auto sl = tutil::TransformFrenet::TransformToFrenet(
      reference_line_, another_line.reference_line().front());
  if (std::fabs(sl.l()) > util::sys_config_.lane_change_lat_thres() ||
      sl.s() < util::sys_config_.lane_change_lon_thres()) {
    return false;
  }
  return true;
}

bool ReferenceLineInfo::CombinePathAndSpeedProfile(double relative_time,
                                                   double start_s) {
  if (path_data_.qp_norm_path().empty()) {
    TRUNK_LOG_ERROR << "qp_norm_path is empty!!!";
    return false;
  }
  if (speed_data_.empty()) {
    TRUNK_LOG_ERROR << "speed_data is empty!!!";
    return false;
  }
  trajectory_ = tport::Trajectory(path_data_.qp_norm_path());
  bool in_speed_data = true;
  const double init_s = trajectory_.front().s();
  double t = 0.0;
  for (auto& p : trajectory_) {
    p.set_s(p.s() - init_s);
    if (p.s() > speed_data_.back().s()) {
      in_speed_data = false;
    }
    if (in_speed_data) {
      SpeedSTPoint speed;
      if (!speed_data_.EvaluateByS(p.s(), &speed)) {
        speed = speed_data_.front();
      }
      p.set_relative_time(speed.t());
      p.set_v(speed.v());
      p.set_a(speed.a());
      t = p.relative_time();
    } else {
      t += 0.1;
      p.set_relative_time(t);
      p.set_v(speed_data_.back().v());
      p.set_a(speed_data_.back().a());
    }
    p.set_s(p.s() + start_s);
  }
  // for (double cur_rel_time = 0.0; cur_rel_time < speed_data_.TotalTime();
  //      cur_rel_time += util::sys_config_.time_resolution()) {
  //   port::SpeedSTPoint speed_point;
  //   if (!speed_data_.EvaluateByTime(cur_rel_time, &speed_point)) {
  //     TRUNK_LOG_ERROR << "Fail to get speed point with relative time "
  //                     << cur_rel_time;
  //     return false;
  //   }
  //   if (speed_point.s() > path_data_.TotalLength()) {
  //     break;
  //   }
  //   // auto path_point =
  //   path_data_.qp_norm_path().EvaluateByS(speed_point.s()); auto path_point =
  //       SlPoint2PathPoint(path_data_.qp_sl_path().EvaluateByS(speed_point.s()));
  //   path_point.set_s(path_point.s() + start_s);

  //  tport::TrajectoryPoint trajectory_point(path_point);
  //  trajectory_point.set_v(speed_point.v());
  //  trajectory_point.set_a(speed_point.a());
  //  trajectory_point.set_relative_time(speed_point.t() + relative_time);
  //  trajectory_.push_back(std::move(trajectory_point));
  //}
  return true;
}

tport::PathPoint ReferenceLineInfo::EvaluateByS(double s) const {
  if (reference_line_.size() == 1) {
    return reference_line_.back();
  }
  auto it = std::lower_bound(
      reference_line_.begin(), reference_line_.end(), s,
      [](const auto& tp, const auto path_s) { return tp.s() < path_s; });
  auto it_upper = it;
  auto it_lower = it;
  if (it_upper == reference_line_.end()) {
    --it_upper;
    it_lower = it_upper - 1;
  } else if (it_lower == reference_line_.begin()) {
    it_upper = it_lower + 1;
  } else {
    it_lower = it_upper - 1;
  }
  double weight = (s - it_lower->s()) / (it_upper->s() - it_lower->s());

  auto p = tutil::Lerp(*it_lower, *it_upper, weight);
  p.set_theta(tutil::NormalizeAngle(
      it_lower->theta() +
      tutil::Lerp(0.0,
                  tutil::NormalizeAngle(it_upper->theta() - it_lower->theta()),
                  weight)));
  if (it == reference_line_.end() || it == reference_line_.begin()) {
    p.set_theta(it_upper->theta());
    p.set_kappa(0.0);
    p.set_dkappa(0.0);
    p.set_theta(std::atan2(it_upper->y() - it_lower->y(),
                           it_upper->x() - it_lower->x()));
  }
  return p;
}

void ReferenceLineInfo::QpSlPath2XyPath(const tport::Trajectory& stitch_path) {
  auto& path = *mutable_path_data()->mutable_qp_norm_path();
  auto& path_sl = *mutable_path_data()->mutable_qp_sl_path();
  const double s_diff = stitch_path.back().s() - path_sl.front().s();
  const int size = stitch_path.size() - 1;
  path.reserve(path_sl.size() + size);
  path_sl.insert(path_sl.begin(), size, port::PathSLPoint());
  for (int i = 0; i < size; i++) {
    const auto& point = stitch_path[i];
    path.emplace_back(tport::PathPoint(point));
    const auto sl_point = TransformToFrenet(stitch_path[i]);
    path_sl[i].set_s(sl_point.s());
    path_sl[i].set_l(sl_point.l());
    path.back().set_s(sl_point.s());
    const double theta = point.theta();
    const double kappa = point.kappa();
    auto ref_point = EvaluateByS(sl_point.s());
    const double theta_ref = ref_point.theta();  // 车体坐标系
    const double kappa_ref = ref_point.kappa();
    const double dkappa_ref = ref_point.dkappa();
    const double dl =
        tutil::CartesianFrenetConverter::CalculateLateralDerivative(
            theta_ref, theta, sl_point.l(), kappa_ref);
    const double ddl =
        tutil::CartesianFrenetConverter::CalculateSecondOrderLateralDerivative(
            theta_ref, theta, kappa_ref, kappa, dkappa_ref, sl_point.l());
    path_sl[i].set_dl(dl);
    path_sl[i].set_ddl(ddl);
  }
  for (size_t i = size; i < path_sl.size(); ++i) {
    path.emplace_back();
    SlPoint2PathPoint(path_sl[i], path.back());
  }
  util::CalcTrajTrailerTheta(true, stitch_path.front().trailer_theta(), &path);
}

void ReferenceLineInfo::DpSlPath2XyPath() {
  for (const auto& sl_point : path_data_.dp_sl_path()) {
    path_data_.mutable_dp_norm_path()->emplace_back();
    SlPoint2PathPoint(sl_point, path_data_.mutable_dp_norm_path()->back());
  }
  /* if (!path_data_.dp_norm_path().empty()) { */
  /*   tutil::FillPathS(*path_data_.mutable_dp_norm_path()); */
  /* } */
}

double ReferenceLineInfo::GetSpeedLimitFromS(const double s) const {
  auto info = GetInfoByS(s);
  if (!info) {
    return 0.0;
  }
  return info->speed_limit;
}

bool ReferenceLineInfo::IsLaneChangeLane() const {
  return lane_change_flag_ != tpnc::TurnSignal::NO_TURN;
}

void ReferenceLineInfo::AddRunTime(const double t) {
  run_time_ += t;
  task_run_time_.emplace_back(t);
}

Obstacle* ReferenceLineInfo::AddVirtualStaticObstacle(
    const int obstacle_id, const double obstacle_start_s,
    const double obstacle_end_s) {
  const double middle_s = (obstacle_start_s + obstacle_end_s) / 2.0;
  const auto middle_point =
      tutil::FindInterpPointByDistance(reference_line_, middle_s);
  tutil::VehicleModel virtual_model(
      obstacle_end_s - middle_s, middle_s - obstacle_start_s,
      util::GetLaneWidth(this, obstacle_end_s), 0.0);
  virtual_model.RectangleRep(middle_point);
  virtual_obstacles_.emplace_back(virtual_model.contour());
  auto& virtual_obstacle = virtual_obstacles_.back();
  virtual_obstacle.set_id(obstacle_id);
  virtual_obstacle.set_xy_center(middle_point);
  // TODO: 暂定虚拟障碍物为静态障碍物
  virtual_obstacle.set_is_static(true);
  auto m_obs = AddObstacle(virtual_obstacle);
  if (m_obs != nullptr) {
    m_obs->set_is_virtual(true);
  }
  return m_obs;
}

port::PathSLPoint ReferenceLineInfo::CalculateSlPoint(
    const tport::PathPoint& point) const {
  port::PathSLPoint path_sl_point;
  const auto sl_point = TransformToFrenet(point);
  path_sl_point.set_s(sl_point.s());
  path_sl_point.set_l(sl_point.l());
  const double theta = point.theta();
  const double kappa = point.kappa();
  auto ref_point = EvaluateByS(path_sl_point.s());
  const double theta_ref = ref_point.theta();  // 车体坐标系
  const double kappa_ref = ref_point.kappa();
  const double dkappa_ref = ref_point.dkappa();
  const double dl = tutil::CartesianFrenetConverter::CalculateLateralDerivative(
      theta_ref, theta, path_sl_point.l(), kappa_ref);
  const double ddl =
      tutil::CartesianFrenetConverter::CalculateSecondOrderLateralDerivative(
          theta_ref, theta, kappa_ref, kappa, dkappa_ref, path_sl_point.l());
  path_sl_point.set_dl(dl);
  path_sl_point.set_ddl(ddl);
  return path_sl_point;
}

tport::SLPoint ReferenceLineInfo::TransformToFrenet(
    const tport::Point2D& ref_pt) const {
  if (reference_line_.size() < 2) {
    return tport::SLPoint();
  }
  int match_index =
      tutil::FindNearestWayPointIndex(reference_line_, ref_pt).first;
  int idx1 = match_index;
  int idx2 = match_index;
  if (match_index == 0) {
    ++idx2;
  } else if (match_index + 1 == reference_line_.size()) {
    --idx1;
  } else if (tutil::TwoPointsDistance(reference_line_[match_index - 1],
                                      ref_pt) <=
             tutil::TwoPointsDistance(reference_line_[match_index + 1],
                                      ref_pt)) {
    --idx1;
  } else {
    ++idx2;
  }

  const auto match_pt = tutil::FindProjectPointInPath(
      reference_line_[idx1], reference_line_[idx2], ref_pt);
  double match_dis = tutil::TwoPointsDistance(match_pt, ref_pt);
  // 判断最短距离的正负性
  if (tutil::PointRightofLine(reference_line_[idx1], reference_line_[idx2],
                              ref_pt)) {
    match_dis = -match_dis;
  }
  return {match_pt.s(), match_dis};
}
void ReferenceLineInfo::ResetLastPointer() {
  last_reference_line_info_ = nullptr;
}

}  // namespace port
}  // namespace pnd
}  // namespace trunk
