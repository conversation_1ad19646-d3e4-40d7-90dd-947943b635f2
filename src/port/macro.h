// Copyright 2024, trunk Inc. All rights reserved.

#pragma once

namespace trunk {
namespace pnd {
#define MEMBER_PTR_TYPE(type, var, init)             \
 private:                                            \
  type var##_ = init;                                \
                                                     \
 public:                                             \
  type var() noexcept { return var##_; }             \
  type const var() const noexcept { return var##_; } \
  void set_##var(type var) noexcept { var##_ = var; }

#define MEMBER_CONST_PTR_TYPE(type, var, init)       \
 private:                                            \
  const type var##_ = init;                          \
                                                     \
 public:                                             \
  const type var() const noexcept { return var##_; } \
  void set_##var(const type var) noexcept { var##_ = var; }

#define MEMBER_QUOTE_TYPE(type, var, init)     \
 private:                                      \
  type& var##_ = init;                         \
                                               \
 public:                                       \
  type var() const noexcept { return var##_; } \
  void set_##var(const type var) noexcept { var##_ = var; }

#define MEMBER_ARRAY_TYPE(type, num, var)                                     \
 private:                                                                     \
  std::array<type, num> var##_{};                                             \
                                                                              \
 public:                                                                      \
  const std::array<type, num>& var() const noexcept { return var##_; }        \
  std::array<type, num>& mutable_##var() noexcept { return var##_; }          \
  void set_##var(const std::array<type, num>& var) noexcept { var##_ = var; } \
  void set_##var(const std::array<type, num>&& var) noexcept { var##_ = var; }

#define MEMBER_MATRIX_TYPE(type, rows, cols, var)                        \
 private:                                                                \
  std::array<std::array<type, cols>, rows> var##_{};                     \
                                                                         \
 public:                                                                 \
  const std::array<std::array<type, cols>, rows>& var() const noexcept { \
    return var##_;                                                       \
  }                                                                      \
  std::array<std::array<type, cols>, rows>& mutable_##var() noexcept {   \
    return var##_;                                                       \
  }                                                                      \
  void set_##var(                                                        \
      const std::array<std::array<type, cols>, rows>& var) noexcept {    \
    var##_ = var;                                                        \
  }                                                                      \
  void set_##var(                                                        \
      const std::array<std::array<type, cols>, rows>&& var) noexcept {   \
    var##_ = var;                                                        \
  }
#define PRINT_VARIATE(var) TRUNK_LOG_INFO << #var ": " << var;
}  // namespace pnd
}  // namespace trunk
