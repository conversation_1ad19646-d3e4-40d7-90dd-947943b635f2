// Copyright 2024, trunk Inc. All rights reserved

#include "tsl_trajectory.h"

#include <trunk/common/util/tools/tools.h>

#include "util/util.h"

namespace trunk {
namespace pnd {
namespace port {

TslPoint TslPath::EvaluateByT(const double t) {
  if (empty()) {
    return TslPoint();
  }
  if (size() == 1) {
    return back();
  }
  auto it =
      std::lower_bound(begin(), end(), t,
                       [](const auto& tp, const auto t) { return tp.t() < t; });
  auto it_upper = it;
  auto it_lower = it;
  if (it_upper == end()) {
    --it_upper;
    it_lower = it_upper - 1;
  } else if (it_lower == begin()) {
    it_upper = it_lower + 1;
  } else {
    it_lower = it_upper - 1;
  }
  double weight = (t - it_lower->t()) / (it_upper->t() - it_lower->t());

  return tutil::Lerp(*it_lower, *it_upper, weight);
};

TslPoint TslPath::EvaluateByS(const double s) {
  if (empty()) {
    return TslPoint();
  }
  if (size() == 1) {
    return back();
  }
  auto it =
      std::lower_bound(begin(), end(), s,
                       [](const auto& tp, const auto s) { return tp.t() < s; });
  auto it_upper = it;
  auto it_lower = it;
  if (it_upper == end()) {
    --it_upper;
    it_lower = it_upper - 1;
  } else if (it_lower == begin()) {
    it_upper = it_lower + 1;
  } else {
    it_lower = it_upper - 1;
  }
  double weight = (s - it_lower->s()) / (it_upper->s() - it_lower->s());

  return tutil::Lerp(*it_lower, *it_upper, weight);
}

void TslTrajectory::ToTrajectory(const tport::Path& refs) {
  for (int i = 0; i < refs.size(); ++i) {
    auto p = this->at(i).ToCartesian2D(refs[i]);
    trajectory_.emplace_back(p.x(), p.y(), 0.0);
  }
  for (auto& ref : refs) {
  }
  tutil::FillPathS(trajectory_);
  tutil::CalcPathTheta(&trajectory_);
  tutil::FastCalcPathKappa(trajectory_);
  util::FastCalcPathDkappa(trajectory_);
  for (int i = 0; i < refs.size(); ++i) {
    trajectory_[i].set_v(this->at(i).v());
    trajectory_[i].set_a(this->at(i).a());
    trajectory_[i].set_da(this->at(i).da());
  }
}

}  // namespace port
}  // namespace pnd
}  // namespace trunk
