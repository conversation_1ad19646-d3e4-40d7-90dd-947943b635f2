// Copyright 2023, trunk Inc. All rights reserved

#include "tsl_boundary.h"

#include <trunk/common/common.h>

#include "port/operator.h"

namespace trunk {
namespace pnd {
namespace port {
TslBoundary::TslBoundary(const double t, const SLBoundary& b,
                         const tport::SLPoint& v, const tport::SLPoint& a)
    : t_(t), SLBoundary(b), v_(v), a_(a) {}
TslBoundary::TslBoundary(const double t, const double min_s, const double max_s,
                         const double min_l, const double max_l)
    : t_(t), SLBoundary(min_s, max_s, min_l, max_l) {}

TslBoundary& TslBoundary::operator+=(const TslBoundary& other) {
  this->t_ += other.t_;
  this->v_ += other.v_;
  this->a_ += other.a_;
  SLBoundary::operator+=(other);
  return *this;
}
TslBoundary& TslBoundary::operator-=(const TslBoundary& other) {
  this->t_ -= other.t_;
  this->v_ -= other.v_;
  this->a_ -= other.a_;
  SLBoundary::operator-=(other);
  return *this;
}
TslBoundary& TslBoundary::operator*=(const double ratio) {
  this->t_ *= ratio;
  this->v_ *= ratio;
  this->a_ *= ratio;
  SLBoundary::operator*=(ratio);
  return *this;
}

bool TslBoundarys::EvaluateByT(const double t, TslBoundary& b) const {
  if (this->empty()) {
    return false;
  }
  if (this->size() == 1) {
    b = this->front();
    return true;
  }

  auto it = std::lower_bound(
      this->begin(), this->end(), t,
      [](const auto& boundary, const double t) { return boundary.t() < t; });

  auto it_upper = it;
  auto it_lower = it;
  if (it_upper == this->end()) {
    --it_upper;
    it_lower = it_upper - 1;
  } else if (it_lower == this->begin()) {
    it_upper = it_lower + 1;
  } else {
    it_lower = it_upper - 1;
  }
  double weight = (t - it_lower->t()) / (it_upper->t() - it_lower->t());
  b = tutil::Lerp(*it_lower, *it_upper, weight);
  return true;
}
TslBoundary TslBoundarys::EvaluateByT(const double t) const {
  TslBoundary b;
  if (!EvaluateByT(t, b)) {
  }
  return b;
}

}  // namespace port
}  // namespace pnd
}  // namespace trunk
