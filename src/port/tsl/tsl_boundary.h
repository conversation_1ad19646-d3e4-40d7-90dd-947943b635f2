// Copyright 2024, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/config.h>

#include "../sl/sl_boundary.h"
#include "trunk/common/port/base_point.h"

namespace trunk {
namespace pnd {
namespace port {

/////////////////////////////////////////////////////////////////
// The min_s and max_s are longitudinal values.
// min_s <= max_s.
//
//              max_s
//                ^
//                |
//          S  direction
//                |
//            min_s
//
// The min_l and max_l are lateral values.
// min_l <= max_l. Left side of the reference line is positive,
// and right side of the reference line is negative.
//  max_l  <-----L direction---- min_l
/////////////////////////////////////////////////////////////////

class TslBoundary : public SLBoundary {
 public:
  TslBoundary(const double t, const SLBoundary& b,
              const tport::SLPoint& v = tport::SLPoint(),
              const tport::SLPoint& a = tport::SLPoint());
  TslBoundary(const double t, const double min_s, const double max_s,
              const double min_l, const double max_l);
  TslBoundary() = default;

  TslBoundary& operator+=(const TslBoundary& other);
  TslBoundary& operator-=(const TslBoundary& other);
  TslBoundary& operator*=(const double ratio);

 private:
  MEMBER_BASIC_TYPE(double, t, 0.0);
  MEMBER_COMPLEX_TYPE(tport::SLPoint, v);
  MEMBER_COMPLEX_TYPE(tport::SLPoint, a);
};

class TslBoundarys : public std::vector<TslBoundary> {
 public:
  bool EvaluateByT(const double t, TslBoundary& b) const;
  TslBoundary EvaluateByT(const double t) const;
};

}  // namespace port
}  // namespace pnd
}  // namespace trunk
