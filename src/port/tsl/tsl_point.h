// Copyright 2024, trunk Inc. All rights reserved
#pragma once

#include <trunk/common/common.h>
#include <trunk/common/port/plan_point.h>

#include "../sl/path_point.h"
#include "../st/speed_point.h"

namespace trunk {
namespace pnd {
namespace port {

class TslPoint : public tport::SLPoint {
 public:
  TslPoint() = default;
  TslPoint(double t, double s, double l);
  TslPoint(double t, const tport::SLPoint& sl);

  TslPoint& operator+=(const TslPoint& p);
  TslPoint& operator-=(const TslPoint& p);
  TslPoint& operator*=(const double r);
  tport::Point2D ToCartesian2D(const tport::PathPoint& ref_p) const;

 private:
  MEMBER_BASIC_TYPE(double, t, 0.0);
};

class TslTrajectoryPoint : public TslPoint {
 public:
  TslTrajectoryPoint() = default;
  TslTrajectoryPoint(double t, double s, double v, double a, double j, double l,
                     double lv, double la, double lj);
  TslTrajectoryPoint(const double t, const tport::TrajectoryPoint& xy_p,
                     const PathSLPoint& sl_p);
  TslTrajectoryPoint(const double t, const SpeedSTPoint& st_p,
                     const PathSLPoint& sl_p);

  TslTrajectoryPoint& operator+=(const TslTrajectoryPoint& p);
  TslTrajectoryPoint& operator-=(const TslTrajectoryPoint& p);
  TslTrajectoryPoint& operator*=(const double r);

 private:
  MEMBER_BASIC_TYPE(double, v, 0.0);
  MEMBER_BASIC_TYPE(double, a, 0.0);
  MEMBER_BASIC_TYPE(double, da, 0.0);
  MEMBER_BASIC_TYPE(double, dl, 0.0);
  MEMBER_BASIC_TYPE(double, ddl, 0.0);
  MEMBER_BASIC_TYPE(double, dddl, 0.0);
};

}  // namespace port
}  // namespace pnd
}  // namespace trunk
