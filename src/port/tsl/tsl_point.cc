// Copyright 2024, trunk Inc. All rights reserved

#include "tsl_point.h"

#include "trunk/common/port/base_point.h"
#include "trunk/common/port/plan_point.h"

namespace trunk {
namespace pnd {
namespace port {
TslPoint::TslPoint(double t, double s, double l)
    : t_(t), tport::SLPoint(s, l) {}
TslPoint::TslPoint(double t, const tport::SLPoint& sl)
    : t_(t), tport::SLPoint(sl) {}

TslPoint& TslPoint::operator+=(const TslPoint& p) {
  tport::SLPoint::operator+=(p);
  t_ += p.t_;
  return *this;
}

TslPoint& TslPoint::operator-=(const TslPoint& p) {
  tport::SLPoint::operator-=(p);
  t_ -= p.t_;
  return *this;
}

TslPoint& TslPoint::operator*=(const double r) {
  tport::SLPoint::operator*=(r);
  t_ *= r;
  return *this;
}
tport::Point2D TslPoint::ToCartesian2D(const tport::PathPoint& ref_p) const {
  return {ref_p.x() - l() * std::sin(ref_p.theta()),
          ref_p.y() + l() * std::cos(ref_p.theta())};
}

TslTrajectoryPoint::TslTrajectoryPoint(double t, double s, double v, double a,
                                       double j, double l, double lv, double la,
                                       double lj)
    : TslPoint(t, s, l), v_(v), a_(a), da_(j), dl_(lv), ddl_(la), dddl_(lj) {}

TslTrajectoryPoint::TslTrajectoryPoint(const double t,
                                       const tport::TrajectoryPoint& xy_p,
                                       const PathSLPoint& sl_p)
    : TslPoint(t, sl_p),
      v_(xy_p.v()),
      a_(xy_p.a()),
      dl_(sl_p.dl()),
      ddl_(sl_p.ddl()) {}

TslTrajectoryPoint& TslTrajectoryPoint::operator+=(
    const TslTrajectoryPoint& p) {
  TslPoint::operator+=(p);
  v_ += p.v_;
  a_ += p.a_;
  da_ += p.da_;
  dl_ += p.dl_;
  ddl_ += p.ddl_;
  dddl_ += p.dddl_;
  return *this;
}

TslTrajectoryPoint& TslTrajectoryPoint::operator-=(
    const TslTrajectoryPoint& p) {
  TslPoint::operator-=(p);
  v_ -= p.v_;
  a_ -= p.a_;
  da_ -= p.da_;
  dl_ -= p.dl_;
  ddl_ -= p.ddl_;
  dddl_ -= p.dddl_;
  return *this;
}

TslTrajectoryPoint& TslTrajectoryPoint::operator*=(const double r) {
  TslPoint::operator*=(r);
  v_ *= r;
  a_ *= r;
  da_ *= r;
  dl_ *= r;
  ddl_ *= r;
  dddl_ *= r;
  return *this;
}

}  // namespace port
}  // namespace pnd
}  // namespace trunk
