// Copyright 2024, trunk Inc. All rights reserved
#pragma once

#include <trunk/common/config.h>

#include <vector>

#include "trunk/common/port/plan_points.h"
#include "tsl_point.h"

namespace trunk {
namespace pnd {
namespace port {
class TslPath : public std::vector<TslPoint> {
 public:
  TslPath() = default;
  TslPoint EvaluateByT(const double t);
  TslPoint EvaluateByS(const double s);
};

class TslTrajectory : public std::vector<TslTrajectoryPoint> {
 public:
  TslTrajectory() = default;
  void ToTrajectory(const tport::Path& refs);

 private:
  MEMBER_BASIC_TYPE(bool, invalid, false);
  MEMBER_BASIC_TYPE(double, cost_lat_terminal, 0.0);
  MEMBER_BASIC_TYPE(double, cost_lat_velocity, 0.0);
  MEMBER_BASIC_TYPE(double, cost_lat_acceleration, 0.0);
  MEMBER_BASIC_TYPE(double, cost_lat_jerk, 0.0);
  MEMBER_BASIC_TYPE(double, cost_lon_velocity, 0.0);
  MEMBER_BASIC_TYPE(double, cost_lon_acceleration, 0.0);
  MEMBER_BASIC_TYPE(double, cost_lon_jerk, 0.0);
  MEMBER_BASIC_TYPE(double, cost_obstacle_front_distance, 0.0);
  MEMBER_BASIC_TYPE(double, cost_obstacle_back_distance, 0.0);
  MEMBER_BASIC_TYPE(double, cost_obstacle_lateral_distance, 0.0);
  MEMBER_BASIC_TYPE(double, cost_obstacle_oblique_distance, 0.0);
  MEMBER_BASIC_TYPE(double, cost_obstacle_follow, 0.0);
  MEMBER_BASIC_TYPE(double, cost_obstacle_nudge, 0.0);

  MEMBER_BASIC_TYPE(double, lon_cost, 0.0);
  MEMBER_BASIC_TYPE(double, lat_cost, 0.0);
  MEMBER_BASIC_TYPE(double, obstacle_cost, 0.0);
  MEMBER_BASIC_TYPE(double, nudge_cost, 0.0);
  MEMBER_BASIC_TYPE(double, follow_cost, 0.0);

  MEMBER_BASIC_TYPE(double, total_cost, 0.0);

  MEMBER_COMPLEX_TYPE(tport::Trajectory, trajectory)
};

using TslTrajectorys = std::vector<TslTrajectory>;
}  // namespace port
}  // namespace pnd
}  // namespace trunk
