// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/common.h>

namespace trunk {
namespace pnd {
namespace port {

enum class TurnType {
  KEEP = 0,
  INLANE_TO_RAMP = 1,  // 高速行驶车道向右进入匝道（沿着参考线）
  RAMP_TO_INLANE = 2,  // 匝道向左进入高速行驶车道（沿着参考线）
  INLANE_RIGHT_CONFLUENCE = 3,  // 高速行驶车道向右合流（平行变道）
  INLANE_LEFT_CONFLUENCE = 4,  // 高速行驶车道向左合流（平行变道）
  RAMP_RIGHT_TO_RAMP = 5,      // 匝道上向右进入匝道 （沿参考线）
  RAMP_LEFT_TO_RAMP = 6,  // 匝道上向左进入匝道 （沿着参考线）
  RAMP_RIGHT_CONFLUENCE = 7,  // 匝道上向右合流 （平行变道）
  RAMP_LEFT_CONFLUENCE = 8,   // 匝道上向左合流 （平行变道）
  KEEP_TO_TARGET = 12,        // 保持当前道路继续行驶到重点
  INVALID = 15                // 无效值
};
enum class BoundaryType {
  UNKNOWN = 0,     // 未知
  SOLID = 1,       // 实线
  DASHED = 2,      // 虚线
  CURB = 3,        // 路边
  DOUBLELINE = 4,  // 双线
  BOTTSDOTS = 5,   // 波茨点
  VIRTUAL = 6,     // 虚拟线
};
enum class BoundaryColor {
  UNKNOWN = 0,  // 未知
  WHITE = 1,    // 白线
  YELLOW = 2    // 黄线
};
enum class HighwayRegionType {
  UNKNOWN = 0,
  INLANE = 1,
  EMERGENCY = 2,
  RAMP_TO_TOLL = 3,
  TOLL_TO_RAMP = 4,
  RAMP_TO_SERVICE = 5,
  SERVICE_TO_RAMP = 6,
  RAMP_TO_RAMP = 7,
  SERVICE = 8,
  BRANCH = 9,
  TOLL = 11,
  TUNNEL = 12,
  UNDER_BRIDGE = 13,
  INVALID = 15,
  JUNCTION = 16
};
enum class PortRegionType {
  DRIVING = 0,
  SEASIDE = 1,  // 岸桥
  YARD = 2,
  CROSS = 3,
  CONNECTION = 4,
  CONNECTION_STRAIGHT = 41,
  CONNECTION_DETOUR_U = 42,
  CONNECTION_DETOUR_Z = 43,
  LOCKZONE = 5,
  HATCHCOVER = 6,
  LATITUDE = 7,   // 和岸桥平行为纬度
  LONGITUDE = 8,  // 和引桥平行为经度
  EMERGENCY = 10,
  SEASIDE_BARRIER = 12,
  YARD_CHANGE = 21,
  YARD_WORK = 22,
  LOCKZONE_CONNECTION = 51,
  CHARGE = 9,
  X_RAY = 13,
  PB_ZONE = 14,
  PARKING = 15,
  BRIDGE = 16,
  YARD_CROSS = 31
};

}  // namespace port
}  // namespace pnd
}  // namespace trunk
