// Copyright 2023, trunk Inc. All rights reserved

#include "frame.h"

#include "log.h"
#include "port/reference_line_info.h"
#include "port/st/speed_data.h"
#include "trunk/common/port/base_point.h"
#include "trunk/common/util/coordinate/transformfrenet.h"
#include "util/key.h"

namespace trunk {
namespace pnd {
namespace port {

bool Frame::Init() {
  do {
    if (env_.vehicle().driver_status()) {
      break;
    }
    if (util::last_frame_ == nullptr) {
      TRUNK_LOG_WARN << "history frame is nullptr";
      break;
    }
    last_drived_reference_line_info_ =
        util::last_frame_->drive_reference_line_info();
    if (last_drived_reference_line_info_ == nullptr) {
      TRUNK_LOG_WARN << "history drive_reference_line_info is empty";
      break;
    }
    const auto& trajectory = last_drived_reference_line_info_->trajectory();
    if (trajectory.empty()) {
      TRUNK_LOG_WARN << "history trajectory is empty";
      break;
    }
    const auto match_point = tutil::GetClosestMatchPoint(trajectory);
    if (std::fabs(match_point.y()) > util::sys_config_.replan_lat_err_thres()) {
      TRUNK_LOG_INFO << "lat error over thres, replan";
      break;
    }
    if ((trajectory.size() == 1 &&
         match_point.x() < -util::sys_config_.replan_lon_err_thres()) ||
        trajectory.front().x() > util::sys_config_.replan_lon_err_thres()) {
      TRUNK_LOG_INFO << "lon error over thres, replan";
      break;
    }
    const double stitch_length =
        util::sys_config_.stitch_time() * env_.vehicle().velocity();
    const double path_stitch_length =
        util::sys_config_.path_stitch_time() * env_.vehicle().velocity();
    const double ref_delay_length = util::sys_config_.ref_delay_dis();
    const int init_index = tutil::FindPointIndexByDistance(
        trajectory, stitch_length + match_point.s());
    const int path_init_index = std::max(
        init_index, tutil::FindPointIndexByDistance(
                        trajectory, path_stitch_length + match_point.s()));
    const int target_index = tutil::FindPointIndexByDistance(
        trajectory, ref_delay_length + match_point.s());

    // 普通情况下的stitch轨迹
    stitch_trajectory_.insert(stitch_trajectory_.begin(), trajectory.begin(),
                              trajectory.begin() + init_index + 1);
    // 变道时的stitch轨迹
    path_stitch_trajectory_.insert(path_stitch_trajectory_.begin(),
                                   trajectory.begin() + init_index,
                                   trajectory.begin() + path_init_index + 1);
    // 用来确定目标车道的辅助点
    target_point_ = trajectory[target_index];

    const auto init_speed =
        last_drived_reference_line_info_->speed_data().EvaluateByTime(
            util::sys_config_.stitch_time());
    stitch_trajectory_.back().set_v(init_speed.v());
    stitch_trajectory_.back().set_a(init_speed.a());
    // // 车辆静止
    // if (std::abs(env_.vehicle().velocity()) < tport::kMathEpsilon &&
    //     std::abs(env_.vehicle().acceleration()) < tport::kMathEpsilon) {
    //   // 规划起步速度为0，未来在加速，给起步速度
    //   if (init_point().a() < 0.0) {
    //     stitch_trajectory_.back().set_v(0.0);
    //     stitch_trajectory_.back().set_a(0.0);
    //   } else if (trajectory.back().v() > 2.0) {
    //     if (init_point().v() < tport::kMathEpsilon) {
    //       stitch_trajectory_.back().set_a(0.4);
    //     }
    //     stitch_trajectory_.back().set_v(std::max(1.0, init_point().v()));
    //     // 规划未来刹停，直接刹停
    //   }
    // }
  } while (false);

  if (stitch_trajectory_.empty()) {
    tport::TrajectoryPoint init_pt;
    init_pt.set_v(env().vehicle().velocity());
    init_pt.set_kappa(std::tan(tutil::Deg2Rad(env_.vehicle().steering_angle() /
                                              util::model_.steering_ratio())) /
                      util::truck_.head_wheelbase());
    stitch_trajectory_.emplace_back(init_pt);
    path_stitch_trajectory_.emplace_back(init_point());
    target_point_ = init_pt;
    replan_flag_ = true;
  }
  const double time_errro =
      init_point().relative_time() - util::sys_config_.stitch_time();
  for (auto& p : stitch_trajectory_) {
    p.set_relative_time(p.relative_time() - time_errro);
  }

  for (auto it = reference_lines_info_.begin();
       it != reference_lines_info_.end();) {
    if (!it->Init(env_.obstacles(), env_.vehicle(), init_point(),
                  path_init_point(), target_point_)) {
      it = reference_lines_info_.erase(it);
    } else {
      ++it;
    }
  }
  if (reference_lines_info_.size() == 0) {
    TRUNK_LOG_ERROR << "reference_lines_info.size == 0";
    return false;
  }
  // set obstacle.long_nudge
  std::unordered_set<int> obstacle_nudge;
  for (auto& reference_line_info : reference_lines_info_) {
    for (auto& obstacle : reference_line_info.obstacles().Items()) {
      auto& sl_boundary = obstacle->sl_boundary();
      auto& reference_line = reference_line_info.reference_line();
      auto& info = *reference_line_info.GetInfoByS(sl_boundary.max_s());
      if ((info.IsProhibitedLane() || obstacle->obj().is_static() ||
           obstacle->IsStaticType()) &&
          sl_boundary.min_s() <= reference_line.back().s() &&
          reference_line.front().s() <= sl_boundary.max_s() &&
          (sl_boundary.max_l() * sl_boundary.min_l() <= 0.0 ||
           std::min(std::abs(sl_boundary.max_l()),
                    std::abs(sl_boundary.min_l())) <=
               util::kHighwayEmergencyLaneWidth * 0.3)) {
        obstacle_nudge.insert(obstacle->obj().id());
      }
    }
  }
  for (auto& reference_line_info : reference_lines_info_) {
    for (auto& id : obstacle_nudge) {
      reference_line_info.mutable_obstacles()->Find(id)->set_dangerous_nudge(
          true);
    }
  }

  RebuildInternalLinks();

  double min_dis = std::numeric_limits<double>::max();
  for (auto& reference_line_info : reference_lines_info_) {
    auto* front_range =
        GetRangeById(reference_line_info.infos().front().lane_id);
    if (front_range == nullptr) {
      continue;
    }
    if (reference_line_info.self_sl().s() <
        reference_line_info.reference_line().front().s()) {
      continue;
    }
    const double l = tutil::TransformFrenet::TransformToFrenet(
                         reference_line_info.reference_line(),
                         tport::Point2D(util::truck_.base2front(), 0.0))
                         .l();
    if (min_dis > std::abs(l)) {
      current_reference_line_info_ = &reference_line_info;
      min_dis = std::abs(l);
    }
  }

  if (current_reference_line_info_ != nullptr) {
    current_reference_line_info_->set_policy("lane_follow");
  }

  if (!SetTargetLane()) {
    return false;
  }

  // 所有横纵向相关指针必须在删除操作之后，为了建立方便，在rebuild之后赋值
  if (last_drived_reference_line_info_) {
    for (auto& info : last_drived_reference_line_info_->infos()) {
      last_drived_reference_line_info_mapping_ =
          GetReferenceLineById(info.lane_id);
      if (last_drived_reference_line_info_mapping_) {
        break;
      }
    }
  }

  auto sl_boundary_splicing = [](const auto& reference_line,
                                 auto& origin_sl_boundary,
                                 const auto& boundary) {
    std::vector<tport::SLPoint> sl_boundary;
    for (auto& p : boundary) {
      auto sl = tutil::TransformFrenet::TransformToFrenet(reference_line, p);
      if (sl.s() < origin_sl_boundary.front().s()) {
        sl_boundary.push_back(sl);
      } else {
        break;
      }
    }
    origin_sl_boundary.insert(origin_sl_boundary.begin(), sl_boundary.begin(),
                              sl_boundary.end());
  };
  assert(current_reference_line_info_ != nullptr);
  for (auto& reference_line_info : reference_lines_info_) {
    if (reference_line_info.reference_line().front().s() > 1.0) {
      sl_boundary_splicing(
          reference_line_info.reference_line(),
          reference_line_info.mutable_sl_ego_road_boundaries()->first,
          current_reference_line_info_->ego_road_boundaries().first);
      sl_boundary_splicing(
          reference_line_info.reference_line(),
          reference_line_info.mutable_sl_ego_road_boundaries()->second,
          current_reference_line_info_->ego_road_boundaries().second);
      sl_boundary_splicing(
          reference_line_info.reference_line(),
          reference_line_info.mutable_sl_ego_drivable_boundaries()->first,
          current_reference_line_info_->ego_drivable_boundaries().first);
      sl_boundary_splicing(
          reference_line_info.reference_line(),
          reference_line_info.mutable_sl_ego_drivable_boundaries()->second,
          current_reference_line_info_->ego_drivable_boundaries().second);
    }
  }
  return true;
}
bool Frame::SetTargetLane() {
  // 需要判断target_point_ 未初始化？
  if (ranges_.empty()) {
    return false;
  }
  for (auto& range : ranges_) {
    range.Init(env_.vehicle());
    if (!range.is_static_map() && range.InRange(target_point_)) {
      target_lane_id_ = range.target_lane_id();
      TRUNK_LOG_INFO << "map_lane_id:" << target_lane_id_;
      break;
    }
  }
  if (target_lane_id_ == "") {
    TRUNK_LOG_ERROR << "have no target_lane_id";
    return false;
  }

  target_range_ = &ranges_[lane_id2range_idx_.at(target_lane_id_)];
  assert(target_range_ != nullptr);

  for (auto& ii : target_range_->lane_id2line_idx()) {
    reference_lines_info_[ii.second].set_target_info(GetInfoById(ii.first));
  }

  if (current_reference_line_info_ != nullptr &&
      current_reference_line_info_->target_info() != nullptr) {
    const auto cur_info = current_reference_line_info_->target_info();
    if (!cur_info->is_target) {
      // 当前车道不是目标车道
      // 目标车道的判断
      target_lane_id_ = cur_info->lane_id < target_lane_id_
                            ? cur_info->r_lane_id
                            : cur_info->l_lane_id;
      // （jira VTI-10083）
      // TODO:待添加其他的case
      int i = 0;
      for (; i < current_reference_line_info_->infos().size(); ++i) {
        if (&current_reference_line_info_->infos().at(i) == cur_info) {
          break;
        }
      }
      for (; i < current_reference_line_info_->infos().size(); ++i) {
        if (current_reference_line_info_->infos().at(i).is_target) {
          target_lane_id_ = cur_info->lane_id;
          break;
        }
      }
    }
  }

  target_reference_line_info_ =
      &reference_lines_info_[target_range_->lane_id2line_idx().at(
          target_lane_id_)];
  assert(target_reference_line_info_ != nullptr);

  return true;
}

bool Frame::RebuildInternalLinks() {
  auto copy_id2range_idx = lane_id2range_idx_;
  std::unordered_set<int> range_idx_set;
  Id2Idx id2line_idx;
  lane_id2range_idx_.clear();
  for (int i = 0; i < reference_lines_info_.size(); ++i) {
    reference_lines_info_[i].mutable_lane_id2info_idx()->clear();
    for (int j = 0; j < reference_lines_info_[i].infos().size(); ++j) {
      auto& info = reference_lines_info_[i].infos()[j];
      id2line_idx[info.lane_id] = i;
      reference_lines_info_[i].mutable_lane_id2info_idx()->emplace(info.lane_id,
                                                                   j);
      lane_id2range_idx_.emplace(info.lane_id, -1);
      range_idx_set.insert(copy_id2range_idx[info.lane_id]);
    }
  }
  for (auto& reference_line_info : reference_lines_info_) {
    for (auto& info : *reference_line_info.mutable_infos()) {
      if (!id2line_idx.count(info.r_lane_id)) {
        info.r_lane_id = "";
      }
      if (!id2line_idx.count(info.l_lane_id)) {
        info.l_lane_id = "";
      }
    }
  }
  for (int i = ranges_.size() - 1; i >= 0; --i) {
    if (!range_idx_set.count(i)) {
      ranges_.erase(ranges_.begin() + i);
    }
  }
  for (int i = ranges_.size() - 1; i >= 0; --i) {
    auto copy_id2line_idx = ranges_[i].lane_id2line_idx();
    ranges_[i].mutable_lane_id2line_idx()->clear();
    for (auto& ii : copy_id2line_idx) {
      auto it = lane_id2range_idx_.find(ii.first);
      if (it != lane_id2range_idx_.end()) {
        lane_id2range_idx_[ii.first] = i;
        ranges_[i].mutable_lane_id2line_idx()->emplace(ii.first,
                                                       id2line_idx[ii.first]);
      }
    }
  }

  return true;
}

Frame::Range* Frame::GetRangeById(const std::string& id) {
  auto it = lane_id2range_idx().find(id);
  if (lane_id2range_idx().end() == it) {
    return nullptr;
  }
  return &ranges_[it->second];
}
ReferenceLineInfo* Frame::GetReferenceLineById(const std::string& id) {
  auto range = GetRangeById(id);
  if (range == nullptr) {
    return nullptr;
  }
  auto it = range->lane_id2line_idx().find(id);
  if (it == range->lane_id2line_idx().end()) {
    return nullptr;
  }
  return &reference_lines_info_[it->second];
}
ReferenceLineInfo::Info* Frame::GetInfoById(const std::string& id) {
  auto reference_line_info = GetReferenceLineById(id);
  if (reference_line_info == nullptr) {
    return nullptr;
  }
  auto it = reference_line_info->lane_id2info_idx().find(id);
  if (it == reference_line_info->lane_id2info_idx().end()) {
    return nullptr;
  }
  return &reference_line_info->mutable_infos()->at(it->second);
}

const ReferenceLineInfo* Frame::FindDriveReferenceLineInfo() {
  double min_cost = std::numeric_limits<double>::infinity();
  drive_reference_line_info_ = nullptr;
  int line_idx = 0;
  for (auto& reference_line_info : reference_lines_info_) {
    TRUNK_LOG_INFO << "lane_id: " << reference_line_info.id()
                   << " cost:" << reference_line_info.total_cost();
    if (reference_line_info.is_drivable().first) {
      ++nums_of_trajectories_;
      if (reference_line_info.total_cost() < min_cost) {
        drive_reference_line_info_ = &reference_line_info;
        min_cost = reference_line_info.total_cost();
        drive_trajectory_idx_ = line_idx;
      }
    } else {
      TRUNK_LOG_INFO << "lane_id: " << reference_line_info.id()
                     << " is not drivable, reason is: "
                     << reference_line_info.is_drivable().second;
    }
    line_idx++;
  }
  if (drive_reference_line_info_) {
    TRUNK_LOG_INFO << "selected reference_line_info: "
                   << drive_reference_line_info_->id();
  }

  if (util::last_frame_ != nullptr && drive_reference_line_info_) {
    // 如果这一帧变道，则变道
    if (last_drived_reference_line_info_) {
      // 延续上一帧的超车状态
      drive_reference_line_info_->set_overtake_flag(
          last_drived_reference_line_info_->overtake_flag());
      if (drive_reference_line_info_->overtake_flag()) {
        // 在目标车道且变道完成，则取消超车过程
        if (drive_reference_line_info_ == target_reference_line_info_ &&
            drive_reference_line_info_->lane_change_flag() ==
                tpnc::TurnSignal::NO_TURN &&
            util::last_frame_->turn_signal_ == tpnc::TurnSignal::NO_TURN) {
          drive_reference_line_info_->set_overtake_flag(false);
        }
      } else {
        // 不在目标车道且变道过程中，则超车
        if (drive_reference_line_info_ != target_reference_line_info_ &&
            drive_reference_line_info_->lane_change_flag() !=
                tpnc::TurnSignal::NO_TURN) {
          drive_reference_line_info_->set_overtake_flag(true);
        }
      }
    }

    // turn_flag
    do {
      const int idx =
          tutil::FindNearestWayPointIndex(
              drive_reference_line_info_->reference_line(), tport::Point2D())
              .first;
      const double lat =
          tutil::TransformFrenet::TransformToFrenet(
              drive_reference_line_info_->reference_line(), tport::Point2D())
              .l();
      // 变道信号边沿触发
      if (drive_reference_line_info_->lane_change_flag() !=
          tpnc::TurnSignal::NO_TURN) {
        if (idx == 0 || std::fabs(lat) > util::kHighwayLaneHalfWidth) {
          turn_flag_ = drive_reference_line_info_->lane_change_flag();
          break;
        }
      }
      // 上一帧在变道
      if (util::last_frame_->turn_flag_ != tpnc::TurnSignal::NO_TURN) {
        // 如果上一帧变道，计算在目标车道的横向距离，距离较大时变道
        if (idx == 0 ||
            std::fabs(lat) > util::sys_config_.turn_signal_lat_thres()) {
          turn_flag_ = util::last_frame_->turn_flag_;
          break;
        }
      }
      turn_flag_ = tpnc::TurnSignal::NO_TURN;
    } while (false);

    // turn_signal信号
    do {
      // 汇入主路或者进入辅路时，优先级最高（内部改了上一帧的转向状态，直接延续）
      if (target_reference_line_info_ && current_reference_line_info_ &&
          current_reference_line_info_->target_info() &&
          (target_reference_line_info_->target_info()->IsAuxiliaryLane() ^
           current_reference_line_info_->target_info()->IsAuxiliaryLane())) {
        if (target_reference_line_info_->target_info()->IsAuxiliaryLane()) {
          turn_signal_ = tpnc::TurnSignal::RIGHT_TURN;
        } else {
          turn_signal_ = tpnc::TurnSignal::LEFT_TURN;
        }
        break;
      }
      turn_signal_ = turn_flag_;
    } while (false);
  }
  return drive_reference_line_info_;
}

const tport::TrajectoryPoint& Frame::path_init_point() const {
  return path_stitch_trajectory_.back();
}

const tport::TrajectoryPoint& Frame::init_point() const {
  return stitch_trajectory_.back();
}

bool Frame::UseLastFrameTrajectory() {
  if (util::last_frame_ == nullptr) {
    TRUNK_LOG_ERROR << "history frame is nullptr";
    return false;
  }
  auto last_drive = util::last_frame_->drive_reference_line_info();
  if (last_drive == nullptr) {
    TRUNK_LOG_ERROR << "last_drive_reference_line_info is nullptr";
    return false;
  }
  auto last_trajectory = last_drive->mutable_trajectory();
  if (last_trajectory->empty()) {
    TRUNK_LOG_ERROR << "last frame trajectory empty";
    return false;
  }
  const auto self_sl = tutil::TransformFrenet::TransformToFrenet(
      *last_trajectory, tport::Point2D());
  if (self_sl.s() > last_trajectory->back().s() ||
      self_sl.s() < last_trajectory->front().s() ||
      std::abs(self_sl.l()) > util::sys_config_.replan_lat_err_thres()) {
    TRUNK_LOG_ERROR << "Too far from the previous frame trajectory: "
                    << self_sl;
    return false;
  }
  // 转化到车体坐标系的工作在别的地方做过了
  // tutil::TransformUtm::Utm2Vehicle(this->env().vehicle(),
  // *last_trajectory);
  int line_idx = 0;
  for (auto& line : reference_lines_info_) {
    if (last_drived_reference_line_info_ == line.last_reference_line_info()) {
      *line.mutable_trajectory() = *last_trajectory;
      *line.mutable_speed_data() = last_drive->speed_data();
      drive_reference_line_info_ = &line;
      line.set_is_drivable({true, "last_frame"});
      line.set_planning_failure_cause("history - " +
                                      line.planning_failure_cause());
      drive_trajectory_idx_ = line_idx;
    }
    line_idx++;
  }
  FindDriveReferenceLineInfo();
  return true;
}

void Frame::ResetLastPointer() {
  last_drived_reference_line_info_ = nullptr;
  for (auto& reference_line_info : reference_lines_info_) {
    reference_line_info.ResetLastPointer();
  }
}

}  // namespace port
}  // namespace pnd
}  // namespace trunk
