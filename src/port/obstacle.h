// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/common.h>

#include "../util/indexed_list.h"
#include "sl/sl_boundary.h"
#include "st/st_boundary.h"
#include "tsl/tsl_boundary.h"

namespace trunk {
namespace pnd {
namespace port {

enum class ObstacleDecision {
  kNotSet = 0,
  kIgnore = 1,
  // long
  kStop = 2,
  kFollow = 3,
  kYield = 4,
  kOvertake = 5,
  kKeepClear = 8,
  // lat
  kLeftNudge = 6,
  kRightNudge = 7,
};

enum class StopReason {
  kNoStop = 0,
  kObstacle = 1,
  kDestination = 2,
  kRedLight = 3,
};

enum class ObjectType {
  UNKNOWN = 0,
  CAR = 1,
  MOTORCYCLE = 2,
  TRUCK = 3,
  PEDESTRIAN = 4,
  BUS = 5,
  TRAFFIC_CONE = 6,  // 锥桶
  ANIMAL = 7,
  GENERAL = 8,
  BICYCLE = 9,
  UNCERTAIN_VCL = 10,
  CONSTRUCTION_AREA = 100
};

class Obstacle {
 public:
  Obstacle(const tport::PredictionObstacle& obs) : obj_(obs) {}
  Obstacle(const Obstacle& obs) : obj_(obs.obj()) {}

  void GetContourBasedOnPredictionPosture(
      const tport::TrajectoryPoint& prediction_postrue,
      tport::Contour2D& prediction_contour) const;

  void GetSlBoundaryBasedOnPredictionPosture(
      const tport::Path& ref_line,
      const tport::TrajectoryPoint& prediction_postrue,
      SLBoundary& prediction_sl_boundary) const;

  void AddLongitudinalDecision(const std::string& decider_tag,
                               ObstacleDecision decision);

  void AddLateralDecision(const std::string& decider_tag,
                          ObstacleDecision decision);

  const ObstacleDecision lat_decision() const { return lat_decision_; }

  const ObstacleDecision lon_decision() const { return lon_decision_; }

  const std::vector<std::string>& decider_tags() const { return decider_tags_; }

  static std::string TypeName(const ObstacleDecision& type);

  const tport::PredictionObstacle& obj() const { return obj_; };

  bool IsStaticType() {
    auto type = static_cast<ObjectType>(obj_.type());
    return type == ObjectType::TRAFFIC_CONE ||
           type == ObjectType::CONSTRUCTION_AREA;
  }

  bool HasNudge() {
    return lat_decision_ == ObstacleDecision::kLeftNudge ||
           lat_decision_ == ObstacleDecision::kRightNudge;
  }

 private:
  std::vector<std::string> decider_tags_;
  ObstacleDecision lat_decision_ = ObstacleDecision::kNotSet;
  ObstacleDecision lon_decision_ = ObstacleDecision::kNotSet;
  const tport::PredictionObstacle& obj_;

  // get set members
  MEMBER_COMPLEX_TYPE(SLBoundary, sl_boundary);
  MEMBER_COMPLEX_TYPE(STBoundary, st_boundary);
  MEMBER_COMPLEX_TYPE(TslBoundarys, tsl_boundarys);
  MEMBER_BASIC_TYPE(bool, is_blocking, false);
  MEMBER_BASIC_TYPE(bool, is_virtual, false);
  MEMBER_BASIC_TYPE(bool, dangerous_nudge, false);
  MEMBER_BASIC_TYPE(StopReason, stop_reason, StopReason::kNoStop);
};

using IndexedObstacles = util::IndexedList<int, Obstacle>;

}  // namespace port
}  // namespace pnd
}  // namespace trunk
