// Copyright 2023, trunk Inc. All rights reserved

#include "st_graph_point.h"

namespace trunk {
namespace pnd {

const StGraphPoint *StGraphPoint::pre_point() const { return pre_point_; }

void StGraphPoint::Init(const std::uint32_t index_t,
                        const std::uint32_t index_s,
                        const port::STPoint &st_point) {
  index_t_ = index_t;
  index_s_ = index_s;
  point_ = st_point;
}

void StGraphPoint::SetPrePoint(const StGraphPoint &pre_point) {
  pre_point_ = &pre_point;
}

} // namespace pnd
} // namespace trunk
