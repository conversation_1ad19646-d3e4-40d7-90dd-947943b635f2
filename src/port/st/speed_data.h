// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include "speed_point.h"

namespace trunk {
namespace pnd {
namespace port {

class SpeedData : public SpeedSTPoints {
 public:
  using SpeedSTPoints::SpeedSTPoints;

  SpeedData() = default;

  SpeedData(const SpeedSTPoints& points) : SpeedSTPoints(points) {}

  SpeedData(SpeedSTPoints&& points) : SpeedSTPoints(std::move(points)) {}

  void AppendSpeedSTPoint(double s, double time, double v, double a, double da);

  bool EvaluateByTime(double time, port::SpeedSTPoint* speed_point) const;
  port::SpeedSTPoint EvaluateByTime(double time) const;
  bool EvaluateByS(double s, port::SpeedSTPoint* speed_point) const;
  port::SpeedSTPoint EvaluateByS(double s) const;

  double TotalTime() const;

  size_t LowerBoundIndex(double t);

  void PrependSpeedData(const SpeedData& speed_data);

  // MEMBER_COMPLEX_TYPE(std::vector<std::pair<double, double>>,
  // speed_limit_point);
};

}  // namespace port
}  // namespace pnd
}  // namespace trunk
