// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/config.h>

#include "speed_point.h"

namespace trunk {
namespace pnd {
namespace port {

class STBoundary {
 public:
  enum class BoundaryType {
    UNKNOWN = 0,
    STOP = 1,
    FOLLOW = 2,
    YIELD = 3,
    OVERTAKE = 4,
    KEEP_CLEAR = 5,
  };
  bool IsPointInBoundary(const port::STPoint& st_point) const;

  bool GetIndexRange(const std::vector<port::STPoint>& points, const double t,
                     size_t* left, size_t* right) const;

  bool GetBoundarySRange(const double curr_time, double* s_upper,
                         double* s_lower) const;

  bool GetUnblockBoundarySRange(const double curr_time, double* s_upper,
                                double* s_lower) const;
  static std::string TypeName(const BoundaryType& type);

  MEMBER_BASIC_TYPE(double, min_s, std::numeric_limits<double>::max());
  MEMBER_BASIC_TYPE(double, max_s, std::numeric_limits<double>::lowest());
  MEMBER_BASIC_TYPE(double, min_t, std::numeric_limits<double>::max());
  MEMBER_BASIC_TYPE(double, max_t, std::numeric_limits<double>::lowest());
  MEMBER_BASIC_TYPE(double, s_high_limit, 200.0);
  MEMBER_BASIC_TYPE(BoundaryType, boundary_type, BoundaryType::UNKNOWN);
  MEMBER_COMPLEX_TYPE(STPoints, upper_points);
  MEMBER_COMPLEX_TYPE(STPoints, lower_points);
};

}  // namespace port
}  // namespace pnd
}  // namespace trunk
