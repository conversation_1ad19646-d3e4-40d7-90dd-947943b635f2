// Copyright 2023, trunk Inc. All rights reserved

#include "speed_limit.h"

#include "log.h"

namespace trunk {
namespace pnd {
namespace port {

void SpeedLimit::AppendSpeedLimit(const double s, const double v) {
  if (!speed_limit_points_.empty()) {
    if (s < speed_limit_points().back().first)
      TRUNK_LOG_WARN << "s < final_pt.s";
  }
  speed_limit_points_.emplace_back(s, v);
}

const std::vector<std::pair<double, double>>& SpeedLimit::speed_limit_points()
    const {
  return speed_limit_points_;
}

double SpeedLimit::GetSpeedLimitByS(const double s) const {
  if (speed_limit_points_.size() < 2)
    TRUNK_LOG_WARN << "pts.size < 2";
  if (s < speed_limit_points().front().first)
    TRUNK_LOG_WARN << "s < first_pt.s";

  auto compare_s = [](const std::pair<double, double>& point, const double s) {
    return point.first < s;
  };

  auto it_lower = std::lower_bound(speed_limit_points_.begin(),
                                   speed_limit_points_.end(), s, compare_s);

  if (it_lower == speed_limit_points_.end()) {
    return (it_lower - 1)->second;
  }
  return it_lower->second;
}

double SpeedLimit::MinValidS() const {
  if (speed_limit_points_.size() < 2)
    TRUNK_LOG_WARN << "pts.size < 2";
  return speed_limit_points_.front().first;
}

void SpeedLimit::Clear() { speed_limit_points_.clear(); }

}  // namespace port
}  // namespace pnd
}  // namespace trunk
