// Copyright 2023, trunk Inc. All rights reserved

#include "st_boundary.h"

#include "log.h"

namespace trunk {
namespace pnd {
namespace port {

bool STBoundary::GetIndexRange(const std::vector<port::STPoint>& points,
                               const double t, size_t* left,
                               size_t* right) const {
  // CHECK_NOTNULL(left);
  // CHECK_NOTNULL(right);
  if (t < points.front().t() || t > points.back().t()) {
    // AERROR << "t is out of range. t = " << t;
    return false;
  }
  auto comp = [](const port::STPoint& p, const double t) { return p.t() < t; };
  auto first_ge = std::lower_bound(points.begin(), points.end(), t, comp);
  size_t index = std::distance(points.begin(), first_ge);
  if (index == 0) {
    *left = *right = 0;
  } else if (first_ge == points.end()) {
    *left = *right = points.size() - 1;
  } else {
    *left = index - 1;
    *right = index;
  }
  return true;
}
bool STBoundary::IsPointInBoundary(const port::STPoint& st_point) const {
  if (st_point.t() <= min_t_ || st_point.t() >= max_t_) {
    return false;
  }
  size_t left = 0;
  size_t right = 0;
  if (!GetIndexRange(lower_points_, st_point.t(), &left, &right)) {
    // AERROR << "fait to get index range.";
    return false;
  }
  tport::Point2D point(st_point.s(), st_point.t());
  tport::Point2D upper_start(upper_points_[left].s(), upper_points_[left].t());
  tport::Point2D upper_end(upper_points_[right].s(), upper_points_[right].t());
  tport::Point2D lower_start(lower_points_[left].s(), lower_points_[left].t());
  tport::Point2D lower_end(lower_points_[right].s(), lower_points_[right].t());
  const double check_upper = tutil::CrossProd(point, upper_start, upper_end);
  const double check_lower = tutil::CrossProd(point, lower_start, lower_end);

  return (check_upper * check_lower < 0);
}

// 根据决策信息决定一个障碍物应该输出的上下s界, 用于qp
bool STBoundary::GetUnblockBoundarySRange(const double curr_time,
                                          double* s_upper,
                                          double* s_lower) const {
  // CHECK_NOTNULL(s_upper);
  // CHECK_NOTNULL(s_lower);

  *s_upper = s_high_limit_;
  *s_lower = 0.0;
  if (curr_time < min_t_ || curr_time > max_t_) {
    return true;
  }

  size_t left = 0;
  size_t right = 0;
  if (!GetIndexRange(lower_points_, curr_time, &left, &right)) {
    // TRUNK_LOG_ERROR << "Fail to get index range.";
    return false;
  }
  const double r = (curr_time - upper_points_[left].t()) /
                   std::max(tport::kMathEpsilon, (upper_points_.at(right).t() -
                                                  upper_points_.at(left).t()));

  double upper_cross_s =
      upper_points_[left].s() +
      r * (upper_points_[right].s() - upper_points_[left].s());
  double lower_cross_s =
      lower_points_[left].s() +
      r * (lower_points_[right].s() - lower_points_[left].s());

  if (boundary_type_ == BoundaryType::STOP ||
      boundary_type_ == BoundaryType::YIELD ||
      boundary_type_ == BoundaryType::FOLLOW) {
    *s_upper = lower_cross_s;
  } else if (boundary_type_ == BoundaryType::OVERTAKE) {
    *s_lower = std::fmax(*s_lower, upper_cross_s);
  } else {
    TRUNK_LOG_ERROR << "boundary_type is not supported. boundary_type: "
                    << TypeName(boundary_type_);
    return false;
  }
  return true;
}

// 得到一个障碍物真实的上下s界, 通用信息
bool STBoundary::GetBoundarySRange(const double curr_time, double* s_upper,
                                   double* s_lower) const {
  // CHECK_NOTNULL(s_upper);
  // CHECK_NOTNULL(s_lower);
  if (curr_time < min_t_ || curr_time > max_t_) {
    return false;
  }

  size_t left = 0;
  size_t right = 0;
  if (!GetIndexRange(lower_points_, curr_time, &left, &right)) {
    // AERROR << "Fail to get index range.";
    return false;
  }
  const double r = (curr_time - upper_points_[left].t()) /
                   (upper_points_[right].t() - upper_points_[left].t());

  *s_upper = upper_points_[left].s() +
             r * (upper_points_[right].s() - upper_points_[left].s());
  *s_lower = lower_points_[left].s() +
             r * (lower_points_[right].s() - lower_points_[left].s());

  *s_upper = std::fmin(*s_upper, s_high_limit_);
  *s_lower = std::fmax(*s_lower, 0.0);
  return true;
}

std::string STBoundary::TypeName(const BoundaryType& type) {
  if (type == BoundaryType::UNKNOWN) {
    return "UNKNOWN";
  } else if (type == BoundaryType::STOP) {
    return "STOP";
  } else if (type == BoundaryType::FOLLOW) {
    return "FOLLOW";
  } else if (type == BoundaryType::YIELD) {
    return "YIELD";
  } else if (type == BoundaryType::OVERTAKE) {
    return "OVERTAKE";
  } else if (type == BoundaryType::KEEP_CLEAR) {
    return "KEEP_CLEAR";
  }
  TRUNK_LOG_WARN << "未知type " << static_cast<int>(type)
                 << ", 当作 UNKNOWN 处理";
  return "UNKNOWN";
}

}  // namespace port
}  // namespace pnd
}  // namespace trunk
