// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/config.h>
#include "port/st/speed_point.h"

namespace trunk {
namespace pnd {

class StGraphPoint {
 public:
  void Init(const std::uint32_t index_t, const std::uint32_t index_s,
            const port::STPoint& st_point);

  void SetPrePoint(const StGraphPoint& pre_point);
  MEMBER_BASIC_TYPE(std::uint32_t, index_s, 0);
  MEMBER_BASIC_TYPE(std::uint32_t, index_t, 0);
  // given reference speed profile, reach the cost, including position
  MEMBER_BASIC_TYPE(double, reference_cost, 0.0);
  // given obstacle info, get the cost;
  MEMBER_BASIC_TYPE(double, obstacle_cost, 0.0);
  // total cost
  MEMBER_BASIC_TYPE(double, total_cost,
                    std::numeric_limits<double>::infinity());

  MEMBER_COMPLEX_TYPE(port::STPoint, point);

  const StGraphPoint* pre_point() const;

 private:
  const StGraphPoint* pre_point_ = nullptr;
};

}  // namespace pnd
}  // namespace trunk
