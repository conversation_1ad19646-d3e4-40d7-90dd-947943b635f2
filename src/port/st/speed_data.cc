// Copyright 2023, trunk Inc. All rights reserved

#include "speed_data.h"

#include "port/st/speed_point.h"

namespace trunk {
namespace pnd {
namespace port {

void SpeedData::AppendSpeedSTPoint(const double s, const double time,
                                   const double v, const double a,
                                   const double da) {
  if (!empty()) {
    assert(back().t() < time);
  }
  emplace_back(s, time, v, a, da);
}

port::SpeedSTPoint SpeedData::EvaluateByTime(double time) const {
  port::SpeedSTPoint sd;
  EvaluateByTime(time, &sd);
  return sd;
}

bool SpeedData::EvaluateByTime(const double t,
                               port::SpeedSTPoint* const speed_point) const {
  if (size() < 2) {
    return false;
  }
  if (!(front().t() < t + 1.0e-6 && t - 1.0e-6 < back().t())) {
    return false;
  }

  auto comp = [](const port::SpeedSTPoint& sp, const double t) {
    return sp.t() < t;
  };

  auto it_lower = std::lower_bound(begin(), end(), t, comp);
  if (it_lower == end()) {
    *speed_point = back();
  } else if (it_lower == begin()) {
    *speed_point = front();
  } else {
    const auto& p0 = *(it_lower - 1);
    const auto& p1 = *it_lower;
    double t0 = p0.t();
    double t1 = p1.t();

    SpeedSTPoint res;
    res.set_t(t);

    double s = tutil::Lerp(p0.s(), t0, p1.s(), t1, t);
    res.set_s(s);

    double v = tutil::Lerp(p0.v(), t0, p1.v(), t1, t);
    res.set_v(v);

    double a = tutil::Lerp(p0.a(), t0, p1.a(), t1, t);
    res.set_a(a);

    double da = tutil::Lerp(p0.da(), t0, p1.da(), t1, t);
    res.set_da(da);

    *speed_point = res;
  }
  return true;
}
bool SpeedData::EvaluateByS(const double s,
                            port::SpeedSTPoint* const speed_point) const {
  if (size() < 2) {
    return false;
  }
  if (!(front().s() < s + 1.0e-1 && s - 1.0e-1 < back().s())) {
    return false;
  }

  auto comp = [](const port::SpeedSTPoint& sp, const double s) {
    return sp.s() < s;
  };

  auto it_lower = std::lower_bound(begin(), end(), s, comp);
  if (it_lower == end()) {
    *speed_point = back();
  } else if (it_lower == begin()) {
    *speed_point = front();
  } else {
    const auto& p0 = *(it_lower - 1);
    const auto& p1 = *it_lower;
    double s0 = p0.s();
    double s1 = p1.s();

    SpeedSTPoint res;
    res.set_s(s);

    double t = tutil::Lerp(p0.t(), s0, p1.t(), s1, s);
    res.set_t(t);

    double v = tutil::Lerp(p0.v(), s0, p1.v(), s1, s);
    res.set_v(v);

    double a = tutil::Lerp(p0.a(), s0, p1.a(), s1, s);
    res.set_a(a);

    double da = tutil::Lerp(p0.da(), s0, p1.da(), s1, s);
    res.set_da(da);

    *speed_point = res;
  }
  return true;
}

port::SpeedSTPoint SpeedData::EvaluateByS(double s) const {
  port::SpeedSTPoint sd;
  EvaluateByS(s, &sd);
  return sd;
}
double SpeedData::TotalTime() const {
  if (empty()) {
    return 0.0;
  }
  return back().t() - front().t();
}

size_t SpeedData::LowerBoundIndex(const double t) {
  assert(!empty());
  if (t >= back().t()) {
    return size() - 1;
  }
  auto func = [](const port::SpeedSTPoint& tp, const double time) {
    return tp.t() < time;
  };
  auto it_lower = std::lower_bound(begin(), end(), t, func);
  return std::distance(begin(), it_lower);
}

void SpeedData::PrependSpeedData(const SpeedData& speed_data) {
  insert(begin(), speed_data.begin(), speed_data.end());
}

}  // namespace port
}  // namespace pnd
}  // namespace trunk
