// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <trunk/common/common.h>

namespace trunk {
namespace pnd {
namespace port {

class STPoint {
 public:
  // Constructor returning the zero
  STPoint() = default;

  // Constructor which takes s-t-coordinates.
  explicit STPoint(double s, double t);

  // Getter for s component
  double s() const { return point_.y(); }

  // Getter for t component
  double t() const { return point_.x(); }

  // Setter for s component
  void set_s(const double s) { point_.set_y(s); }

  // Setter for t component
  void set_t(const double t) { point_.set_x(t); }

 private:
  tport::Point2D point_;
};

class SpeedSTPoint : public STPoint {
 public:
  using STPoint::STPoint;

  SpeedSTPoint() = default;
  SpeedSTPoint(double s, double t, double v, double a, double da)
      : STPoint(s, t), v_(v), a_(a), da_(da) {}

  MEMBER_BASIC_TYPE(double, v, 0.0);
  MEMBER_BASIC_TYPE(double, a, 0.0);
  MEMBER_BASIC_TYPE(double, da, 0.0);
};

using STPointPair = std::pair<STPoint, STPoint>;
using STPoints = std::vector<STPoint>;
using SpeedSTPoints = std::vector<SpeedSTPoint>;

}  // namespace port
}  // namespace pnd
}  // namespace trunk
