// Copyright 2023, trunk Inc. All rights reserved

#include "obstacle.h"

#include "log.h"
#include "math/coordinate_transformation/coordiniate_transformation.h"

namespace trunk {
namespace pnd {
namespace port {

namespace {

bool IsLongitudinalDecision(const ObstacleDecision decision) {
  return decision == ObstacleDecision::kIgnore ||
         decision == ObstacleDecision::kStop ||
         decision == ObstacleDecision::kFollow ||
         decision == ObstacleDecision::kYield ||
         decision == ObstacleDecision::kOvertake;
}

bool IsLateralDecision(const ObstacleDecision decision) {
  return decision == ObstacleDecision::kIgnore ||
         decision == ObstacleDecision::kLeftNudge ||
         decision == ObstacleDecision::kRightNudge;
}

}  // namespace

void Obstacle::GetContourBasedOnPredictionPosture(
    const tport::TrajectoryPoint& prediction_postrue,
    tport::Contour2D& prediction_contour) const {
  for (const auto& corner : obj_.xy_contour()) {
    prediction_contour.emplace_back();
    math::CoordinateRotationAroundAnchorResetOrigin(
        corner, obj_.xy_center(), prediction_postrue.theta() - obj_.heading(),
        prediction_postrue, prediction_contour.back());
  }
}

void Obstacle::GetSlBoundaryBasedOnPredictionPosture(
    const tport::Path& ref_line,
    const tport::TrajectoryPoint& prediction_postrue,
    SLBoundary& prediction_sl_boundary) const {
  tport::Contour2D contour;
  GetContourBasedOnPredictionPosture(prediction_postrue, contour);
  for (const auto& pt : contour) {
    const auto sl_point =
        tutil::TransformFrenet::TransformToFrenet(ref_line, pt);
    prediction_sl_boundary.set_max_l(
        std::max(prediction_sl_boundary.max_l(), sl_point.l()));
    prediction_sl_boundary.set_min_l(
        std::min(prediction_sl_boundary.min_l(), sl_point.l()));
    prediction_sl_boundary.set_max_s(
        std::max(prediction_sl_boundary.max_s(), sl_point.s()));
    prediction_sl_boundary.set_min_s(
        std::min(prediction_sl_boundary.min_s(), sl_point.s()));
  }
}

void Obstacle::AddLongitudinalDecision(const std::string& decider_tag,
                                       const ObstacleDecision decision) {
  if (!IsLongitudinalDecision(decision)) {
    TRUNK_LOG_WARN << decider_tag << " is not a longitudinal decision";
    return;
  }
  if (lon_decision_ == ObstacleDecision::kNotSet ||
      lon_decision_ == ObstacleDecision::kIgnore) {
    lon_decision_ = decision;
  } else if (lon_decision_ == ObstacleDecision::kOvertake &&
             decision != ObstacleDecision::kOvertake) {
    lon_decision_ = decision;
  } else if (lon_decision_ == ObstacleDecision::kFollow &&
             (decision == ObstacleDecision::kStop ||
              decision == ObstacleDecision::kYield)) {
    lon_decision_ = decision;
  } else if (lon_decision_ == ObstacleDecision::kYield &&
             decision == ObstacleDecision::kStop) {
    lon_decision_ = decision;
  }
  decider_tags_.push_back(decider_tag);
}

void Obstacle::AddLateralDecision(const std::string& decider_tag,
                                  const ObstacleDecision decision) {
  if (!IsLateralDecision(decision)) {
    TRUNK_LOG_WARN << decider_tag << " is not a lateral decision";
    return;
  }
  if (lat_decision_ == ObstacleDecision::kNotSet ||
      lat_decision_ == ObstacleDecision::kIgnore) {
    lat_decision_ = decision;
  }
  decider_tags_.push_back(decider_tag);
}

std::string Obstacle::TypeName(const ObstacleDecision& type) {
  if (type == ObstacleDecision::kNotSet) {
    return "kNotSet";
  } else if (type == ObstacleDecision::kIgnore) {
    return "kIgnore";
  } else if (type == ObstacleDecision::kStop) {
    return "kStop";
  } else if (type == ObstacleDecision::kFollow) {
    return "kFollow";
  } else if (type == ObstacleDecision::kYield) {
    return "kYield";
  } else if (type == ObstacleDecision::kOvertake) {
    return "kOvertake";
  } else if (type == ObstacleDecision::kKeepClear) {
    return "kKeepClear";
  } else if (type == ObstacleDecision::kLeftNudge) {
    return "kLeftNudge";
  } else if (type == ObstacleDecision::kRightNudge) {
    return "kRightNudge";
  }
  TRUNK_LOG_WARN << "未知type " << static_cast<int>(type)
                 << ", 当作 kNotSet 处理";
  return "kNotSet";
}

}  // namespace port
}  // namespace pnd
}  // namespace trunk
