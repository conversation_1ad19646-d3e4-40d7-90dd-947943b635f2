// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include <string>
#include <unordered_set>
#include <vector>

#include "lane_type.h"
#include "macro.h"
#include "obstacle.h"
#include "sl/path_data.h"
#include "st/speed_data.h"
#include "trunk/common/port/plan_point.h"
#include "tsl/tsl_trajectory.h"

namespace trunk {
namespace pnd {

class SysParam;

namespace port {

class ReferenceLineInfo {
 public:
  // 对应地图的一段section的对应的这条lane的信息
  struct Info {
    Info() = delete;
    Info(int bi, int ei, double s, bool it, bool id, std::string lid,
         std::string sid, HighwayRegionType t, TurnType tt, BoundaryType lt,
         BoundaryType rt, BoundaryColor lc, BoundaryColor rc, std::string ls,
         std::string rs)
        : begin_idx(bi),
          end_idx(ei),
          speed_limit(s),
          is_target(it),
          is_drivable(id),
          lane_id(lid),
          section_id(sid),
          type(t),
          turn_type(tt),
          l_boundary_type(lt),
          r_boundary_type(rt),
          l_boundary_color(lc),
          r_boundary_color(rc),
          l_lane_id(ls),
          r_lane_id(rs) {};
    bool IsAuxiliaryLane() const {
      if (type == port::HighwayRegionType::RAMP_TO_RAMP ||
          type == port::HighwayRegionType::RAMP_TO_TOLL ||
          type == port::HighwayRegionType::RAMP_TO_SERVICE ||
          type == port::HighwayRegionType::TOLL_TO_RAMP ||
          type == port::HighwayRegionType::SERVICE_TO_RAMP) {
        return true;
      }
      return false;
    }
    bool IsProhibitedLane() const {
      return type == port::HighwayRegionType::BRANCH ||
             type == port::HighwayRegionType::EMERGENCY;
    }

    int begin_idx = -1;
    int end_idx = -1;
    double speed_limit = 0.0;
    bool is_target = false;
    bool is_drivable = true;
    std::string lane_id = "";
    std::string section_id = "";
    HighwayRegionType type = HighwayRegionType::INLANE;
    TurnType turn_type = TurnType::KEEP;
    BoundaryType l_boundary_type = BoundaryType::UNKNOWN;
    BoundaryType r_boundary_type = BoundaryType::UNKNOWN;
    BoundaryColor l_boundary_color = BoundaryColor::UNKNOWN;
    BoundaryColor r_boundary_color = BoundaryColor::UNKNOWN;
    std::string l_lane_id = "";
    std::string r_lane_id = "";
  };

  ReferenceLineInfo() = default;

  bool Init(const tport::PredictionObstacles& obstacles,
            const tport::VehiclePose& vehicle,
            const tport::PathPoint& init_point,
            const tport::PathPoint& path_init_point,
            const tport::PathPoint& target_lane_selected_point);
  const Info* GetInfoByS(double s) const;

  void AddCost(double cost) noexcept {
    cost_ += cost;
    total_cost_ += cost;
  }
  void AddTrajectoryCost(double cost) noexcept {
    trajectory_cost_ += cost;
    total_cost_ += cost;
  }

  Obstacle* AddObstacle(const tport::PredictionObstacle& obstacle);

  struct LRWidth {
    LRWidth() = default;
    LRWidth(const double l, const double r) : left_width(l), right_width(r) {}
    double left_width = std::numeric_limits<double>::max();
    double right_width = std::numeric_limits<double>::max();
  };

  bool GetBoundary(double s, const SLBoundaries& boundaries, LRWidth& lr) const;
  const LRWidth GetLaneWidth(double s) const;
  const LRWidth GetRoadWidth(double s) const;
  const LRWidth GetDrivableWidth(double s) const;
  const LRWidth GetEgoRoadWidth(double s) const;
  const LRWidth GetEgoDrivableWidth(double s) const;

  const SLBoundary& AdcSlBoundary() const {
    return sl_boundary_info_.adc_sl_boundary_;
  }

  const SLBoundary& VehicleSlBoundary() const {
    return sl_boundary_info_.vehicle_sl_boundary_;
  }

  bool IsStartFrom(const ReferenceLineInfo& another_line) const;

  bool CombinePathAndSpeedProfile(double relative_time, double start_s);

  template <typename T>
  void SlPoint2PathPoint(const T& sl_point, tport::PathPoint& xy_point) const {
    tport::PathPoint ref_point = EvaluateByS(sl_point.s());
    xy_point = tport::PathPoint(
        tutil::CartesianFrenetConverter::CalculateCartesianPoint(
            ref_point.theta(), ref_point, sl_point.l()));
    xy_point.set_theta(tutil::CartesianFrenetConverter::CalculateTheta(
        ref_point.theta(), ref_point.kappa(), sl_point.l(), sl_point.dl()));

    xy_point.set_kappa(tutil::CartesianFrenetConverter::CalculateKappa(
        ref_point.kappa(), ref_point.dkappa(), sl_point.l(), sl_point.dl(),
        sl_point.ddl()));
    xy_point.set_dkappa(ref_point.dkappa());
    xy_point.set_s(sl_point.s());
    xy_point.set_map_limit_speed(GetSpeedLimitFromS(sl_point.s()));
    xy_point.set_trailer_theta(xy_point.theta());
  }

  template <typename T>
  tport::PathPoint SlPoint2PathPoint(const T& sl_point) const {
    tport::PathPoint xy_point;
    SlPoint2PathPoint(sl_point, xy_point);
    return xy_point;
  }

  void QpSlPath2XyPath(const tport::Trajectory& stitch_path);
  void DpSlPath2XyPath();
  tport::PathPoint EvaluateByS(double s) const;
  tport::SLPoint TransformToFrenet(const tport::Point2D& ref_pt) const;

  double GetSpeedLimitFromS(const double s) const;
  bool IsLaneChangeLane() const;

  port::PathSLPoint CalculateSlPoint(const tport::PathPoint& point) const;

  void AddRunTime(const double t);

  /**
   * @brief: create static virtual object with lane width,
   */
  Obstacle* AddVirtualStaticObstacle(int obstacle_id,
                                     const double obstacle_start_s,
                                     const double obstacle_end_s);
  void ResetLastPointer();

 private:
  using pair_bool_string = std::pair<bool, std::string>;
  using Id2Idx = std::unordered_map<std::string, int>;
  struct {
    /**
     * @brief SL boundary of stitching point (starting point of plan
     * trajectory) relative to the reference line
     */
    SLBoundary adc_sl_boundary_;
    /**
     * @brief SL boundary of vehicle realtime state relative to the reference
     * line
     */
    SLBoundary vehicle_sl_boundary_;
  } sl_boundary_info_;
  std::list<tport::PredictionObstacle> virtual_obstacles_;

  MEMBER_BASIC_TYPE(std::string, id, "");
  MEMBER_BASIC_TYPE(std::string, policy, "lane_change");
  MEMBER_BASIC_TYPE(double, run_time, 0.0);
  MEMBER_BASIC_TYPE(tpnc::TurnSignal, lane_change_flag,
                    tpnc::TurnSignal::NO_TURN);
  MEMBER_BASIC_TYPE(bool, cal_overtake_cost, true);
  MEMBER_BASIC_TYPE(bool, to_auto_drive, false);
  MEMBER_BASIC_TYPE(bool, using_sl_adaptive, false);
  MEMBER_BASIC_TYPE(double, cost, 0.0);
  MEMBER_BASIC_TYPE(double, trajectory_cost, 0.0);
  MEMBER_BASIC_TYPE(double, total_cost, 0.0);
  MEMBER_BASIC_TYPE(int, road_priority, 0);
  MEMBER_BASIC_TYPE(pair_bool_string, is_drivable, std::make_pair(true, ""));
  MEMBER_BASIC_TYPE(bool, overtake_flag, false);
  MEMBER_BASIC_TYPE(bool, speed_pressure, false);
  MEMBER_BASIC_TYPE(double, stop_reference_line_s,
                    std::numeric_limits<double>::max());
  MEMBER_BASIC_TYPE(double, qp_sl_end_s, std::numeric_limits<double>::max());
  MEMBER_BASIC_TYPE(double, ref_l, 0.0);
  MEMBER_BASIC_TYPE(std::string, planning_failure_cause, "--");
  MEMBER_BASIC_TYPE(std::string, drivable_cause, "");
  MEMBER_BASIC_TYPE(uint8_t, nudge_direction, 0);

  MEMBER_COMPLEX_TYPE(std::vector<Info>, infos);
  MEMBER_COMPLEX_TYPE(tport::Path, reference_line);
  MEMBER_COMPLEX_TYPE(tport::Path, origin_reference_line);
  MEMBER_COMPLEX_TYPE(tport::SLPoint, self_sl);
  MEMBER_COMPLEX_TYPE(port::PathSLPoint, sl_init_point);
  MEMBER_COMPLEX_TYPE(port::PathSLPoint, sl_path_init_point);
  MEMBER_COMPLEX_TYPE(port::PathSLPoint, sl_target_point);
  MEMBER_COMPLEX_TYPE(PathData, path_data);
  MEMBER_COMPLEX_TYPE(SpeedData, dp_speed_data);
  MEMBER_COMPLEX_TYPE(SpeedData, speed_data);
  MEMBER_COMPLEX_TYPE(IndexedObstacles, obstacles);
  MEMBER_COMPLEX_TYPE(tport::Trajectory, trajectory);
  MEMBER_COMPLEX_TYPE(tport::Boundaries, lane_boundaries);
  MEMBER_COMPLEX_TYPE(SLBoundaries, sl_lane_boundaries);
  MEMBER_COMPLEX_TYPE(tport::Boundaries, road_boundaries);
  MEMBER_COMPLEX_TYPE(SLBoundaries, sl_road_boundaries);
  MEMBER_COMPLEX_TYPE(tport::Boundaries, drivable_boundaries);
  MEMBER_COMPLEX_TYPE(SLBoundaries, sl_drivable_boundaries);
  MEMBER_COMPLEX_TYPE(tport::Boundaries, ego_road_boundaries);
  MEMBER_COMPLEX_TYPE(SLBoundaries, sl_ego_road_boundaries);
  MEMBER_COMPLEX_TYPE(tport::Boundaries, ego_drivable_boundaries);
  MEMBER_COMPLEX_TYPE(SLBoundaries, sl_ego_drivable_boundaries);
  MEMBER_COMPLEX_TYPE(tport::Boundaries, interact_boundaries);
  MEMBER_COMPLEX_TYPE(Id2Idx, lane_id2info_idx);
  MEMBER_COMPLEX_TYPE(std::vector<double>, task_run_time);
  MEMBER_COMPLEX_TYPE(TslTrajectorys, tsl_trajectorys);
  MEMBER_COMPLEX_TYPE(std::unordered_set<std::string>, flags_set);

  MEMBER_PTR_TYPE(Info*, target_info, nullptr);
  MEMBER_PTR_TYPE(ReferenceLineInfo*, last_reference_line_info, nullptr);
  MEMBER_PTR_TYPE(TslTrajectory*, optimal_tsl_trajctory, nullptr);
};

}  // namespace port
}  // namespace pnd
}  // namespace trunk
