// Copyright 2023, trunk Inc. All rights reserved

#pragma once

#include "macro.h"
#include "reference_line_info.h"

namespace trunk {
namespace pnd {
namespace port {

class TrafficInfo {
 public:
  inline bool IsMerge() const {
    return ramp_turn_info_.second < 1500.0 &&
           (ramp_turn_info_.first == 1 || ramp_turn_info_.first == 2 ||
            ramp_turn_info_.first == 3 || ramp_turn_info_.first == 4 ||
            ramp_turn_info_.first == 5 || ramp_turn_info_.first == 6 ||
            ramp_turn_info_.first == 7 || ramp_turn_info_.first == 8);
  }

  inline bool ToLeft() const {
    return ramp_turn_info_.first == 2 || ramp_turn_info_.first == 4 ||
           ramp_turn_info_.first == 6 || ramp_turn_info_.first == 8;
  }
  inline bool ToRight() const {
    return ramp_turn_info_.first == 1 || ramp_turn_info_.first == 3 ||
           ramp_turn_info_.first == 5 || ramp_turn_info_.first == 7;
  }

 private:
  using int_double = std::pair<int, double>;
  using vec_int_int = std::vector<std::pair<int, int>>;
  MEMBER_BASIC_TYPE(int_double, ramp_turn_info, std::make_pair(0, 10000));
  MEMBER_COMPLEX_TYPE(vec_int_int, traffic_light_status);
};

class Frame {
 public:
  using Id2Idx = std::unordered_map<std::string, int>;
  class Range : public std::vector<tport::Point2D> {  // lateral info
   public:
    Range() = default;
    MEMBER_BASIC_TYPE(std::string, section_id, "");
    MEMBER_BASIC_TYPE(std::string, target_lane_id, "");
    MEMBER_BASIC_TYPE(bool, is_static_map, false)
    MEMBER_COMPLEX_TYPE(Id2Idx, lane_id2line_idx);
    void Init(const tport::VehiclePose vehicle) {
      tutil::TransformUtm::Utm2Vehicle2D(vehicle, *this);
    }
    bool InRange(const tutil::Point& point) const {  // Author: yushuangjiang
      // 环绕数法
      int wn = 0;
      // 当前边的起点在点的下方且终点在点的上方并且如果点在边的左侧，环绕数加1。
      // 当前边的起点在点的上方且终点在点的下方并且如果点在边的右侧，环绕数减1。
      for (int i = 0; i < this->size(); ++i) {
        auto next = (i + 1) % this->size();
        if (this->at(i).x() <= point.x()) {
          if (this->at(next).x() > point.x() &&
              !tutil::PointRightofLine(this->at(i), this->at(next), point)) {
            ++wn;
          }
        } else {
          if (this->at(next).x() <= point.x() &&
              tutil::PointRightofLine(this->at(i), this->at(next), point)) {
            --wn;
          }
        }
      }
      return wn != 0;
    }
  };
  Frame() = default;

  bool Init();
  Range* GetRangeById(const std::string& id);
  ReferenceLineInfo* GetReferenceLineById(const std::string& id);
  ReferenceLineInfo::Info* GetInfoById(const std::string& id);

  const ReferenceLineInfo* FindDriveReferenceLineInfo();

  const tport::TrajectoryPoint& init_point() const;
  const tport::TrajectoryPoint& path_init_point() const;

  bool UseLastFrameTrajectory();
  void ResetLastPointer();

 private:
  bool RebuildInternalLinks();
  bool SetTargetLane();

 private:
  // the reference line info that the vehicle finally choose to drive on
  MEMBER_BASIC_TYPE(double, run_time, 0.0);
  MEMBER_BASIC_TYPE(int, nums_of_trajectories, 0);
  MEMBER_BASIC_TYPE(int, drive_trajectory_idx, -1);
  MEMBER_BASIC_TYPE(double, tnp_speed_limit, 22.222);
  MEMBER_BASIC_TYPE(double, tnp_time_headway, 2.0);
  MEMBER_BASIC_TYPE(tpnc::TurnSignal, turn_signal, tpnc::TurnSignal::NO_TURN);
  MEMBER_BASIC_TYPE(tpnc::TurnSignal, turn_flag, tpnc::TurnSignal::NO_TURN);
  MEMBER_BASIC_TYPE(bool, replan_flag, false);
  MEMBER_BASIC_TYPE(bool, cancel_lane_change_flag, false);
  MEMBER_BASIC_TYPE(std::string, target_lane_id, "");
  MEMBER_BASIC_TYPE(double, nearest_obstacle_distance, -1.0);
  MEMBER_BASIC_TYPE(double, nearest_obstacle_velocity, -1.0);
  MEMBER_BASIC_TYPE(int, nearest_obstacle_id, -1);

  MEMBER_COMPLEX_TYPE(std::vector<ReferenceLineInfo>, reference_lines_info);
  MEMBER_COMPLEX_TYPE(tport::Environment, env);
  MEMBER_COMPLEX_TYPE(TrafficInfo, traffic_info);
  MEMBER_COMPLEX_TYPE(tport::Trajectory, path_stitch_trajectory);
  MEMBER_COMPLEX_TYPE(tport::Trajectory, stitch_trajectory);
  MEMBER_COMPLEX_TYPE(tport::PathPoint, destination_point);
  MEMBER_COMPLEX_TYPE(tport::TrajectoryPoint, target_point);
  MEMBER_COMPLEX_TYPE(std::vector<Range>, ranges);
  MEMBER_COMPLEX_TYPE(Id2Idx, lane_id2range_idx);

  MEMBER_PTR_TYPE(Range*, target_range, nullptr);
  MEMBER_PTR_TYPE(ReferenceLineInfo*, target_reference_line_info, nullptr);
  MEMBER_PTR_TYPE(ReferenceLineInfo*, drive_reference_line_info, nullptr);
  MEMBER_PTR_TYPE(ReferenceLineInfo*, current_reference_line_info, nullptr);
  MEMBER_PTR_TYPE(ReferenceLineInfo*, last_drived_reference_line_info, nullptr);
  MEMBER_PTR_TYPE(ReferenceLineInfo*, last_drived_reference_line_info_mapping,
                  nullptr);
};

}  // namespace port
}  // namespace pnd
}  // namespace trunk
