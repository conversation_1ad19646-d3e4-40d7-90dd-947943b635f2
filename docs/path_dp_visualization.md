# Path DP 可视化功能使用说明

## 概述

本文档介绍如何使用 Path DP（动态规划路径规划）的可视化功能，帮助开发者实时观察撒点过程和最终路径选择。

## 功能特性

### 1. 撒点可视化
- 显示所有采样层级的候选点
- 用蓝色圆点标记采样点
- 展示采样点在 SL 坐标系中的分布

### 2. 最终路径可视化
- 用绿色粗线显示最终选择的路径
- 显示路径的平滑性和连续性
- 标注路径标签便于识别

### 3. 障碍物可视化
- 用红色矩形显示静态障碍物在 SL 坐标系中的边界
- 半透明显示，不遮挡路径信息
- 实时更新障碍物位置

### 4. 车辆边界可视化
- 用绿色虚线显示车辆安全边界
- 显示横向忽略缓冲区和停车缓冲区
- 帮助理解决策逻辑

## 编译配置

### 启用可视化功能

1. 确保系统安装了 Python3 和相关依赖：
```bash
sudo apt-get install python3-dev python3-numpy python3-matplotlib
```

2. 在编译时启用 PLOT 选项：
```bash
mkdir build && cd build
cmake -DPLOT=ON ..
make
```

### 禁用可视化功能

如果不需要可视化功能，可以禁用以减少依赖：
```bash
cmake -DPLOT=OFF ..
make
```

## 使用方法

### 1. 运行时自动显示

当启用 PLOT 选项编译后，系统会在以下时机自动显示可视化：

- **DP 路径规划阶段**：显示采样点、障碍物和最终选择路径
- **路径决策阶段**：显示 DP 结果、障碍物和车辆边界

### 2. 可视化窗口

系统会打开两个可视化窗口：

- **窗口1**：Path DP Sample Points and Obstacles
  - 显示采样点分布
  - 显示障碍物位置
  - 显示最终选择路径

- **窗口2**：Path Decision: DP Result and Obstacles  
  - 显示 DP 路径结果
  - 显示障碍物边界
  - 显示车辆安全边界

### 3. 交互操作

- 可以缩放和平移查看细节
- 图例显示各元素含义
- 坐标轴标注 s(m) 和 l(m)

## 代码集成

### 在自定义任务中使用

```cpp
#ifdef PLOT
#include "plot/plot.h"

// 创建可视化对象
static plot::Plot my_plot(3);
my_plot.initNum(3);

// 绘制采样点
my_plot.plotSamplePoints(sample_points, "blue", "o");

// 绘制选择路径
my_plot.plotSelectedPath(selected_path, "green", "My Path");

// 绘制障碍物
my_plot.plotObstacles(obstacles, "red");

// 显示结果
plt::title("My Custom Visualization");
plt::legend();
my_plot.Show();
#endif
```

### 可用的绘图方法

- `plotSamplePoints()`: 绘制采样点
- `plotSelectedPath()`: 绘制选择路径  
- `plotObstacles()`: 绘制障碍物
- `plotPathWithCost()`: 绘制带成本的路径
- `plotPath()`: 绘制通用路径

## 注意事项

1. **性能影响**：可视化会增加计算开销，建议仅在调试时启用
2. **依赖要求**：需要 Python3、NumPy 和 Matplotlib
3. **线程安全**：可视化代码在主线程中运行，避免并发调用
4. **内存使用**：大量采样点可能占用较多内存

## 故障排除

### 常见问题

1. **编译错误**：检查 Python3 开发包是否安装
2. **运行时错误**：确认 matplotlib 版本兼容（推荐 3.1.2）
3. **显示问题**：检查 X11 转发或显示环境配置

### 调试信息

系统会输出调试日志：
```
Path DP visualization displayed with X sample levels and Y selected points
Path decision visualization displayed with X path points and Y obstacles
```

## 扩展开发

可以通过扩展 `plot::Plot` 类添加更多可视化功能：

1. 添加新的绘图方法
2. 支持更多数据类型
3. 增加交互功能
4. 添加动画效果

详细的 API 文档请参考 `src/plot/plot.h`。
