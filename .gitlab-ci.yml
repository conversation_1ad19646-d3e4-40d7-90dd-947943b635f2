stages:
  - build                           # make编译
  - deploy                          # 部署, 上传deb包到jfrog发布

arm64_build:
  stage: build
  image: *************:8081/docker/trunk/port:nvidia_r32.4.3_arm64
  tags:
    - arm64
  only:
    - master
    - dev
    - tags
    - merge_requests

  artifacts:
      name: "cross_1804_${CI_COMMIT_SHORT_SHA}"
      paths:
        - build/packages/*.deb
      expire_in: '3000'
      when: on_success

  script:
    - apt update
    - apt install -y libeigen3-dev ttl_glog osqpeigen osqp qpoases ompl absl jian
    - apt install -y trunk_common=3.6.12
    - export CMAKE_PREFIX_PATH=$CMAKE_PREFIX_PATH:/opt/trunk_master/
    - cmake -Bbuild . -DCMAKE_BUILD_TYPE=$CI_BUILD_TYPE -DCMAKE_INSTALL_PREFIX=$CI_INSTALL_PREFIX -DJIAN=1
    - cd $CI_PROJECT_DIR/build && make -j$(nproc) && make package

arm64_upload:
  stage: deploy
  image: *************:8081/docker/trunk/port:nvidia_r32.4.3_arm64
  tags:
    - arm64
  only:
    - tags

  needs: ["arm64_build"]
  dependencies:
    - arm64_build

  before_script:
    - cd $CI_PROJECT_DIR/build/packages
    - name=$(sed -e "s/libtrunk_pnd-dev_//;s/_arm64.deb//" <<< $(ls)) # 获取打得deb版本
    - if [ $CI_COMMIT_REF_NAME != $name ]; then exit 1;fi;

  script:
    - cd $CI_PROJECT_DIR/build/packages
    - dir=`basename $CI_PROJECT_DIR`
    - pkg_name=`ls`
    - curl -u$JFROG_USER:$JFROG_PASSWORD "http://*************:8081/artifactory/PNC/arm/trunk_pnd/" -T $pkg_name -f
    - curl -u$JFROG_USER:$JFROG_PASSWORD -XPUT "http://*************:8081/artifactory/infra-ports/pool/main/trunk_pnd/$pkg_name;deb.distribution=xenial;deb.distribution=bionic;deb.distribution=focal;deb.component=main;deb.architecture=arm64" -T $pkg_name -f

amd64_build:
  stage: build
  image: *************:8081/docker-local/trunk/port:16.04_amd64
  tags:
    - amd64
  only:
    - master
    - dev
    - tags
    - merge_requests

  artifacts:
      name: "1604_${CI_COMMIT_SHORT_SHA}"
      paths:
        - build/packages/*.deb
      expire_in: '3000'
      when: on_success

  script:
    - apt update
    - apt install -y libeigen3-dev ttl_glog osqpeigen osqp qpoases ompl absl jian
    - apt install -y trunk_common=3.6.12
    - export CMAKE_PREFIX_PATH=$CMAKE_PREFIX_PATH:/opt/trunk_master/
    - cmake -Bbuild . -DCMAKE_BUILD_TYPE=$CI_BUILD_TYPE -DCMAKE_INSTALL_PREFIX=$CI_INSTALL_PREFIX -DJIAN=1
    - cd $CI_PROJECT_DIR/build && make -j$(nproc) && make package

amd64_upload:
  stage: deploy
  image: *************:8081/docker-local/trunk/port:16.04_amd64
  tags:
    - amd64
  only:
    - tags

  needs: ["amd64_build"]
  dependencies:
    - amd64_build

  before_script:
    - cd $CI_PROJECT_DIR/build/packages
    - name=$(sed -e "s/libtrunk_pnd-dev_//;s/_amd64.deb//" <<< $(ls)) # 获取打得deb版本
    - if [ $CI_COMMIT_REF_NAME != $name ]; then exit 1;fi;

  script:
    - cd $CI_PROJECT_DIR/build/packages
    - dir=`basename $CI_PROJECT_DIR`
    - pkg_name=`ls`
    - curl -u$JFROG_USER:$JFROG_PASSWORD "http://*************:8081/artifactory/PNC/x64/trunk_pnd/" -T $pkg_name -f
    - curl -u$JFROG_USER:$JFROG_PASSWORD -XPUT "http://*************:8081/artifactory/infra/pool/main/trunk_pnd/$pkg_name;deb.distribution=xenial;deb.distribution=bionic;deb.distribution=focal;deb.component=main;deb.architecture=amd64" -T $pkg_name -f
