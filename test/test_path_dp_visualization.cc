// Copyright 2023, trunk Inc. All rights reserved

#include <gtest/gtest.h>
#include <vector>

#ifdef PLOT
#include "plot/plot.h"
#endif

#include "port/sl/path_point.h"
#include "port/obstacle.h"

namespace trunk {
namespace pnd {
namespace test {

class PathDpVisualizationTest : public ::testing::Test {
 protected:
  void SetUp() override {
    // Create sample points for testing
    CreateSamplePoints();
    CreateSelectedPath();
    CreateObstacles();
  }

  void CreateSamplePoints() {
    // Create 5 levels of sample points
    sample_points_.resize(5);
    
    for (int level = 0; level < 5; ++level) {
      double s = level * 20.0;  // 20m intervals
      
      // Create 7 lateral samples per level
      for (int i = -3; i <= 3; ++i) {
        double l = i * 1.0;  // 1m lateral spacing
        sample_points_[level].emplace_back(s, l);
      }
    }
  }

  void CreateSelectedPath() {
    // Create a smooth selected path
    for (int i = 0; i < 5; ++i) {
      double s = i * 20.0;
      double l = 0.5 * sin(s * 0.1);  // Slight sinusoidal path
      selected_path_.emplace_back(s, l);
    }
  }

  void CreateObstacles() {
    // Create mock obstacles for testing
    // Note: This is a simplified test - in real usage, obstacles come from perception
    
    // Create a static obstacle
    tport::PredictionObstacle obs1;
    obs1.set_id(1);
    obs1.set_type(static_cast<int>(port::ObjectType::CAR));
    
    auto* obstacle1 = obstacles_.Add(1, port::Obstacle(obs1));
    
    // Set SL boundary for obstacle
    port::SLBoundary sl_boundary1;
    sl_boundary1.set_min_s(30.0);
    sl_boundary1.set_max_s(35.0);
    sl_boundary1.set_min_l(-2.0);
    sl_boundary1.set_max_l(2.0);
    obstacle1->set_sl_boundary(sl_boundary1);
  }

  std::vector<std::vector<tport::SLPoint>> sample_points_;
  std::vector<tport::SLPoint> selected_path_;
  port::IndexedObstacles obstacles_;
};

TEST_F(PathDpVisualizationTest, TestSamplePointsVisualization) {
#ifdef PLOT
  plot::Plot test_plot(10);
  test_plot.initNum(10);
  
  // Test sample points plotting
  test_plot.plotSamplePoints(sample_points_, "blue", "o");
  
  plt::title("Test: Sample Points Visualization");
  plt::xlabel("s (m)");
  plt::ylabel("l (m)");
  plt::grid(true);
  
  // This test passes if no exception is thrown
  EXPECT_NO_THROW(test_plot.Show());
  
  std::cout << "Sample points visualization test completed. "
            << "Check the plot window for visual verification." << std::endl;
#else
  GTEST_SKIP() << "PLOT not enabled, skipping visualization test";
#endif
}

TEST_F(PathDpVisualizationTest, TestSelectedPathVisualization) {
#ifdef PLOT
  plot::Plot test_plot(11);
  test_plot.initNum(11);
  
  // Test selected path plotting
  test_plot.plotSelectedPath(selected_path_, "green", "Test Path");
  
  plt::title("Test: Selected Path Visualization");
  plt::xlabel("s (m)");
  plt::ylabel("l (m)");
  plt::grid(true);
  plt::legend();
  
  EXPECT_NO_THROW(test_plot.Show());
  
  std::cout << "Selected path visualization test completed. "
            << "Check the plot window for visual verification." << std::endl;
#else
  GTEST_SKIP() << "PLOT not enabled, skipping visualization test";
#endif
}

TEST_F(PathDpVisualizationTest, TestObstacleVisualization) {
#ifdef PLOT
  plot::Plot test_plot(12);
  test_plot.initNum(12);
  
  // Test obstacle plotting
  test_plot.plotObstacles(obstacles_, "red");
  
  plt::title("Test: Obstacle Visualization");
  plt::xlabel("s (m)");
  plt::ylabel("l (m)");
  plt::grid(true);
  
  EXPECT_NO_THROW(test_plot.Show());
  
  std::cout << "Obstacle visualization test completed. "
            << "Check the plot window for visual verification." << std::endl;
#else
  GTEST_SKIP() << "PLOT not enabled, skipping visualization test";
#endif
}

TEST_F(PathDpVisualizationTest, TestCompleteVisualization) {
#ifdef PLOT
  plot::Plot test_plot(13);
  test_plot.initNum(13);
  
  // Test complete visualization with all elements
  test_plot.plotSamplePoints(sample_points_, "blue", "o");
  test_plot.plotSelectedPath(selected_path_, "green", "Selected Path");
  test_plot.plotObstacles(obstacles_, "red");
  
  plt::title("Test: Complete Path DP Visualization");
  plt::xlabel("s (m)");
  plt::ylabel("l (m)");
  plt::grid(true);
  plt::legend();
  
  EXPECT_NO_THROW(test_plot.Show());
  
  std::cout << "Complete visualization test completed. "
            << "This simulates the actual path DP visualization." << std::endl;
  std::cout << "Sample points: " << sample_points_.size() << " levels" << std::endl;
  std::cout << "Selected path: " << selected_path_.size() << " points" << std::endl;
  std::cout << "Obstacles: " << obstacles_.Items().size() << " items" << std::endl;
#else
  GTEST_SKIP() << "PLOT not enabled, skipping visualization test";
#endif
}

TEST_F(PathDpVisualizationTest, TestVisualizationWithoutPlot) {
  // Test that code works correctly when PLOT is disabled
  
  // These calls should not crash when PLOT is disabled
#ifdef PLOT
  plot::Plot test_plot(14);
  test_plot.plotSamplePoints(sample_points_, "blue", "o");
  test_plot.plotSelectedPath(selected_path_, "green", "Test Path");
  test_plot.plotObstacles(obstacles_, "red");
  test_plot.Show();
#endif
  
  // Test always passes - we're just checking for no crashes
  EXPECT_TRUE(true);
  
  std::cout << "No-plot test completed successfully." << std::endl;
}

}  // namespace test
}  // namespace pnd
}  // namespace trunk
