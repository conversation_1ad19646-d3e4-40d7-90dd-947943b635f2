#!/bin/bash

# Path DP Visualization Test Script
# This script helps test the path DP visualization functionality

set -e

echo "=== Path DP Visualization Test Script ==="
echo

# Check if we're in the right directory
if [ ! -f "CMakeLists.txt" ]; then
    echo "Error: Please run this script from the project root directory"
    exit 1
fi

# Function to check dependencies
check_dependencies() {
    echo "Checking dependencies..."
    
    # Check Python3
    if ! command -v python3 &> /dev/null; then
        echo "Error: Python3 is not installed"
        echo "Please install: sudo apt-get install python3-dev"
        exit 1
    fi
    
    # Check Python3 development headers
    if ! python3 -c "import distutils.sysconfig" &> /dev/null; then
        echo "Warning: Python3 development headers may not be installed"
        echo "Consider installing: sudo apt-get install python3-dev"
    fi
    
    # Check NumPy
    if ! python3 -c "import numpy" &> /dev/null; then
        echo "Error: NumPy is not installed"
        echo "Please install: sudo apt-get install python3-numpy"
        exit 1
    fi
    
    # Check Matplotlib
    if ! python3 -c "import matplotlib" &> /dev/null; then
        echo "Error: Matplotlib is not installed"
        echo "Please install: sudo apt-get install python3-matplotlib"
        exit 1
    fi
    
    echo "✓ All dependencies are available"
    echo
}

# Function to build with visualization enabled
build_with_plot() {
    echo "Building with PLOT=ON..."
    
    # Create build directory
    mkdir -p build_plot
    cd build_plot
    
    # Configure with PLOT enabled
    cmake -DPLOT=ON -DCMAKE_BUILD_TYPE=Debug ..
    
    # Build
    make -j$(nproc)
    
    cd ..
    echo "✓ Build completed with visualization enabled"
    echo
}

# Function to build without visualization
build_without_plot() {
    echo "Building with PLOT=OFF..."
    
    # Create build directory
    mkdir -p build_no_plot
    cd build_no_plot
    
    # Configure with PLOT disabled
    cmake -DPLOT=OFF -DCMAKE_BUILD_TYPE=Debug ..
    
    # Build
    make -j$(nproc)
    
    cd ..
    echo "✓ Build completed without visualization"
    echo
}

# Function to run tests
run_tests() {
    echo "Running visualization tests..."
    
    if [ -f "build_plot/test/test_path_dp_visualization" ]; then
        echo "Running tests with visualization enabled..."
        cd build_plot
        ./test/test_path_dp_visualization
        cd ..
        echo "✓ Visualization tests completed"
    else
        echo "Warning: Test executable not found"
    fi
    
    echo
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [options]"
    echo
    echo "Options:"
    echo "  --check-deps    Check dependencies only"
    echo "  --build-plot    Build with visualization enabled"
    echo "  --build-no-plot Build without visualization"
    echo "  --test          Run visualization tests"
    echo "  --all           Run all steps (default)"
    echo "  --help          Show this help"
    echo
}

# Parse command line arguments
case "${1:-all}" in
    --check-deps)
        check_dependencies
        ;;
    --build-plot)
        check_dependencies
        build_with_plot
        ;;
    --build-no-plot)
        build_without_plot
        ;;
    --test)
        run_tests
        ;;
    --all)
        check_dependencies
        build_with_plot
        build_without_plot
        run_tests
        echo "=== All tests completed ==="
        echo
        echo "To use visualization in your code:"
        echo "1. Build with: cmake -DPLOT=ON .."
        echo "2. The visualization will automatically show during path planning"
        echo "3. Check the documentation: docs/path_dp_visualization.md"
        ;;
    --help)
        show_usage
        ;;
    *)
        echo "Unknown option: $1"
        show_usage
        exit 1
        ;;
esac

echo "Done."
